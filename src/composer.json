{"name": "lf/cantine", "description": "Cantine La Famille", "type": "project", "license": ["OSL-3.0", "AFL-3.0"], "config": {"preferred-install": "dist", "sort-packages": true, "allow-plugins": {"cweagans/composer-patches": true, "dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/composer-dependency-version-audit-plugin": true, "magento/composer-root-update-plugin": true, "magento/inventory-composer-installer": true, "magento/magento-composer-installer": true, "php-http/discovery": true}}, "version": "1.3.6", "require": {"ext-openssl": "*", "adin/edenred": "~1.1", "ced/module-wallet": "1.1.6", "cweagans/composer-patches": "^1.6", "degdigital/magento2-customreports": "^2.0", "dialoginsight/di": "^2.0", "giggsey/libphonenumber-for-php": "^8.10", "iwd/opc": "dev-master", "lyranetwork/module-systempay": "^2.10", "magefan/module-google-tag-manager": "^2.2", "magento/composer-root-update-plugin": "^2.0.4", "magento/product-community-edition": "2.4.7-p3", "mageplaza/magento-2-french-language-pack": "dev-master", "mageplaza/module-smtp": "^4.7.4", "magestore/bannerslider-magento2": "^1.7.1", "mike42/escpos-php": "^v4.0", "mpdf/mpdf": "^8.0", "outeredge/magento-multiplenewsletter-module": "^1.1", "setasign/fpdf": "^1.8", "smile/module-gift-sales-rule": "^1.1"}, "require-dev": {"allure-framework/allure-phpunit": "^2", "dealerdirect/phpcodesniffer-composer-installer": "^0.7 || ^1.0", "dg/bypass-finals": "^1.4", "friendsofphp/php-cs-fixer": "^3.22", "lusitanian/oauth": "^0.8", "pdepend/pdepend": "^2.10", "phpstan/phpstan": "^1.9", "sebastian/phpcpd": "^6.0", "doctrine/annotations": "^1.13.2", "fakerphp/faker": "^1.23", "mage2tv/magento-cache-clean": "^1.0", "magento/magento-coding-standard": "*", "magento/magento2-functional-testing-framework": "^4.7", "phpunit/phpunit": "^9.5", "symfony/finder": "^6.4"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\Setup\\": "setup/src/Magento/Setup/"}}, "conflict": {"gene/bluefoot": "*"}, "minimum-stability": "stable", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://repo.magento.com/"}, {"type": "git", "url": "**************:cvalcke/lf_iwd_opc.git"}, {"type": "package", "package": {"name": "magestore/bannerslider-magento2", "version": "1.7.1", "source": {"url": "**************:cvalcke/Bannerslider-Magento2.git", "type": "git", "reference": "master"}}}, {"type": "git", "url": "**************:cvalcke/lf_lyranetwork_systempay.git"}, {"type": "git", "url": "**************:cvalcke/lf_ced_wallet.git"}, {"type": "git", "url": "**************:cvalcke/adin_edenred.git"}], "scripts": {"post-install-cmd": ["([ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/phpcs --config-set installed_paths ../../magento/magento-coding-standard/,../../phpcompatibility/php-compatibility)"], "post-update-cmd": ["([ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/phpcs --config-set installed_paths ../../magento/magento-coding-standard/,../../phpcompatibility/php-compatibility)"]}, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "patches": {"lyranetwork/module-systempay": {"Fix onepage rest token check": "patches/composer/systempay-onepage.diff"}, "magento/module-sales-sequence": {"Fix probleme de contrainte plusieurs franchises": "patches/composer/franchiseconstraint.diff"}, "magento/module-sales": {"Fix grid filter problem": "patches/composer/M36719-OrderGridFilter.diff"}, "magento/module-customer": {"Fix grid filter problem": "patches/composer/M36719-CustomerGrid.diff"}, "magento/module-checkout": {"Fix edenred payment": "patches/composer/edenred-payment.diff"}, "magento/magento2-base": {"Fix datepicker position": "patches/composer/j<PERSON>y-ui-datepicker.diff"}, "magento/framework": {"Fix joinprocessor cache": "patches/composer/M27504-joinprocessor.diff"}, "magento/module-customer-graph-ql": {"Fix type error": "patches/composer/M38662-graphql-type.diff"}, "smile/module-gift-sales-rule": {"Fix missing extension attribute for sales cart rules": "patches/composer/M1284-RuleRepositoryPlugin.patch", "Fix method signature for Magento 2.4.7": "patches/composer/smile-gift-rule.diff"}, "iwd/opc": {"Fix cantine magento dependencies": "patches/composer/iwd-cantine-dependency.diff"}, "magestore/bannerslider-magento2": {"Fix deprecated dynamic property creation in PHP 8.2": "patches/composer/magestore-bannerslider-property.diff"}}}}
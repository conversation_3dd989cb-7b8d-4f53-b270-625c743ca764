<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <carriers>
            <eatdelivery>
                <active>1</active>
                <title>Livraison</title>
                <name>Livraison EatLf</name>
                <shipping_cost>0</shipping_cost>
                <sallowspecific>0</sallowspecific>
                <sort_order>15</sort_order>
                <model>EatLf\Carrier\Model\Delivery</model>
            </eatdelivery>
        </carriers>
    </default>
</config>

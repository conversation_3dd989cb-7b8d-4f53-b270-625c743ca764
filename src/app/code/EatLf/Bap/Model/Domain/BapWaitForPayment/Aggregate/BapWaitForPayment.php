<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapWaitForPayment\Aggregate;

use EatLf\Bap\Api\Data\BapStatusInterface;
use EatLf\Bap\Model\Domain\BapWaitForPayment\Aggregate\Exception\InvalidStatusException;

class BapWaitForPayment
{
    private string $status;

    private int $bapId;

    public function __construct(int $bapId, string $status)
    {
        $this->status = $status;
        $this->bapId = $bapId;
    }

    /**
     * @throws \EatLf\Bap\Model\Domain\BapWaitForPayment\Aggregate\Exception\InvalidStatusException
     */
    public function waitForPayment(): void
    {
        if ($this->status !== BapStatusInterface::STATUS_CREATED) {
            throw new InvalidStatusException('Bapid: ' . $this->bapId . ' - Current status: ' . $this->status);
        }

        $this->status = BapStatusInterface::STATUS_WAITING_FOR_PAYMENT;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getBapId(): int
    {
        return $this->bapId;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapWaitForPayment\Repository;

use EatLf\Bap\Model\Domain\BapWaitForPayment\Aggregate\BapWaitForPayment;
use EatLf\Bap\Model\Entity\Query\GetBapById;
use EatLf\Bap\Model\Entity\ResourceModel\Bap;

class BapWaitForPaymentRepository
{
    private GetBapById $getBapById;

    private Bap $bapResource;

    public function __construct(
        GetBapById $getBapById,
        Bap $bapResource
    ) {
        $this->getBapById = $getBapById;
        $this->bapResource = $bapResource;
    }

    public function getForBap(int $bapId): BapWaitForPayment
    {
        $bap = $this->getBapById->execute($bapId);

        return new BapWaitForPayment($bapId, $bap->getStatus());
    }

    public function save(BapWaitForPayment $bapStatusWorkflow): void
    {
        $bapId = $bapStatusWorkflow->getBapId();

        $bapStatus = $bapStatusWorkflow->getStatus();

        $bap = $this->getBapById->execute($bapId);

        $bap->setStatus($bapStatus);

        $this->bapResource->save($bap);
    }
}

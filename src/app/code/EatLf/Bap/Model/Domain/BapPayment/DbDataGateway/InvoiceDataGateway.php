<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapPayment\DbDataGateway;

use EatLf\Bap\Api\Data\BapInvoiceInterface;
use EatLf\Bap\Model\Domain\BapPayment\InvoicePaymentNotifier\InvoiceGatewayInterface;
use EatLf\Bap\Model\Entity\ResourceModel\BapInvoice\CollectionFactory;

class InvoiceDataGateway implements InvoiceGatewayInterface
{
    private CollectionFactory $collectionFactory;

    public function __construct(CollectionFactory $collectionFactory)
    {
        $this->collectionFactory = $collectionFactory;
    }

    public function getInvoiceIdsForBaps(array $bapIds): array
    {
        $collection = $this->collectionFactory->create();

        $collection->addFieldToFilter(BapInvoiceInterface::BAP_ID_FIELD, ['in' => $bapIds]);

        $invoicePaymentsList = [];

        /** @var BapInvoiceInterface $bapInvoice */
        foreach ($collection->getItems() as $bapInvoice) {
            $invoicePaymentsList[] = $bapInvoice->getIncrementId();
        }

        return $invoicePaymentsList;
    }
}

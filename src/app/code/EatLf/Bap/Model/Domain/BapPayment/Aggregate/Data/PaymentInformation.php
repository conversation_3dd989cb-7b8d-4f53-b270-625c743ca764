<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapPayment\Aggregate\Data;

class PaymentInformation
{
    private string $modeReglement;

    private \DateTimeInterface $dateReglement;

    private ?string $numeroRemise;

    public function __construct(string $modeReglement, \DateTimeInterface $dateReglement, ?string $numeroRemise)
    {
        $this->modeReglement = $modeReglement;
        $this->dateReglement = $dateReglement;
        $this->numeroRemise = $numeroRemise;
    }

    public function getModeReglement(): string
    {
        return $this->modeReglement;
    }

    public function getDateReglement(): \DateTimeInterface
    {
        return $this->dateReglement;
    }

    public function getNumeroRemise(): ?string
    {
        return $this->numeroRemise;
    }
}

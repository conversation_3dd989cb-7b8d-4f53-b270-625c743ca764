<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapPayment\Aggregate;

use EatLf\Bap\Api\Data\BapPaymentMethodInterface;
use EatLf\Bap\Api\Data\BapStatusInterface;
use EatLf\Bap\Model\Domain\BapPayment\Aggregate\Data\PaymentInformation;
use EatLf\Bap\Model\Domain\BapPayment\Aggregate\Exception\BapInvalidPaymentMethod;
use EatLf\Bap\Model\Domain\BapPayment\Aggregate\Exception\BapNotWaitingForPaymentException;

/**
 * This aggregate is responsible for the payment of "bon à payer" (bap) documents.
 *
 * The payment operation for a bap document is only allowed if the current status is "waiting for payment".
 * In all other cases an exception is thrown and payment is refused.
 *
 * One of the allowed payment methods must be used otherwise the payment is refused with a thrown exception.
 *
 * If payment is valid then the payment information is saved for the relevant bap document.
 */
class BapPayment
{
    private BapDataInterface $bapData;

    public function __construct(BapDataInterface $bapData)
    {
        $this->bapData = $bapData;
    }

    public function getBapData(): BapDataInterface
    {
        return $this->bapData;
    }

    /**
     * @throws \EatLf\Bap\Model\Domain\BapPayment\Aggregate\Exception\BapPaymentException
     */
    public function pay(PaymentInformation $paymentInformation): void
    {
        $this->validateBapIsWaitingForPayment();

        $this->validatePaymentMethodIsAllowed($paymentInformation);

        $this->setBapPaymentInformation($paymentInformation);
    }

    /**
     * @throws \EatLf\Bap\Model\Domain\BapPayment\Aggregate\Exception\BapNotWaitingForPaymentException
     */
    private function validateBapIsWaitingForPayment(): void
    {
        if ($this->bapData->getStatus() !== BapStatusInterface::STATUS_WAITING_FOR_PAYMENT) {
            throw new BapNotWaitingForPaymentException(
                'Bapid: ' . $this->bapData->getBapId() . ' - Status: ' . $this->bapData->getStatus()
            );
        }
    }

    /**
     * @throws \EatLf\Bap\Model\Domain\BapPayment\Aggregate\Exception\BapInvalidPaymentMethod
     */
    private function validatePaymentMethodIsAllowed(PaymentInformation $paymentInformation): void
    {
        $paymentMethod = $paymentInformation->getModeReglement();

        if (!in_array($paymentMethod, BapPaymentMethodInterface::BAP_PAYMENT_METHODS)) {
            throw new BapInvalidPaymentMethod(
                'Bapid: ' . $this->bapData->getBapId() . ' Payment method: ' . $paymentMethod
            );
        }
    }

    private function setBapPaymentInformation(PaymentInformation $paymentInformation): void
    {
        $this->setPaymentDate($paymentInformation);

        $this->setPaymentMethod($paymentInformation);

        $this->setPaymentNumberIfApplicable($paymentInformation);

        $this->setStatusToPaid();
    }

    private function setPaymentDate(PaymentInformation $paymentInformation): void
    {
        $this->bapData->setPaymentDate($paymentInformation->getDateReglement());
    }

    private function setPaymentMethod(PaymentInformation $paymentInformation): void
    {
        $this->bapData->setPaymentMethod($paymentInformation->getModeReglement());
    }

    private function setPaymentNumberIfApplicable(PaymentInformation $paymentInformation): void
    {
        if ($paymentInformation->getNumeroRemise() !== null) {
            $this->bapData->setPaymentNumber($paymentInformation->getNumeroRemise());
        }
    }

    private function setStatusToPaid(): void
    {
        $this->bapData->setStatus(BapStatusInterface::STATUS_PAID);
    }
}

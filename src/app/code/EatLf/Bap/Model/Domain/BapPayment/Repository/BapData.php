<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapPayment\Repository;

use EatLf\Bap\Api\Data\BapInterface;
use EatLf\Bap\Model\Domain\BapPayment\Aggregate\BapDataInterface;
use EatLf\Bap\Model\Entity\Bap;

class BapData implements BapDataInterface
{
    private Bap $bap;

    public function __construct(BapInterface $bap)
    {
        $this->bap = $bap;
    }

    public function getBap(): Bap
    {
        return $this->bap;
    }

    public function getStatus(): string
    {
        return $this->bap->getStatus();
    }

    public function setStatus(string $status): void
    {
        $this->bap->setStatus($status);
    }

    public function setPaymentMethod(string $paymentMethod): void
    {
        $this->bap->setPaymentMethod($paymentMethod);
    }

    public function setPaymentDate(\DateTimeInterface $paymentDate): void
    {
        $this->bap->setPaymentDate($paymentDate);
    }

    public function setPaymentNumber(string $paymentNumber): void
    {
        $this->bap->setPaymentNumber($paymentNumber);
    }

    public function getBapId(): int
    {
        return $this->bap->getBapId();
    }
}

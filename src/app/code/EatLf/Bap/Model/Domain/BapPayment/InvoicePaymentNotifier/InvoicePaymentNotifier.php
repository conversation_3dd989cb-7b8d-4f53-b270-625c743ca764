<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapPayment\InvoicePaymentNotifier;

use EatLf\Bap\Model\Domain\BapPayment\Aggregate\Data\PaymentInformation;
use Lf\Sales\Model\Data\InvoicePayment;
use Magento\Framework\MessageQueue\PublisherInterface;

class InvoicePaymentNotifier
{
    private InvoiceGatewayInterface $invoiceGateway;

    private PublisherInterface $publisher;

    public function __construct(
        InvoiceGatewayInterface $invoiceGateway,
        PublisherInterface $publisher
    ) {
        $this->invoiceGateway = $invoiceGateway;
        $this->publisher = $publisher;
    }

    public function notifyPaymentForInvoices(array $bapIds, PaymentInformation $paymentInformation): void
    {
        $invoiceIdsList = $this->invoiceGateway->getInvoiceIdsForBaps($bapIds);

        $this->notifyInvoiceList($invoiceIdsList, $paymentInformation);
    }

    private function notifyInvoiceList(array $invoiceIdsList, PaymentInformation $paymentInformation): void
    {
        // Generate a unique payment id to link all the invoices to the same payment.
        $paymentId = $this->createPaymentId();

        foreach ($invoiceIdsList as $invoiceId) {
            $invoicePayment = $this->createInvoicePayment($invoiceId, $paymentInformation, $paymentId);

            $this->notifyInvoice($invoicePayment);
        }
    }

    private function createPaymentId(): string
    {
        // uniqid generates a unique value base on the timestamp which is sufficient for our use case.
        return uniqid();
    }

    private function createInvoicePayment(
        $invoiceId,
        PaymentInformation $paymentInformation,
        string $paymentId
    ): InvoicePayment {
        $invoicePayment = new InvoicePayment();

        $invoicePayment->setInvoiceIncrementId($invoiceId);
        $invoicePayment->setPaymentMethod($paymentInformation->getModeReglement());
        $invoicePayment->setPaymentDate($paymentInformation->getDateReglement()->format('Y-m-d'));
        $invoicePayment->setPaymentReference($paymentInformation->getNumeroRemise());
        $invoicePayment->setPaymentId($paymentId);

        return $invoicePayment;
    }

    private function notifyInvoice(InvoicePayment $invoicePayment): void
    {
        $this->publisher->publish('invoice.paid', $invoicePayment);
    }
}

<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapInvoiceCancel\DbDataGateway;

use EatLf\Bap\Model\Domain\BapInvoiceCancel\Service\BapGatewayInterface;
use EatLf\Bap\Model\Entity\Command\LockBap;
use EatLf\Bap\Model\Entity\Query\GetBapInvoiceByIncrementId;

class ServiceGateway implements BapGatewayInterface
{
    private LockBap $lockBap;

    private GetBapInvoiceByIncrementId $getBapInvoiceByIncrementId;

    public function __construct(
        LockBap $lockBap,
        GetBapInvoiceByIncrementId $getBapInvoiceByIncrementId
    ) {
        $this->lockBap = $lockBap;
        $this->getBapInvoiceByIncrementId = $getBapInvoiceByIncrementId;
    }

    public function lockBapByInvoiceId(string $incrementId, callable $callable, array $args = []): mixed
    {
        $invoice = $this->getBapInvoiceByIncrementId->execute($incrementId);

        $bapId = $invoice->getBapId();

        return $this->lockBap->execute($bapId, $callable, $args);
    }
}

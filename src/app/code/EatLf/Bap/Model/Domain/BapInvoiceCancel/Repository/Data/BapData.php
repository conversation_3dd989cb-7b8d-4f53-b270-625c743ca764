<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapInvoiceCancel\Repository\Data;

use EatLf\Bap\Model\Domain\BapInvoiceCancel\Aggregate\Data\AmountsInterface;
use EatLf\Bap\Model\Domain\BapInvoiceCancel\Aggregate\Data\BapDataInterface;
use EatLf\Bap\Model\Domain\BapInvoiceCancel\Aggregate\Data\CustomerDataInterface;
use EatLf\Bap\Model\Domain\BapInvoiceCancel\Aggregate\Data\DayDataInterface;
use EatLf\Bap\Model\Entity\Bap;
use EatLf\Bap\Model\Entity\BapCustomer;
use EatLf\Bap\Model\Entity\BapDay;
use EatLf\Bap\Model\Entity\BapInvoice;

class BapData implements BapDataInterface
{
    public Bap $bap;

    public BapCustomer $bapCustomer;

    public BapDay $bapDay;

    public BapInvoice $bapInvoice;

    private Customer $customerData;

    private Day $dayData;

    private \EatLf\Bap\Model\Domain\BapInvoiceCancel\Repository\Data\Bap $bapData;

    public function __construct(
        Bap $bap,
        BapCustomer $bapCustomer,
        BapDay $bapDay,
        BapInvoice $bapInvoice
    ) {
        $this->bap = $bap;
        $this->bapCustomer = $bapCustomer;
        $this->bapDay = $bapDay;
        $this->bapInvoice = $bapInvoice;

        $this->customerData = new Customer($bapCustomer);
        $this->dayData = new Day($bapDay);
        $this->bapData = new \EatLf\Bap\Model\Domain\BapInvoiceCancel\Repository\Data\Bap($bap);
    }

    public function getCustomerData(): CustomerDataInterface
    {
        return $this->customerData;
    }

    public function getDayData(): DayDataInterface
    {
        return $this->dayData;
    }

    public function getBapAmounts(): AmountsInterface
    {
        return $this->bapData;
    }

    public function getStatus(): string
    {
        return $this->bap->getStatus();
    }
}

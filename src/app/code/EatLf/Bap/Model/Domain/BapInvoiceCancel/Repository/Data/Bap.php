<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapInvoiceCancel\Repository\Data;

use EatLf\Bap\Model\Domain\BapInvoiceCancel\Aggregate\Data\AmountsInterface;

class Bap implements AmountsInterface
{
    private \EatLf\Bap\Model\Entity\Bap $bap;

    public function __construct(\EatLf\Bap\Model\Entity\Bap $bap)
    {
        $this->bap = $bap;
    }

    public function getOrdersTotalInclTax(): float
    {
        return $this->bap->getOrdersTotalInclTax();
    }

    public function getTotalInclTax(): float
    {
        return $this->bap->getTotalInclTax();
    }

    public function getTotalExclTax(): float
    {
        return $this->bap->getTotalExclTax();
    }

    public function getTaxDetails(): array
    {
        return $this->bap->getTaxDetails();
    }

    public function setOrdersTotalInclTax(float $ordersTotalInclTax): void
    {
        $this->bap->setOrdersTotalInclTax($ordersTotalInclTax);
    }

    public function setTotalInclTax(float $totalInclTax): void
    {
        $this->bap->setTotalInclTax($totalInclTax);
    }

    public function setTotalExclTax(float $totalExclTax): void
    {
        $this->bap->setTotalExclTax($totalExclTax);
    }

    public function setTaxDetails(array $taxDetails): void
    {
        $this->bap->setTaxDetails($taxDetails);
    }

    public function getTaxBase(): array
    {
        return $this->bap->getTaxBase();
    }

    public function setTaxBase(array $taxBase): void
    {
        $this->bap->setTaxBase($taxBase);
    }
}

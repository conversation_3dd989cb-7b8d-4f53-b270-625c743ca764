<?php

namespace EatLf\Bap\Model\Domain\BapInvoiceCancel\Aggregate\Data;

interface InvoiceDataInterface
{
    public function getBapId(): int;

    public function getShippingDate(): string;

    public function getInvoiceIncrementId(): string;

    public function getOrderTotalInclTax(): float;

    public function getTotalInclTax(): float;

    public function getTotalExclTax(): float;

    public function getTaxDetails(): array;

    public function getTaxBase(): array;

    public function getCustomerEmail(): string;
}

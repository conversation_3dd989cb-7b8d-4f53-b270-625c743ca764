<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate;

use EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Data\DayDataInterface;
use EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Data\InvoiceDataInterface;
use EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Exception\MultipleOrdersSameDateException;
use InvalidArgumentException;

class ProjectionDay
{
    private DayDataInterface $day;

    public function __construct(DayDataInterface $dayData)
    {
        $this->day = $dayData;
    }

    public function addInvoice(InvoiceDataInterface $invoice): void
    {
        $this->validateDate($invoice->getShippingDate());

        $this->addEmail($invoice);
    }

    private function validateDate(string $date): void
    {
        $currentDate = $this->day->getDate();

        if ($currentDate && $currentDate !== $date) {
            throw new InvalidArgumentException('Shipping date mismatch');
        }
    }

    private function addEmail(InvoiceDataInterface $invoice): void
    {
        $email = $invoice->getCustomerEmail();

        $emailList = $this->day->getCustomerEmails();

        if (in_array($email, $emailList)) {
            throw new MultipleOrdersSameDateException(
                'Invoice ' .
                $invoice->getInvoiceIncrementId() .
                ' : customer ' .
                $invoice->getCustomerEmail() .
                ' already has another invoice for date ' .
                $invoice->getShippingDate()
            );
        }

        $emailList[] = $email;

        $this->day->setCustomerEmails($emailList);
    }
}

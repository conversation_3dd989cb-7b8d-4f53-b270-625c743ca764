<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate;

use EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Data\AmountsInterface;
use EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Data\InvoiceDataInterface;

class Amounts
{
    private AmountsInterface $amounts;

    public function __construct(AmountsInterface $amounts)
    {
        $this->amounts = $amounts;
    }

    public function addInvoice(InvoiceDataInterface $invoice): void
    {
        $this->addTotals($invoice);
        $this->addTaxDetails($invoice->getTaxDetails());
        $this->addTaxBase($invoice->getTaxBase());
    }

    private function addTaxDetails(array $taxDetails): void
    {
        $currentTax = $this->amounts->getTaxDetails();

        foreach ($taxDetails as $rate => $amount) {
            if (isset($currentTax[$rate])) {
                $currentTax[$rate] += $amount;
            } else {
                $currentTax[$rate] = $amount;
            }
        }

        $this->amounts->setTaxDetails($currentTax);
    }

    private function addTaxBase(array $taxBase): void
    {
        $currentTax = $this->amounts->getTaxBase();

        foreach ($taxBase as $rate => $amount) {
            if (isset($currentTax[$rate])) {
                $currentTax[$rate] += $amount;
            } else {
                $currentTax[$rate] = $amount;
            }
        }

        $this->amounts->setTaxBase($currentTax);
    }

    private function addTotals(InvoiceDataInterface $invoice): void
    {
        $this->addAmounts(
            $invoice->getTotalExclTax(),
            $invoice->getTotalInclTax(),
            $invoice->getOrderTotalInclTax()
        );
    }

    private function addAmounts(float $totalExclTax, float $totalInclTax, float $orderInclTax): void
    {
        $total = $this->amounts->getTotalExclTax();
        $this->amounts->setTotalExclTax($total + $totalExclTax);

        $total = $this->amounts->getTotalInclTax();
        $this->amounts->setTotalInclTax($total + $totalInclTax);

        $total = $this->amounts->getOrdersTotalInclTax();
        $this->amounts->setOrdersTotalInclTax($total + $orderInclTax);
    }
}

<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapNewInvoice\Repository\Data;

use EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Data\CustomerDataInterface;
use EatLf\Bap\Model\Entity\BapCustomer;

class Customer implements CustomerDataInterface
{
    private BapCustomer $bapCustomer;

    public function __construct(BapCustomer $bapCustomer)
    {
        $this->bapCustomer = $bapCustomer;
    }

    public function setOrdersCount(int $ordersCount): void
    {
        $this->bapCustomer->setOrdersCount($ordersCount);
    }

    public function getOrdersCount(): int
    {
        return $this->bapCustomer->getOrdersCount();
    }

    public function getEmail(): string
    {
        return $this->bapCustomer->getEmail();
    }

    public function getOrdersTotalInclTax(): float
    {
        return $this->bapCustomer->getOrdersTotalInclTax();
    }

    public function getTotalInclTax(): float
    {
        return $this->bapCustomer->getTotalInclTax();
    }

    public function getTotalExclTax(): float
    {
        return $this->bapCustomer->getTotalExclTax();
    }

    public function getTaxDetails(): array
    {
        return $this->bapCustomer->getTaxDetails();
    }

    public function getTaxBase(): array
    {
        return $this->bapCustomer->getTaxBase();
    }

    public function setOrdersTotalInclTax(float $ordersTotalInclTax): void
    {
        $this->bapCustomer->setOrdersTotalInclTax($ordersTotalInclTax);
    }

    public function setTotalInclTax(float $totalInclTax): void
    {
        $this->bapCustomer->setTotalInclTax($totalInclTax);
    }

    public function setTotalExclTax(float $totalExclTax): void
    {
        $this->bapCustomer->setTotalExclTax($totalExclTax);
    }

    public function setTaxDetails(array $taxDetails): void
    {
        $this->bapCustomer->setTaxDetails($taxDetails);
    }

    public function setTaxBase(array $taxBase): void
    {
        $this->bapCustomer->setTaxBase($taxBase);
    }
}

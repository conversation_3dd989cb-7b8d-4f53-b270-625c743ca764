<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Domain\BapNewInvoice\Service;

use EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Data\InvoiceDataInterface;
use EatLf\Bap\Model\Domain\BapNewInvoice\Repository\BapNewInvoiceRepository;
use EatLf\Bap\Model\Domain\BapNewInvoice\Service\Data\InvoiceData;
use EatLf\Bap\Model\Domain\BapNewInvoice\Service\Data\InvoiceInputInterface;
use EatLf\Bap\Model\Domain\BapNewInvoice\Service\Exception\InvalidDateException;

class BapNewInvoiceService
{
    private BapGatewayInterface $dataGateway;

    private BapNewInvoiceRepository $bapNewInvoiceRepository;

    public function __construct(
        BapGatewayInterface $dataGateway,
        BapNewInvoiceRepository $bapNewInvoiceRepository,
    ) {
        $this->dataGateway = $dataGateway;
        $this->bapNewInvoiceRepository = $bapNewInvoiceRepository;
    }

    /**
     * @throws \EatLf\Bap\Model\Domain\BapNewInvoice\Service\Exception\InvalidDateException
     * @throws \EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Exception\BapNewInvoiceException
     * @throws \EatLf\Bap\Model\Domain\BapNewInvoice\Repository\Exception\InvoiceAlreadyExistsException
     */
    public function addNewInvoice(InvoiceInputInterface $invoiceInput): void
    {
        [$month, $year] = $this->extractMonthAndYear($invoiceInput->getShippingDate());

        $bapId = $this->dataGateway->getOrCreateBap(
            $invoiceInput->getContributionId(),
            $invoiceInput->getFranchiseId(),
            $month,
            $year
        );

        $invoiceData = new InvoiceData($bapId, $invoiceInput);

        $this->dataGateway->lockBap(
            $bapId,
            $this->addInvoice(...),
            [$invoiceData]
        );
    }

    /**
     * @throws \EatLf\Bap\Model\Domain\BapNewInvoice\Service\Exception\InvalidDateException
     */
    private function extractMonthAndYear(string $date): array
    {
        try {
            $dateObj = new \DateTime($date);
        } catch (\Exception) {
            throw new InvalidDateException('Invalid invoice date received: ' . $date);
        }

        return [
            (int)$dateObj->format('n'),
            (int)$dateObj->format('Y'),
        ];
    }

    /**
     * @throws \EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Exception\BapNewInvoiceException
     * @throws \EatLf\Bap\Model\Domain\BapNewInvoice\Repository\Exception\InvoiceAlreadyExistsException
     */
    private function addInvoice(InvoiceDataInterface $invoice): void
    {
        $model = $this->bapNewInvoiceRepository->getForInvoice($invoice);

        $model->saveInvoice();

        $this->bapNewInvoiceRepository->save($model);
    }
}

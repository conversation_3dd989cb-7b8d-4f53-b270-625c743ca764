<?php

namespace EatLf\Bap\Model\Domain\BapNewInvoice\Repository;

use EatLf\Bap\Model\Domain\BapNewInvoice\Aggregate\Data\InvoiceDataInterface;
use EatLf\Bap\Model\Domain\BapNewInvoice\Repository\Data\BapData;

interface BapDataGatewayInterface
{
    public function getInvoiceExists(string $incrementId): bool;

    public function getBapDataByInvoice(InvoiceDataInterface $invoiceData): BapData;

    public function saveBapData(BapData $bapData): void;
}

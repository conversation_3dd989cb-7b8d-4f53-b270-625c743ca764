<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Entity\Command;

use EatLf\Bap\Model\Entity\ResourceModel\Bap;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\DB\Select;
use Throwable;

class LockBap
{
    private ResourceConnection $resourceConnection;

    private AdapterInterface $connection;

    public function __construct(ResourceConnection $resourceConnection)
    {
        $this->resourceConnection = $resourceConnection;
    }

    /**
     * @throws \Throwable
     */
    public function execute(int $bapId, callable $callable, array $args = []): mixed
    {
        $this->connection = $this->resourceConnection->getConnection();

        $query = $this->getLockQuery($bapId);

        $this->connection->beginTransaction();

        $this->connection->query($query);

        try {
            $result = $callable(...$args);

            $this->connection->commit();

            return $result;
        } catch (Throwable $e) {
            $this->connection->rollBack();
            throw $e;
        }
    }

    private function getLockQuery(int $bapId): Select
    {
        return $this->connection
            ->select()
            ->from(Bap::TABLE_NAME, 'bap_id')
            ->where('bap_id = ?', $bapId)
            ->forUpdate(true);
    }
}

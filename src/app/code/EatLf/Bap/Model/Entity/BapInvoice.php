<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Entity;

use EatLf\Bap\Api\Data\BapInvoiceInterface;
use Magento\Framework\Model\AbstractModel;

class BapInvoice extends AbstractModel implements BapInvoiceInterface
{
    protected function _construct(): void
    {
        $this->_init(ResourceModel\BapInvoice::class);
    }

    public function getIncrementId(): string
    {
        return $this->_getData(BapInvoiceInterface::INVOICE_ID);
    }

    public function getCustomerEmail(): string
    {
        return $this->_getData(BapInvoiceInterface::CUSTOMER_EMAIL);
    }

    public function getShippingDate(): string
    {
        return $this->_getData(BapInvoiceInterface::SHIPPING_DATE);
    }

    public function getBapId(): int
    {
        return (int)$this->_getData(BapInvoiceInterface::BAP_ID_FIELD);
    }

    public function getOrderTotalInclTax(): float
    {
        return (float)$this->_getData(BapInvoiceInterface::ORDER_TOTAL_INCL_TAX_FIELD);
    }

    public function getTotalInclTax(): float
    {
        return (float)$this->_getData(BapInvoiceInterface::TOTAL_INCL_TAX_FIELD);
    }

    public function getTotalExclTax(): float
    {
        return (float)$this->_getData(BapInvoiceInterface::TOTAL_EXCL_TAX_FIELD);
    }

    public function getTaxDetails(): array
    {
        return $this->_getData(BapInvoiceInterface::TAX_DETAILS_FIELD);
    }

    public function getTaxBase(): array
    {
        return $this->_getData(BapInvoiceInterface::TAX_BASE_FIELD);
    }

    public function setBapId(int $bapId): void
    {
        $this->setData(BapInvoiceInterface::BAP_ID_FIELD, $bapId);
    }
}

<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Entity\Query;

use EatLf\Bap\Api\Data\BapInvoiceInterface;
use EatLf\Bap\Model\Entity\BapInvoice;
use EatLf\Bap\Model\Entity\BapInvoiceFactory;
use Magento\Framework\Exception\NoSuchEntityException;

class GetBapInvoiceByIncrementId
{
    private \EatLf\Bap\Model\Entity\ResourceModel\BapInvoice $resource;

    private BapInvoiceFactory $bapInvoiceFactory;

    public function __construct(
        \EatLf\Bap\Model\Entity\ResourceModel\BapInvoice $resource,
        BapInvoiceFactory $bapInvoiceFactory
    ) {
        $this->resource = $resource;
        $this->bapInvoiceFactory = $bapInvoiceFactory;
    }

    /**
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(string $incrementId): BapInvoice
    {
        $bapInvoice = $this->bapInvoiceFactory->create();

        $this->resource->load($bapInvoice, $incrementId, BapInvoiceInterface::INVOICE_ID);

        if (!$bapInvoice->getId()) {
            throw new NoSuchEntityException();
        }

        return $bapInvoice;
    }
}

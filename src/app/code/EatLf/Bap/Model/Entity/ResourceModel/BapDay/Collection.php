<?php
declare(strict_types=1);

namespace EatLf\Bap\Model\Entity\ResourceModel\BapDay;

use EatLf\Bap\Model\Entity\BapDay;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'bap_day_id';

    /**
     * @inheritDoc
     */
    protected function _construct(): void
    {
        $this->_init(
            BapDay::class,
            \EatLf\Bap\Model\Entity\ResourceModel\BapDay::class
        );
    }

    /**
     * Deserialize entity fields.
     *
     * @return \EatLf\Bap\Model\ResourceModel\BapDay\Collection
     */
    protected function _afterLoad(): static
    {
        parent::_afterLoad();

        foreach ($this as $item) {
            $this->_resource->unserializeFields($item);
        }

        return $this;
    }
}

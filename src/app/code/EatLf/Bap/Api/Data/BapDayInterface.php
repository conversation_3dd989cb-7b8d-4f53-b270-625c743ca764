<?php

namespace EatLf\Bap\Api\Data;

interface BapDayInterface
{
    // Fields
    public const CUSTOMER_EMAILS_FIELD = 'customer_emails';
    public const BAP_ID_FIELD = 'bap_id';
    public const SHIPPING_DATE_FIELD = 'shipping_date';
    public const TOTAL_EXCL_TAX_FIELD = 'total_excl_tax';
    public const TOTAL_INCL_TAX_FIELD = 'total_incl_tax';
    public const ORDER_TOTAL_INCL_TAX_FIELD = 'order_total_incl_tax';
    public const TAX_DETAILS_FIELD = 'tax_details';
    public const TAX_BASE_FIELD = 'tax_base';

    public function getDate(): string;

    public function getCustomerEmails(): array;

    public function setCustomerEmails(array $customerEmails): void;

    public function getOrdersTotalInclTax(): float;

    public function getTotalInclTax(): float;

    public function getTotalExclTax(): float;

    public function getTaxDetails(): array;

    public function getTaxBase(): array;
}

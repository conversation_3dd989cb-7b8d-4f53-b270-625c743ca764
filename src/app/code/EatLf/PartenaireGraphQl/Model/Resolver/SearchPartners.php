<?php
declare(strict_types=1);

namespace EatLf\PartenaireGraphQl\Model\Resolver;

use EatLf\Partenaire\Api\PartenaireSearchInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class SearchPartners implements ResolverInterface
{
    /**
     * @var \EatLf\Partenaire\Api\PartenaireSearchInterface
     */
    private PartenaireSearchInterface $partenaireSearch;

    public function __construct(
        PartenaireSearchInterface $partenaireSearch
    ) {
        $this->partenaireSearch = $partenaireSearch;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!is_array($args) || !isset($args['queryText'])) {
            return [];
        }

        return $this->partenaireSearch->searchFilterBy(
            $args['queryText'],
            1,
            0,
            $args['pageSize'],
            $args['currentPage'],
        );
    }
}
<?php
declare(strict_types=1);

namespace EatLf\PartenaireGraphQl\Model\Resolver;

use EatLf\Partenaire\Model\CreateContactMailInterface;
use Magento\Framework\DataObject;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class SendEmail implements ResolverInterface
{
    /**
     * @var CreateContactMailInterface
     */
    private $mail;

    public function __construct(
        CreateContactMailInterface $mail
    ) {
        $this->mail = $mail;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null) : bool
    {
        if (!is_array($args)) {
            return false;
        }

        try {
            $this->mail->send(
                $args['emailInput']['emailAddress'],
                ['data' => new DataObject($args['emailInput'])]
            );
        } catch (\Exception $e) {
            return false;
        }

        return true;
    }
}
type Query {
    searchPartners(
        queryText: String!,
        pageSize: Int = 10,
        currentPage: Int = 1
    ): [Partner]

    @resolver(class:"EatLf\\PartenaireGraphQl\\Model\\Resolver\\SearchPartners")

    @doc(description:
    "Searches for partners whose name / address matches the query string"
    )

    @cache(cacheable: false)
}

input PartnerContactInput @doc(description: "PartnerContact contains all partner form datas") {
    firstName: String
    lastName: String
    emailAddress: String
    phone: String
    job: String
    companyName: String
    employeesNumber: String
    companyAddress: String
    locality: String
}

type Mutation {
    sendEmailPartner(
        emailInput: PartnerContactInput
    ): <PERSON><PERSON><PERSON>

    @resolver(class: "EatLf\\PartenaireGraphQl\\Model\\Resolver\\SendEmail")

    @doc(description:
    "Send Email with new Partner information"
    )

    @cache(cacheable: false)
}


type Partner {
    id: Int
    company: String
    address: String
    postcode: String
    city: String
}

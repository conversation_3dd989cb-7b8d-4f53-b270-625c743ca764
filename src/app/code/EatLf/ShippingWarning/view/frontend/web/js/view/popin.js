define([
    'ko',
    'jquery',
    'moment',
    'uiComponent',
    'Magento_Ui/js/modal/modal',
    'EatLf_Shipping/js/model/shipping'
], function (
    ko,
    $,
    moment,
    Component,
    modal,
    shippingModel
) {
    'use strict';

    var DELAY = 10;

    return Component.extend({
        defaults: {
            template: 'EatLf_ShippingWarning/popin',
            delay: ''
        },

        modal: null,

        initialize: function () {
            this._super();

            this.setupPopin = this.setupPopin.bind(this);
            this.onShippingDataChange = this.onShippingDataChange.bind(this);

            return this;
        },

        initObservable: function () {
            this._super()
                .observe(['delay']);

            return this;
        },

        showPopin: function () {
            this.modal.openModal();
        },

        onShippingDataChange: function () {
            var timeDifference,
                maxOrderTime = shippingModel.data().maxOrderTime,
                shippingDate = shippingModel.data().selectedDate;

            if (!maxOrderTime || shippingDate !== moment().format('YYYY-MM-DD')) {
                return;
            }

            clearInterval(this.interval);

            this.interval = setInterval(function () {
                timeDifference = (moment(maxOrderTime, 'Hmm') - moment()) / 60000;

                this.delay(Math.round(timeDifference));

                if (timeDifference <= DELAY && timeDifference > 0) {
                    this.showPopin();
                    clearInterval(this.interval);
                }
            }.bind(this), 1000);
        },

        setupPopin: function (element) {
            this.modal = modal({
                title: 'L\'heure tourne...',
                buttons: [{
                    text: 'OK'
                }],
                modalClass: 'shipping-warning-modal popup-authentication modal-slide'
            }, element);

            ko.computed(this.onShippingDataChange);
        }
    });
});
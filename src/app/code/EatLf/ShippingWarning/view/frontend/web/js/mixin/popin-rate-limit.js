define([
    'jquery',
    'EatLf_Shipping/js/model/shipping',
    'mage/storage',
    'jquery/jquery-storageapi'
], function ($, shippingModel) {
    'use strict';

    var STORAGE_KEY = 'shipping-warning-popin';

    // Initialize storage
    if ($.localStorage.get(STORAGE_KEY) === null) {
        $.localStorage.set(STORAGE_KEY, {
            shown: false,
            maxOrderTime: null
        });
    }

    // Reset flag on shipping data change
    shippingModel.data.subscribe(function (newData) {
        var cacheData = $.localStorage.get(STORAGE_KEY);

        if (newData.maxOrderTime === cacheData.maxOrderTime) {
            return;
        }

        $.localStorage.set(STORAGE_KEY, {
            shown: false
        });
    });

    return function (Popin) {
        return Popin.extend({
            showPopin: function () {
                var cacheData = $.localStorage.get(STORAGE_KEY);

                if (cacheData.shown) {
                    return;
                }

                $.localStorage.set(STORAGE_KEY, {
                    shown: true,
                    maxOrderTime: shippingModel.data().maxOrderTime
                });

                return this._super();
            }
        });
    };
});

<?php

namespace EatLf\SalesInventory\Cron;

use Magento\Framework\DB\Adapter\Pdo\Mysql;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;
use Magento\Sales\Model\Order;
use Magento\Framework\App\Config\ScopeConfigInterface;

/**
 * Class ReassignStocks
 * @package EatLf\SalesInventory\Cron
 */
class ReassignStocks
{

    /**
     *
     */
    const AUTO_CANCELATION_ORDER_TIME = 'eat_sales/general/auto_cancelation_time';

    /**
     * @var OrderCollectionFactory
     */
    private $orderCollectionFactory;

    /**
     * @var ScopeConfigInterface
     */
    private $globalConfig;

    /**
     * ReassignStocks constructor.
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param ScopeConfigInterface $globalConfig
     */
    public function __construct(
        OrderCollectionFactory $orderCollectionFactory,
        ScopeConfigInterface $globalConfig
    )
    {
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->globalConfig = $globalConfig;
    }

    /**
     * Clears stocks data for dates earlier than today
     * @throws \Exception
     */
    public function execute()
    {
        $reassignDate = new \DateTime();
        $reassignDate->sub(new \DateInterval('PT' . $this->globalConfig->getValue(self::AUTO_CANCELATION_ORDER_TIME) . 'M'));

        $shippingDate = new \DateTime();

        $orders = $this->orderCollectionFactory->create();

        $orders
            ->getSelect()
            ->where('main_table.created_at < ?',
                $reassignDate->format(Mysql::DATETIME_FORMAT))
            ->where('main_table.shipping_date >= ?',
                $shippingDate->format(Mysql::DATE_FORMAT))
            ->where('main_table.state = ?',
                Order::STATE_PENDING_PAYMENT);

        /** @var Order[] $orders */
        foreach ($orders as $order) {
                $order->cancel()->save();
        }
    }
}
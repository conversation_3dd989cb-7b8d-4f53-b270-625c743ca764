<?php
declare(strict_types=1);

namespace EatLf\Franchise\Observer;

use Lf\Franchise\Model\Franchise;
use Lf\Franchise\Model\FranchiseProduct;
use Lf\Franchise\Model\FranchiseProductRepository;
use Lf\Franchise\Api\FranchiseProductRepositoryInterface;
use Lf\Franchise\Model\ResourceModel\Franchise\CollectionFactory as FranchiseCollectionFactory;
use Lf\Franchise\Api\Data\FranchiseProductInterfaceFactory;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class CreateFranchiseProductDatas
 * @package EatLf\Franchise\Observer
 */
class CreateFranchiseProductDatas implements ObserverInterface
{
    /**
     * @var FranchiseCollectionFactory
     */
    private $franchiseCollectionFactory;

    /**
     * @var FranchiseProductRepositoryInterface
     */
    private $franchiseProductRepository;

    /**
     * @var FranchiseProductInterfaceFactory
     */
    private $franchiseProductFactory;

    /**
     * CreateFranchiseProductDatas constructor.
     */
    public function __construct(
        FranchiseCollectionFactory $franchiseCollectionFactory,
        FranchiseProductInterfaceFactory $franchiseProductFactory,
        FranchiseProductRepository $franchiseProductRepository
    )
    {
        $this->franchiseProductFactory = $franchiseProductFactory;
        $this->franchiseProductRepository = $franchiseProductRepository;
        $this->franchiseCollectionFactory = $franchiseCollectionFactory;
    }

    /**
     * Create entries in Franchise_Product table when is_active_eatlf status is set to 1 on a product
     *
     * @param EventObserver $observer
     */
    public function execute(EventObserver $observer)
    {
        /** @var Product $product */
        $product = $observer->getEvent()->getProduct();

        $franchises = $this->franchiseCollectionFactory->create();

        if ($product->getData('is_active_eatlf') == Status::STATUS_ENABLED && $product->getData('is_active_eatlf') != $product->getOrigData('is_active_eatlf')) {
            /** @var Franchise $franchise */
            foreach ($franchises as $franchise) {
                try {
                    $this->franchiseProductRepository->getByFranchiseProduct(
                        $franchise->getFranchiseId(),
                        $product->getId()
                    );
                } catch (NoSuchEntityException $e) {
                    /** @var FranchiseProduct $franchiseProduct */
                    $franchiseProduct = $this->franchiseProductFactory->create();
                    $franchiseProduct
                        ->setFranchiseId($franchise->getFranchiseId())
                        ->setProductId($product->getId())
                        ->setPrice(0)
                        ->setPriceHT(0)
                        ->setSpecialPrice(0)
                        ->setSpecialPriceHT(0)
                        ->setIsActiveBO(false)
                        ->setIsActiveFO(false)
                        ->setTaxClassId($product->getOriginalTaxClassId())
                        ->setCodeCompta($product->getCodeCompta())
                        ->setAtelier(1);
                    try {
                        $franchiseProduct->save();
                    } catch (\Exception $e) {
                        throwException('Unable to create Franchise Product : ' . $e->getMessage());
                    }
                }
            }
        }
    }
}

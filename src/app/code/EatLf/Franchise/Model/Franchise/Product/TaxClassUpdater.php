<?php
namespace EatLf\Franchise\Model\Franchise\Product;

use Lf\Franchise\Api\Data\FranchiseProductInterface as FranchiseProductInterfaceAlias;
use Lf\Franchise\Model\Franchise\Product\TaxClassUpdaterInterface;

class TaxClassUpdater implements TaxClassUpdaterInterface
{
    public function updatePrices(FranchiseProductInterfaceAlias $franchiseProduct): void
    {
        $franchiseProduct->changePrice($franchiseProduct->getPrice());
        $franchiseProduct->changeSpecialPrice($franchiseProduct->getSpecialPrice());
    }
}

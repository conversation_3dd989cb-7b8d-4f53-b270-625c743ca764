<?php
declare(strict_types=1);

namespace EatLf\Franchise\Setup\Patch\Schema;

use Magento\Framework\DB\Ddl\Table;
use Magento\Framework\Setup\Patch\SchemaPatchInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

class AddFranchiseRecommandedTtcPriceOnFranchiseProductGridTable implements SchemaPatchInterface
{
    /**
     * @var SchemaSetupInterface $schemaSetup
     */
    private SchemaSetupInterface $schemaSetup;

    /**
     * @param SchemaSetupInterface $schemaSetup
     */
    public function __construct(
        SchemaSetupInterface $schemaSetup
    ) {
        $this->schemaSetup = $schemaSetup;
    }

    /**
     * @return $this|AddFranchiseRecommandedTtcPriceOnFranchiseProductGridTable
     */
    public function apply(): AddFranchiseRecommandedTtcPriceOnFranchiseProductGridTable
    {
        //ce patch a été créé pour palier à un problème de version/merge entre les branche EAT et LF
        // src/app/code/Lf/Franchise/Setup/UpgradeSchema.php

        $this->schemaSetup->startSetup();
        $connection = $this->schemaSetup->getConnection();

        $tableName = $connection->getTableName('franchise_product_grid');
        $columnName = 'franchise_recommanded_ttc_price';

        if ($connection->tableColumnExists($tableName, $columnName) === false) {
            $connection->addColumn(
                $tableName,
                $columnName,
                [
                    'type' => Table::TYPE_DECIMAL,
                    'comment' => 'Prix conseillé TTC',
                    'length' => '12,4',
                    'default' => 0
                ]
            );
        }
        $this->schemaSetup->endSetup();
        return $this;
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}

<?php
declare(strict_types=1);

namespace EatLf\Franchise\Rewrite\Model\ResourceModel\FranchiseProduct\Grid;

use Lf\Franchise\Model\ResourceModel\FranchiseProductGrid;
use Magento\Backend\Model\Auth\Session as AuthSession;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Data\Collection\Db\FetchStrategyInterface as FetchStrategy;
use Magento\Framework\Data\Collection\EntityFactoryInterface as EntityFactory;
use Magento\Framework\Event\ManagerInterface as EventManager;
use Psr\Log\LoggerInterface as Logger;

class Collection extends \Lf\Franchise\Model\ResourceModel\FranchiseProduct\Grid\Collection
{
    /**
     * @var AttributeRepositoryInterface
     */
    private $attributeRepository;

    public function __construct(EntityFactory $entityFactory,
                                Logger $logger,
                                FetchStrategy $fetchStrategy,
                                EventManager $eventManager,
                                EavConfig $eavConfig,
                                AuthSession $authSession,
                                AttributeRepositoryInterface $attributeRepository,
                                $mainTable = 'franchise_product_grid',
                                $resourceModel = FranchiseProductGrid::class)
    {
        $this->attributeRepository = $attributeRepository;

        parent::__construct($entityFactory,
            $logger,
            $fetchStrategy,
            $eventManager,
            $eavConfig,
            $authSession,
            $attributeRepository,
            $mainTable,
            $resourceModel);
    }

    protected function _initSelect()
    {
        parent::_initSelect();

        $enabledValue = Status::STATUS_ENABLED;

        $statusAtt = $this->attributeRepository
            ->get('catalog_product', 'status')
            ->getAttributeId();

        $eatStatusAtt = $this->attributeRepository
            ->get('catalog_product', 'is_active_eatlf')
            ->getAttributeId();

        $this->join(
            ['status' => 'catalog_product_entity_int'],
            "status.entity_id = main_table.product_id and status.store_id = 0 and status.attribute_id = {$statusAtt} and status.value = {$enabledValue}",
            []
        )->join(
            ['eat_status' => 'catalog_product_entity_int'],
            "eat_status.entity_id = main_table.product_id and eat_status.store_id = 0 and eat_status.attribute_id = {$eatStatusAtt} and eat_status.value = {$enabledValue}",
            []
        );

        return $this;
    }

    public function setOrder($field, $direction = self::SORT_ORDER_DESC)
    {
        if ($field !== 'sku') {
            return parent::setOrder($field, $direction);
        }

        return parent::setOrder('(sku + 0) ' . $direction . ', sku', $direction);
    }


}
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <listingToolbar name="listing_top"/>

    <columns name="spinner_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="franchiseeat/product/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">franchise_product_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">franchise_product.franchise_product.spinner_columns.ids
                </param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">franchise_product.franchise_product.spinner_columns_editor
                    </item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids" sortOrder="10">
            <settings>
                <indexField>franchise_product_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="franchise_code">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="componentDisabled" xsi:type="boolean">true</item>
                </item>
            </argument>
            <settings>
                <filter>text</filter>
                <label translate="true">Franchise</label>
            </settings>
        </column>
        <column name="sku">
            <settings>
                <filter>text</filter>
                <label translate="true">Sku du produit</label>
            </settings>
        </column>
        <column name="product_name">
            <settings>
                <filter>text</filter>
                <label translate="true">Nom du produit</label>
            </settings>
        </column>
        <column name="franchise_recommanded_ttc_price" class="Magento\Sales\Ui\Component\Listing\Column\Price" sortOrder="39">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Prix conseillé TTC</label>
            </settings>
        </column>
        <column name="franchise_price_ttc" class="Magento\Sales\Ui\Component\Listing\Column\Price" sortOrder="40">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Prix du produit</label>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>text</editorType>
                </editor>
            </settings>
        </column>
        <column name="franchise_special_price_ttc" class="Magento\Sales\Ui\Component\Listing\Column\Price"
                sortOrder="50">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Prix remisé du produit</label>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>text</editorType>
                </editor>
            </settings>
        </column>

        <column name="franchise_price_ht" sortOrder="60">
            <settings>
                <editor>
                    <editorType>none</editorType>
                </editor>
            </settings>
        </column>

        <column name="franchise_special_price_ht" sortOrder="70">
            <settings>
                <editor>
                    <editorType>none</editorType>
                </editor>
            </settings>
        </column>

        <column name="product_cout_matiere" sortOrder="80"/>

        <column name="product_cout_matiere_percent" sortOrder="90"/>

        <column name="actif_bo" component="Magento_Ui/js/grid/columns/select" sortOrder="100">
            <settings>
                <options class="Magento\Cms\Model\Block\Source\IsActive"/>
                <filter>select</filter>
                <dataType>select</dataType>
                <label translate="true">Actif BO</label>
                <editor>
                    <editorType>select</editorType>
                </editor>
            </settings>
        </column>

        <column name="actif_fo" component="Magento_Ui/js/grid/columns/select" sortOrder="110">
            <settings>
                <options class="Magento\Cms\Model\Block\Source\IsActive"/>
                <filter>select</filter>
                <dataType>select</dataType>
                <label translate="true">Actif FO</label>
                <editor>
                    <editorType>select</editorType>
                </editor>
            </settings>
        </column>

        <column name="franchise_atelier">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="componentDisabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </column>

        <column name="no_production">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="componentDisabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </column>

        <column name="franchise_recommanded_ht_price">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="componentDisabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </column>

        <column name="preparation_time">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="componentDisabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </column>

        <column name="code_compta" sortOrder="170">
            <settings>
                <filter>text</filter>
                <label translate="true">Code pour la compta</label>
                <editor>
                    <editorType>none</editorType>
                </editor>
            </settings>
        </column>

        <actionsColumn name="actions" class="Lf\Franchise\Ui\Component\Listing\Column\FranchiseProductActions">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="componentDisabled" xsi:type="boolean">true</item>
                </item>
            </argument>
            <settings>
                <indexField>franchise_product_id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>


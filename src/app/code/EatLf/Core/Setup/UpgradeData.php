<?php

namespace EatLf\Core\Setup;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\AttributeSetManagement;
use Magento\Eav\Model\Entity\Attribute\SetFactory;
use Magento\Eav\Model\Entity\TypeFactory;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\App\Config\ConfigResource\ConfigInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;

class UpgradeData implements UpgradeDataInterface
{

    private $resourceConfig;
    private $attributeSetFactory;
    private $attributeSetManagement;
    /**
     * @var EavSetupFactory
     */
    private $eavSetupFactory;
    private TypeFactory $eavTypeFactory;

    /**
     * Init
     */
    public function __construct(
        ConfigInterface  $resourceConfig,
        TypeFactory $eavTypeFactory,
        SetFactory $attributeSetFactory,
        EavSetupFactory $eavSetupFactory,
        AttributeSetManagement $attributeSetManagement
    )
    {
        $this->resourceConfig = $resourceConfig;
        $this->eavTypeFactory = $eavTypeFactory;
        $this->attributeSetFactory = $attributeSetFactory;
        $this->attributeSetManagement = $attributeSetManagement;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface $context
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $attributes = [
            'glucides',
            'energie',
            'matieres_grasses',
            'proteines',
            'fibres',
            'sel',
            'acides_gras_satures',
            'sucres'
        ];

        if (version_compare($context->getVersion(), '0.0.2', '<')) {
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
            $attributeSets = $eavSetup->getAllAttributeSetIds(Product::ENTITY);

            foreach($attributeSets as $attributeSet) {
                $eavSetup->addAttributeGroup(
                    Product::ENTITY,
                    $attributeSet,
                    'Valeurs nutritionnelles',
                    40
                );

                foreach ($attributes as $attribute) {
                    $eavSetup->addAttributeToGroup(
                        Product::ENTITY,
                        $attributeSet,
                        'Valeurs nutritionnelles',
                        $attribute
                    );
                }
            }
        }

        if (version_compare($context->getVersion(), '0.0.3', '<')) {
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

            foreach ($attributes as $attribute) {
                $eavSetup->updateAttribute(
                    Product::ENTITY,
                    $attribute,
                    'is_required',
                    0
                );
            }
        }
    }
}

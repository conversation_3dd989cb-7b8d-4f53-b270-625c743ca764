<?php
declare(strict_types=1);

namespace EatLf\CheckoutGraphQl\Observer;

use Lf\Franchise\Model\FranchiseProduct;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class CustomPrice implements ObserverInterface
{
    /**
     * @var \Lf\Franchise\Model\FranchiseProduct
     */
    private FranchiseProduct $franchiseProduct;

    public function __construct(
        FranchiseProduct $franchiseProduct
    ) {
        $this->franchiseProduct = $franchiseProduct;
    }

    public function execute(Observer $observer)
    {
        /** @var \Magento\Quote\Model\Quote\Item $item */
        $item = $observer->getEvent()->getData('quote_item');
        $item = ($item->getParentItem() ? $item->getParentItem() : $item);

        if ($item->getQuote()->getExtensionAttributes() != null) {
            /** @var  \Magento\Quote\Api\Data\CartExtensionFactory $cartExtAtt */
            $shippingData = $item->getQuote()->getExtensionAttributes()->getShippingData();

            $franchiseProduct = $this->franchiseProduct->getByProductAndFranchise(
                $item->getProduct()->getId(),
                $shippingData->getFranchiseId()
            );

            if ($franchiseProduct->getFranchiseProductId()) {
                $price = $franchiseProduct->getPrice();
                if ($franchiseProduct->getSpecialPrice() != 0) {
                    $price = $franchiseProduct->getSpecialPrice();
                }
                $item->setCustomPrice($price);
                $item->setOriginalCustomPrice($price);
                $item->getProduct()->setIsSuperMode(true);
            }
        }
    }
}
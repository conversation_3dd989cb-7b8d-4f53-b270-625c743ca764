<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Quote\Model\Cart\AddProductsToCart">
        <plugin name="checkStocks" type="EatLf\CheckoutGraphQl\Plugin\AddToCartCheckStock"/>
    </type>

    <type name="Magento\QuoteGraphQl\Model\CartItem\DataProvider\UpdateCartItems">
        <plugin name="checkStocks" type="EatLf\CheckoutGraphQl\Plugin\UpdateCartCheckStock"/>
    </type>

    <preference for="Magento\QuoteGraphQl\Model\Cart\CheckCartCheckoutAllowance" type="EatLf\CheckoutGraphQl\Plugin\CheckCartCheckoutAllowance"/>
</config>
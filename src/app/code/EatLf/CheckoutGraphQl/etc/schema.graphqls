type Mutation {
    assignCustomerToGuestCart(cart_id: String!): Cart! @resolver(class: "EatLf\\CheckoutGraphQl\\Model\\Resolver\\AssignCustomerToGuestCart") @doc(description:"Assign a logged in customer to the specified guest shopping cart.")
    validateCart(cart_id: String!): CartValidationOutput @resolver(class: "EatLf\\CheckoutGraphQl\\Model\\Resolver\\CartValidation")
}

type CartValidationOutput {
    cart: Cart!,
    dateInvalid: Boolean!,
    limited: [LimitedProduct]!,
    unavailable: [UnavailableProduct]!,
    partenaireDisabled: Boolean!,
}

type LimitedProduct {
    name: String,
    oldQty: Int,
    newQty: Int
}

type UnavailableProduct {
    name: String
}

interface CartItemInterface {
    original_price: Money @resolver(class: "EatLf\\CheckoutGraphQl\\Model\\Resolver\\CartItemFranchisePrice") @doc(description: "The franchise original price if any")
}

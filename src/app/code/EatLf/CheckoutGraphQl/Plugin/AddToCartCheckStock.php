<?php
declare(strict_types=1);

namespace EatLf\CheckoutGraphQl\Plugin;

use Lf\FranchiseInventory\Api\DailyStockRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\Cart\AddProductsToCart;
use Magento\Quote\Model\Cart\Data\AddProductsToCartOutput;
use Magento\Quote\Model\Cart\Data\Error;

class AddToCartCheckStock
{
    /**
     * @var \Lf\FranchiseInventory\Api\DailyStockRepositoryInterface
     */
    private DailyStockRepositoryInterface $dailyStockRepository;
    /**
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    private ProductRepositoryInterface $productRepository;
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;

    public function __construct(
        DailyStockRepositoryInterface $dailyStockRepository,
        ProductRepositoryInterface $productRepository,
        CartRepositoryInterface $cartRepository
    ) {
        $this->dailyStockRepository = $dailyStockRepository;
        $this->productRepository = $productRepository;
        $this->cartRepository = $cartRepository;
    }

    /**
     * This plugin makes sure that product/qty added to the cart
     * are available for the associated franchise. Otherwise an
     * 'INSUFFICIENT_STOCK' error message is returned.
     *
     * @param \Magento\Quote\Model\Cart\AddProductsToCart $subject
     * @param \Magento\Quote\Model\Cart\Data\AddProductsToCartOutput $result
     * @param string $maskedCartId
     * @param array $cartItems
     *
     * @return \Magento\Quote\Model\Cart\Data\AddProductsToCartOutput
     * @throws \Exception
     */
    public function afterExecute(
        AddProductsToCart $subject,
        AddProductsToCartOutput $result,
        string $maskedCartId,
        array $cartItems
    ): AddProductsToCartOutput {
        /** @var \Magento\Quote\Model\Quote $cart */
        $cart = $result->getCart();
        $shippingData = $cart->getExtensionAttributes()->getShippingData();
        $errors = $result->getErrors();

        $franchiseStock = $this->dailyStockRepository->getForFranchise(
            $shippingData->getFranchiseId(),
            new \DateTime($shippingData->getShippingDate())
        );

        foreach ($cartItems as $itemPosition => $cartItem) {
            try {
                $product = $this->productRepository->get($cartItem->getSku());
            } catch (NoSuchEntityException $e) {
                continue;
            }

            if ($product->getIsAccessory() == 1) {
                continue;
            }

            $cartItem = $cart->getItemByProduct($product);
            $productStock = $franchiseStock->getStockForProduct($product->getId())->getStock();

            $hasStockError = false;

            if ($productStock == 0) {
                $cart->removeItem($cartItem->getItemId());
                $hasStockError = true;
            }

            if ($productStock - $cartItem->getQty() < 0) {
                $cartItem->setQty($productStock);
                $hasStockError = true;
            }

            if ($hasStockError) {
                $this->cartRepository->save($cart);

                $errors[] = new Error(
                    'Invalid stock data',
                    'INSUFFICIENT_STOCK',
                    $itemPosition
                );
            }
        }

        return new AddProductsToCartOutput($cart, $errors);
    }
}
<?php
declare(strict_types=1);

namespace EatLf\CheckoutGraphQl\Plugin;

use Lf\FranchiseInventory\Api\DailyStockRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\Quote;
use Magento\QuoteGraphQl\Model\CartItem\DataProvider\UpdateCartItems;

class UpdateCartCheckStock
{
    /**
     * @var \Lf\FranchiseInventory\Api\DailyStockRepositoryInterface
     */
    private DailyStockRepositoryInterface $dailyStockRepository;
    /**
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    private ProductRepositoryInterface $productRepository;
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;

    public function __construct(
        DailyStockRepositoryInterface $dailyStockRepository,
        ProductRepositoryInterface $productRepository,
        CartRepositoryInterface $cartRepository
    ) {
        $this->dailyStockRepository = $dailyStockRepository;
        $this->productRepository = $productRepository;
        $this->cartRepository = $cartRepository;
    }

    /**
     * This plugin makes sure that the updated product quantity
     * is available for the associated franchise. Otherwise a
     * LocalizedException is thrown and is propagated back to the
     * graphql client.
     *
     * @param \Magento\QuoteGraphQl\Model\CartItem\DataProvider\UpdateCartItems $subject
     * @param \Magento\Quote\Model\Cart\Data\AddProductsToCartOutput $result
     * @param \Magento\Quote\Model\Quote $cart
     * @param array $items
     *
     * @return void
     * @throws \Exception
     */
    public function afterProcessCartItems(UpdateCartItems $subject, $result, Quote $cart, array $items): void
    {
        $shippingData = $cart->getExtensionAttributes()->getShippingData();

        $franchiseStock = $this->dailyStockRepository->getForFranchise(
            $shippingData->getFranchiseId(),
            new \DateTime($shippingData->getShippingDate())
        );

        $hasStockChange = false;

        foreach ($items as $item) {
            $cartItem = $cart->getItemById((int)$item['cart_item_id']);

            try {
                $product = $this->productRepository->get($cartItem->getSku());
            } catch (NoSuchEntityException $e) {
                continue;
            }

            $productStock = $franchiseStock->getStockForProduct($cartItem->getProduct()->getId())
                ->getStock();

            if ($product->getIsAccessory() == 1) {
                continue;
            }

            if ($productStock < $item['quantity']) {
                $hasStockChange = true;
                $cartItem->setQty($productStock);
            }
        }

        if ($hasStockChange) {
            $this->cartRepository->save($cart);
            throw new LocalizedException(__('NOT ENOUGH STOCK'));
        }
    }
}
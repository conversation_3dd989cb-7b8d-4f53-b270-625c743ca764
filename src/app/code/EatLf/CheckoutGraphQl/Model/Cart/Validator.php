<?php
declare(strict_types=1);

namespace EatLf\CheckoutGraphQl\Model\Cart;

use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\Shipping\Api\ShippingDateManagementInterface;
use Lf\FranchiseInventory\Api\DailyStockRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Model\Quote\Item;

class Validator
{
    /**
     * @var ShippingDateManagementInterface
     */
    private ShippingDateManagementInterface $shippingDateManagement;
    /**
     * @var CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;
    /**
     * @var \Lf\FranchiseInventory\Api\DailyStockRepositoryInterface
     */
    private DailyStockRepositoryInterface $dailyStockRepository;
    /**
     * @var PartenaireRepositoryInterface
     */
    private PartenaireRepositoryInterface $partenaireRepository;

    public function __construct(
        ShippingDateManagementInterface $shippingDateManagement,
        CartRepositoryInterface $cartRepository,
        DailyStockRepositoryInterface $dailyStockRepository,
        PartenaireRepositoryInterface $partenaireRepository
    ) {
        $this->shippingDateManagement = $shippingDateManagement;
        $this->cartRepository = $cartRepository;
        $this->dailyStockRepository = $dailyStockRepository;
        $this->partenaireRepository = $partenaireRepository;
    }

    public function validatePartenaire(CartInterface $cart): array
    {
        $disabled = false;
        try {
            $shippingData = $cart->getExtensionAttributes()->getShippingData();
            $partenaireId = $shippingData->getExtensionAttributes()->getPartenaireId();
            try {
                $partenaire = $this->partenaireRepository->getById($partenaireId);

                if($partenaire->getIsActive() == 0) {
                   $disabled = true;
                }
            } catch (NoSuchEntityException $e) {
                $disabled = true;
            }
        } catch (\Exception $e) {
            $disabled = true;
        }

        if ($disabled) {
            //on vide le panier et la shippingBar
            $shippingData->getExtensionAttributes()->setPartenaireId(null);

            /** @var Item $item */
            foreach ($cart->getAllItems() as $item) {
                $cart->removeItem($item->getItemId());
            }
            $cart->setTotalsCollectedFlag(false);
            $this->cartRepository->save($cart);
        }

        return [
            'partenaireDisabled' => $disabled,
        ];
    }

    public function validateDate(CartInterface $cart): array
    {
        try {
            $shippingData = $cart->getExtensionAttributes()->getShippingData();

            if ($shippingData->getShippingDate() == null) {
                return [
                    'dateInvalid' => false,
                ];
            }

            $shippingDate = new \DateTime($shippingData->getShippingDate());

            $isDateValid = $this->shippingDateManagement->isValid(
                $shippingDate,
                $shippingData->getExtensionAttributes()->getPartenaireId()
            );

            if (!$isDateValid) {
                $cart->removeAllItems();

                $shippingDates = $this->shippingDateManagement->getAvailableDates(
                    $shippingData->getExtensionAttributes()->getPartenaireId()
                );

                if (count($shippingDates) > 0) {
                    $shippingData->setShippingDate($shippingDates[0]->format('Y-m-d'));
                } else {
                    $shippingData->setShippingDate(null);
                }

                $this->cartRepository->save($cart);

                return [
                    'dateInvalid' => true,
                ];
            }
        } catch (\Exception $e) {
            return [
                'dateInvalid' => false,
            ];
        }

        return [
            'dateInvalid' => false,
        ];
    }

    public function validateStocks(CartInterface $cart): array
    {
        $result = ['unavailable' => [], 'limited' => []];
        $shippingData = $cart->getExtensionAttributes()->getShippingData();

        if ($shippingData->getShippingDate() == null) {
            return $result;
        }

        try {
            $franchiseStock = $this->dailyStockRepository->getForFranchise(
                $shippingData->getFranchiseId(),
                new \DateTime($shippingData->getShippingDate())
            );
        } catch (\Exception $e) {
            return $result;
        }

        /** @var Item $item */
        foreach ($cart->getAllItems() as $item) {
            $product = $item->getProduct();
            $productId = $product->getId();
            $dailyStock = $franchiseStock->getStockForProduct($productId);

            if ($dailyStock->getStock() != null && $product->getIsAccessory() != 1) {
                if ($dailyStock->getStock() == 0) {
                    $result['unavailable'][] = [
                        'name' => $item->getProduct()->getName(),
                    ];

                    $cart->removeItem($item->getItemId());

                    continue;
                }

                if ($dailyStock->getStock() - $item->getQty() < 0) {
                    $result['limited'][] = [
                        'oldQty' => $item->getQty(),
                        'newQty' => $dailyStock->getStock(),
                        'name' => $item->getProduct()->getName(),
                    ];

                    $item->setQty($dailyStock->getStock());
                }
            }
        }

        $cart->setTotalsCollectedFlag(false);
        $this->cartRepository->save($cart);

        return $result;
    }
}

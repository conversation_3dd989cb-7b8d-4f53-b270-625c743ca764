<?php
declare(strict_types=1);

namespace EatLf\CheckoutGraphQl\Model\Cart;

use Lf\Franchise\Api\FranchiseProductRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartItemInterface;

class FranchisePrices
{
    private $quoteCache = [];
    /**
     * @var \Lf\Franchise\Api\FranchiseProductRepositoryInterface
     */
    private FranchiseProductRepositoryInterface $franchiseProductRepository;
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;
    /**
     * @var \Magento\Framework\Api\SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $criteriaBuilder;

    public function __construct(
        FranchiseProductRepositoryInterface $franchiseProductRepository,
        CartRepositoryInterface $cartRepository,
        SearchCriteriaBuilder $criteriaBuilder
    ) {
        $this->franchiseProductRepository = $franchiseProductRepository;
        $this->cartRepository = $cartRepository;
        $this->criteriaBuilder = $criteriaBuilder;
    }

    public function getFranchisePrice(CartItemInterface $cartItem)
    {
        $cart = $this->cartRepository->get($cartItem->getQuoteId());
        $franchiseId = $cart->getExtensionAttributes()->getShippingData()->getFranchiseId();

        if (!isset($this->quoteCache[$cart->getId()])) {
            $productIds = [];

            foreach ($cart->getItems() as $item) {
                $productIds[] = $item->getProductId();
            }

            $criteria = $this->criteriaBuilder
                ->addFilter('franchise_id', $franchiseId)
                ->addFilter('product_id', $productIds, 'in')
                ->create();

            $this->quoteCache[$cart->getId()] = $this->franchiseProductRepository
                ->getList($criteria)
                ->getItems();
        }

        foreach ($this->quoteCache[$cart->getId()] as $franchiseProduct) {
            if ($franchiseProduct->getProductId() === $cartItem->getProductId()
                && $franchiseProduct->getSpecialPrice() > 0
                && $franchiseProduct->getSpecialPrice() !== $franchiseProduct->getPrice()) {
                return $franchiseProduct->getPrice() * $cartItem->getQty();
            }
        }

        return null;
    }
}
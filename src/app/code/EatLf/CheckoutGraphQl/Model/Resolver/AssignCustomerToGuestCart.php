<?php
declare(strict_types=1);

namespace EatLf\CheckoutGraphQl\Model\Resolver;

use Exception;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\StateException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\GraphQl\Model\Query\ContextInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\Cart\CustomerCartResolver;
use Magento\Quote\Model\QuoteIdMaskFactory;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;

/**
 * Assign the customer to the guest cart resolver
 *
 * @SuppressWarnings(PHPMD.LongVariable)
 */
class AssignCustomerToGuestCart implements ResolverInterface
{
    /**
     * @var GetCartForUser
     */
    private $getCartForUser;
    /**
     * @var CustomerCartResolver
     */
    private $customerCartResolver;
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $quoteRepository;
    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;
    /**
     * @var \Magento\Quote\Model\QuoteIdMaskFactory
     */
    private QuoteIdMaskFactory $quoteIdMaskFactory;
    private CustomerFactory $customerModelFactory;
    /**
     * @var \Magento\Framework\Event\ManagerInterface
     */
    private ManagerInterface $eventManager;

    /**
     * @param GetCartForUser $getCartForUser
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param CustomerCartResolver $customerCartResolver
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Quote\Model\QuoteIdMaskFactory $quoteIdMaskFactory
     * @param \Magento\Customer\Model\CustomerFactory $customerModelFactory
     */
    public function __construct(
        GetCartForUser $getCartForUser,
        CartRepositoryInterface $quoteRepository,
        CustomerCartResolver $customerCartResolver,
        CustomerRepositoryInterface $customerRepository,
        QuoteIdMaskFactory $quoteIdMaskFactory,
        CustomerFactory $customerModelFactory,
        ManagerInterface $eventManager
    ) {
        $this->getCartForUser = $getCartForUser;
        $this->customerCartResolver = $customerCartResolver;
        $this->quoteRepository = $quoteRepository;
        $this->customerRepository = $customerRepository;
        $this->quoteIdMaskFactory = $quoteIdMaskFactory;
        $this->customerModelFactory = $customerModelFactory;
        $this->eventManager = $eventManager;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /** @var ContextInterface $context */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(
                __(
                    'The current customer isn\'t authorized.'
                )
            );
        }

        $currentUserId = $context->getUserId();
        $guestMaskedCartId = $args['cart_id'];
        $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();
        $quote = $this->getCartForUser->execute($guestMaskedCartId, null, $storeId);

        try {
            $this->assignCustomer($quote->getId(), $currentUserId, $storeId);
        } catch (StateException $e) {
            throw new GraphQlInputException(__($e->getMessage()), $e);
        }

        try {
            $customerCart = $this->customerCartResolver->resolve($currentUserId);

            $this->eventManager->dispatch(
                'eatlf_checkout_assign_cart',
                ['cart' => $customerCart]
            );
        } catch (Exception $e) {
            $customerCart = null;
        }

        return [
            'model' => $customerCart,
        ];
    }

    public function assignCustomer($cartId, $customerId, $storeId)
    {
        $quote = $this->quoteRepository->getActive($cartId);
        $customer = $this->customerRepository->getById($customerId);
        $customerModel = $this->customerModelFactory->create();

        if (!in_array($storeId, $customerModel->load($customerId)->getSharedStoreIds())) {
            throw new StateException(
                __("The customer can't be assigned to the cart. The cart belongs to a different store.")
            );
        }
        if ($quote->getCustomerId()) {
            throw new StateException(
                __("The customer can't be assigned to the cart because the cart isn't anonymous.")
            );
        }
        try {
            $customerActiveQuote = $this->quoteRepository->getForCustomer($customerId);
            $customerActiveQuote->setIsActive(0);
            $this->quoteRepository->save($customerActiveQuote);
            // phpcs:ignore Magento2.CodeAnalysis.EmptyBlock
        } catch (NoSuchEntityException $e) {
        }

        $quote->setCustomer($customer);
        $quote->setCustomerIsGuest(0);
        $quote->setIsActive(1);

        /** @var \Magento\Quote\Model\QuoteIdMask $quoteIdMask */
        $quoteIdMask = $this->quoteIdMaskFactory->create()->load($cartId, 'quote_id');
        if ($quoteIdMask->getId()) {
            $quoteIdMask->delete();
        }

        $this->quoteRepository->save($quote);

        return true;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\CheckoutGraphQl\Model\Resolver;

use EatLf\CheckoutGraphQl\Model\Cart\FranchisePrices;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Model\Quote\Item;

class CartItemFranchisePrice implements ResolverInterface
{
    /**
     * @var \EatLf\CheckoutGraphQl\Model\Cart\FranchisePrices
     */
    private FranchisePrices $franchisePrices;

    public function __construct(
        FranchisePrices $franchisePrices
    ) {
        $this->franchisePrices = $franchisePrices;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($value['model'])) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        /** @var Item $cartItem */
        $cartItem = $value['model'];
        $currencyCode = $cartItem->getQuote()->getQuoteCurrencyCode();

        return [
            'value' => $this->franchisePrices->getFranchisePrice($cartItem),
            'currency' => $currencyCode
        ];
    }
}
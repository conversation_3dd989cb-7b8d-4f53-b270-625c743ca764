<?php
declare(strict_types=1);

namespace EatLf\CheckoutGraphQl\Model\Resolver;

use EatLf\CheckoutGraphQl\Model\Cart\Validator;
use EatLf\PartenaireAutoShipping\Model\CartManagement;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;

class CartValidation implements ResolverInterface
{
    /**
     * @var \Magento\QuoteGraphQl\Model\Cart\GetCartForUser
     */
    private GetCartForUser $getCartForUser;
    /**
     * @var \EatLf\CheckoutGraphQl\Model\Cart\Validator
     */
    private Validator $cartValidator;
    /**
     * @var \EatLf\PartenaireAutoShipping\Model\CartManagement
     */
    private CartManagement $cartManagement;

    public function __construct(
        GetCartForUser $getCartForUser,
        Validator $cartValidator,
        CartManagement $cartManagement
    ) {
        $this->getCartForUser = $getCartForUser;
        $this->cartValidator = $cartValidator;
        $this->cartManagement = $cartManagement;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($args['cart_id'])) {
            throw new GraphQlInputException(__('cart_id parameter is missing'));
        }

        $currentUserId = $context->getUserId();
        $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();

        $cart = $this->getCartForUser->execute($args['cart_id'], $currentUserId, $storeId);

        $this->cartManagement
            ->populateShippingAddress($cart)
            ->populateBillingAddressFromShipping($cart);

        return array_merge(
            [
                'cart' => [
                    'model' => $cart,
                ],
            ],
            $this->cartValidator->validateStocks($cart),
            $this->cartValidator->validateDate($cart),
            $this->cartValidator->validatePartenaire($cart)
        );
    }
}

input ProductAttributeFilterInput {
    cart_id: FilterTypeInput @doc(description: "The cart id to get products available for the current franchise")
}

interface ProductInterface {
    franchise_price: Float @doc(description: "The franchise price"),
    franchise_special_price: Float @doc(description: "The franchise special price")
    franchise_stock: Int @doc(description: "The franchise stock")
}

interface CategoryInterface {
    hasProductsInStock(cartId: String): <PERSON><PERSON>an @doc(description: "Whether the category has at least one product available for the given cart") @resolver(class: "EatLf\\FranchiseGraphQl\\Model\\Resolver\\Category\\HasProductsInStock")
}
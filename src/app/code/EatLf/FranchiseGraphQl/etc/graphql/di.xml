<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <virtualType name="Magento\Catalog\Model\Api\SearchCriteria\CollectionProcessor\ProductFilterProcessor">
        <arguments>
            <argument name="customFilters" xsi:type="array">
                <item name="cart_id" xsi:type="object">
                    EatLf\FranchiseGraphQl\Model\Api\SearchCriteria\CollectionProcessor\FilterProcessor\CartIdFilter
                </item>
                <item name="accessory" xsi:type="object">
                    EatLf\FranchiseGraphQl\Model\Api\SearchCriteria\CollectionProcessor\FilterProcessor\AccessoryFilter
                </item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\ProductSearch\ProductCollectionSearchCriteriaBuilder">
        <plugin name="cartIdFilter" type="EatLf\FranchiseGraphQl\Plugin\ProductCollectionSearchCriteriaBuilder"/>
    </type>


    <type name="Magento\QuoteGraphQl\Model\Resolver\AvailablePaymentMethods">
        <plugin name="setFranchiseContext" type="EatLf\FranchiseGraphQl\Plugin\PaymentFranchiseContext"/>
    </type>

    <type name="Magento\QuoteGraphQl\Model\Resolver\SetPaymentMethodOnCart">
        <plugin name="setFranchiseContext" type="EatLf\FranchiseGraphQl\Plugin\SelectPaymentFranchiseContext"/>
    </type>

    <type name="Magento\QuoteGraphQl\Model\Resolver\PlaceOrder">
        <plugin name="setFranchiseContext" type="EatLf\FranchiseGraphQl\Plugin\PlaceOrderFranchiseContext"/>
    </type>
</config>

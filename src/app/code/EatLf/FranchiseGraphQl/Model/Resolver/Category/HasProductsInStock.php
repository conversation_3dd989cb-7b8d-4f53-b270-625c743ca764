<?php
declare(strict_types=1);

namespace EatLf\FranchiseGraphQl\Model\Resolver\Category;

use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;

class HasProductsInStock implements ResolverInterface
{
    /**
     * @var \Magento\QuoteGraphQl\Model\Cart\GetCartForUser
     */
    private GetCartForUser $getCartForUser;
    /**
     * @var \Magento\Catalog\Model\Product\Visibility
     */
    private Visibility $catalogProductVisibility;

    public function __construct(GetCartForUser $getCartForUser, Visibility $catalogProductVisibility)
    {
        $this->getCartForUser = $getCartForUser;
        $this->catalogProductVisibility = $catalogProductVisibility;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($value['model'])) {
            throw new GraphQlInputException(__('"model" value should be specified'));
        }

        if (!isset($args['cartId'])) {
            throw new GraphQlInputException(__('"cartId" parameter is missing'));
        }

        $currentUserId = $context->getUserId();
        $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();

        $cart = $this->getCartForUser->execute($args['cartId'], $currentUserId, $storeId);
        $franchiseId = $cart->getExtensionAttributes()->getShippingData()->getFranchiseId();
        $shippingDate = $cart->getExtensionAttributes()->getShippingData()->getShippingDate();

        /** @var Category $category */
        $category = $value['model'];
        $productsCollection = $category->getProductCollection();

        $productsCollection
            ->addFieldToFilter('is_accessory', 0)
            ->addFieldToFilter('is_active_eatlf', Status::STATUS_ENABLED)
            ->setVisibility($this->catalogProductVisibility->getVisibleInSiteIds());

        $productsCollection
            ->getSelect()
            ->join(['fp' => 'franchise_product'], 'fp.product_id = e.entity_id', [])
            ->join(['ds' => 'daily_stock'], 'ds.product_id = e.entity_id')
            ->where('fp.is_active_fo = 1')
            ->where('ds.stock > 0')
            ->where('ds.franchise_id = ?', $franchiseId)
            ->where('ds.date = ?', $shippingDate);

        return $productsCollection->getSize() > 0;
    }
}
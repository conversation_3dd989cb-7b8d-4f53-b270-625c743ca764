<?php
declare(strict_types=1);

namespace EatLf\FranchiseGraphQl\Model\Api\SearchCriteria\CollectionProcessor\FilterProcessor;

use EatLf\Shipping\Helper\Shipping;
use Magento\Framework\Api\Filter;
use Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor\CustomFilterInterface;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\MaskedQuoteIdToQuoteIdInterface;

class AccessoryFilter implements CustomFilterInterface
{
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;
    /**
     * @var \EatLf\Shipping\Helper\Shipping
     */
    private Shipping $shippingHelper;
    /**
     * @var \Magento\Quote\Model\MaskedQuoteIdToQuoteIdInterface
     */
    private MaskedQuoteIdToQuoteIdInterface $maskedQuoteIdToQuoteId;

    public function __construct(
        CartRepositoryInterface $cartRepository,
        Shipping $shippingHelper,
        MaskedQuoteIdToQuoteIdInterface $maskedQuoteIdToQuoteId
    ) {
        $this->cartRepository = $cartRepository;
        $this->shippingHelper = $shippingHelper;
        $this->maskedQuoteIdToQuoteId = $maskedQuoteIdToQuoteId;
    }

    public function apply(Filter $filter, AbstractDb $collection)
    {
        try {
            $cartId = $this->maskedQuoteIdToQuoteId->execute(
                $filter->getValue()
            );

            $cart = $this->cartRepository->get($cartId);
        } catch (NoSuchEntityException $e) {
            return true;
        }

        $shippingData = $this->shippingHelper->getShippingDatas($cart);

        if (empty($shippingData->getFranchiseId()) || empty($shippingData->getShippingDate())) {
            return true;
        }

        $collection
            ->getSelect()
            ->join(
                ['fp' => 'franchise_product'],
                'e.entity_id = fp.product_id',
                ['franchise_price' => 'price', 'franchise_special_price' => 'special_price']
            )
            ->where('fp.franchise_id = ?', $shippingData->getFranchiseId())
            ->where('fp.is_active_fo = 1');

        return true;
    }
}
<?php
declare(strict_types=1);

namespace EatLf\FranchiseGraphQl\Plugin;

use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Lf\Franchise\Model\FranchiseContext;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;
use Magento\QuoteGraphQl\Model\Resolver\SetPaymentMethodOnCart;
use Psr\Log\LoggerInterface;

class SelectPaymentFranchiseContext
{
    /**
     * @var \Lf\Franchise\Model\FranchiseContext
     */
    private FranchiseContext $franchiseContext;
    /**
     * @var \Lf\Franchise\Api\FranchiseRepositoryInterface
     */
    private FranchiseRepositoryInterface $franchiseRepository;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    private LoggerInterface $logger;
    /**
     * @var \Magento\QuoteGraphQl\Model\Cart\GetCartForUser
     */
    private GetCartForUser $getCartForUser;

    public function __construct(
        FranchiseContext $franchiseContext,
        FranchiseRepositoryInterface $franchiseRepository,
        GetCartForUser $getCartForUser,
        LoggerInterface $logger
    ) {
        $this->franchiseContext = $franchiseContext;
        $this->franchiseRepository = $franchiseRepository;
        $this->logger = $logger;
        $this->getCartForUser = $getCartForUser;
    }

    public function beforeResolve(
        SetPaymentMethodOnCart $subject,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $cartId = $args['input']['cart_id'];

        if (empty($cartId)) {
            return null;
        }

        try {
            $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();
            $cart = $this->getCartForUser->execute($cartId, $context->getUserId(), $storeId);
            $franchiseId = $cart->getExtensionAttributes()->getShippingData()->getFranchiseId();
            $franchise = $this->franchiseRepository->getById($franchiseId);

            $this->franchiseContext->setFranchise($franchise);
        } catch (NoSuchEntityException $e) {
            $this->logger->error('Could not find franchise id ' . $franchiseId . ' for cartId ' . $cart->getId());
        }

        return null;
    }
}

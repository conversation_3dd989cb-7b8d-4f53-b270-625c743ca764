<?php
declare(strict_types=1);

namespace EatLf\FranchiseGraphQl\Plugin;

use Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\ProductSearch\ProductCollectionSearchCriteriaBuilder as Subject;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupBuilder;
use Magento\Framework\Api\SearchCriteriaInterface;

class ProductCollectionSearchCriteriaBuilder
{
    /**
     * @var \Magento\Framework\Api\FilterBuilder
     */
    private FilterBuilder $filterBuilder;
    /**
     * @var \Magento\Framework\Api\Search\FilterGroupBuilder
     */
    private FilterGroupBuilder $filterGroupBuilder;

    public function __construct(
        FilterBuilder $filterBuilder,
        FilterGroupBuilder $filterGroupBuilder
    ) {
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilder = $filterGroupBuilder;
    }

    /**
     * Add franchise filter to db search criteria
     *
     * @param \Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\ProductSearch\ProductCollectionSearchCriteriaBuilder $subject
     * @param $result
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     *
     * @return \Magento\Framework\Api\SearchCriteriaInterface
     */
    public function afterBuild(
        Subject $subject,
        $result,
        SearchCriteriaInterface $searchCriteria
    ): SearchCriteriaInterface {
        $isAccessoryRequest = false;

        foreach ($searchCriteria->getFilterGroups() as $filterGroup) {
            foreach ($filterGroup->getFilters() as $filter) {
                if ($filter->getField() == 'is_accessory' && $filter->getValue() == 1) {
                    $isAccessoryRequest = true;
                }
            }
        }

        $franchiseFilter = null;

        foreach ($searchCriteria->getFilterGroups() as $filterGroup) {
            foreach ($filterGroup->getFilters() as $filter) {
                if ($filter->getField() == 'cart_id') {
                    if ($isAccessoryRequest) {
                        $franchiseFilter = $this->filterBuilder
                            ->setField('accessory')
                            ->setValue($filter->getValue())
                            ->setConditionType($filter->getConditionType())
                            ->create();
                    } else {
                        $franchiseFilter = $this->filterBuilder
                            ->setField('cart_id')
                            ->setValue($filter->getValue())
                            ->setConditionType($filter->getConditionType())
                            ->create();
                    }
                }
            }
        }

        if ($franchiseFilter !== null) {
            $this->filterGroupBuilder->addFilter($franchiseFilter);
            $franchiseGroup = $this->filterGroupBuilder->create();

            $filterGroups = $result->getFilterGroups();
            $filterGroups[] = $franchiseGroup;
            $result->setFilterGroups($filterGroups);
        }

        return $result;
    }
}

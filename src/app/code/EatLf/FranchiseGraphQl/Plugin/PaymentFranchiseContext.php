<?php
declare(strict_types=1);

namespace EatLf\FranchiseGraphQl\Plugin;

use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Lf\Franchise\Model\FranchiseContext;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\QuoteGraphQl\Model\Resolver\AvailablePaymentMethods;
use Psr\Log\LoggerInterface;

class PaymentFranchiseContext
{
    /**
     * @var \Lf\Franchise\Model\FranchiseContext
     */
    private FranchiseContext $franchiseContext;
    /**
     * @var \Lf\Franchise\Api\FranchiseRepositoryInterface
     */
    private FranchiseRepositoryInterface $franchiseRepository;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    private LoggerInterface $logger;

    public function __construct(
        FranchiseContext $franchiseContext,
        FranchiseRepositoryInterface $franchiseRepository,
        LoggerInterface $logger
    ) {
        $this->franchiseContext = $franchiseContext;
        $this->franchiseRepository = $franchiseRepository;
        $this->logger = $logger;
    }

    public function beforeResolve(
        AvailablePaymentMethods $subject,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (!isset($value['model'])) {
            return null;
        }

        /** @var \Magento\Quote\Api\Data\CartInterface $cart */
        $cart = $value['model'];

        try {
            $franchiseId = $cart->getExtensionAttributes()->getShippingData()->getFranchiseId();
            $franchise = $this->franchiseRepository->getById($franchiseId);

            $this->franchiseContext->setFranchise($franchise);
        } catch (NoSuchEntityException $e) {
            $this->logger->error('Could not find franchise id ' . $franchiseId . ' for cartId ' . $cart->getId());
        }

        return null;
    }
}
<?php
declare(strict_types=1);

namespace EatLf\FranchiseGraphQl\Setup\Patch\Data;

use Magento\Cms\Api\BlockRepositoryInterface;
use Magento\Cms\Api\Data\BlockInterfaceFactory;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Store\Model\Store;

class InitAppCms implements DataPatchInterface
{
    private const BLOCKS = [
        'mobile-splash-title' => '[Mobile] Splash - title',
        'mobile-splash-subtitle' => '[Mobile] Splash - subtitle',
        'mobile-home-text' => '[Mobile] Home text',
        'mobile-home-image' => '[Mobile] Home image',
        'mobile-home-bg' => '[Mobile] Home bg',
        'mobile-partenaires-howto' => '[Mobile] Partenaires - Howto',
        'mobile-partenaires-noresult' => '[Mobile] Partenaires - Pas de résultat',
        'mobile-partenaires-create-post-image' => '[Mobile] Partenaires - Création image',
        'mobile-partenaires-create-post-text' => '[Mobile] Partenaires - Création texte',
        'mobile-partenaires-create-post-promise' => '[Mobile] Partenaires - Création confirmation',
        'mobile-categories-image' => '[Mobile] Catégories - image',
        'mobile-categories-text' => '[Mobile] Catégories - text',
        'mobile-categories-carousel1' => '[Mobile] Catégories - Slideshow 1',
        'mobile-categories-carousel2' => '[Mobile] Catégories - Slideshow 2',
        'mobile-categories-carousel3' => '[Mobile] Catégories - Slideshow 3',
        'mobile-order-confirmation-bg' => '[Mobile] Order confirmation bg',
    ];

    /**
     * @var \Magento\Cms\Api\BlockRepositoryInterface
     */
    private BlockRepositoryInterface $blockRepository;
    /**
     * @var \Magento\Cms\Api\Data\BlockInterfaceFactory
     */
    private BlockInterfaceFactory $blockFactory;

    public function __construct(
        BlockRepositoryInterface $blockRepository,
        BlockInterfaceFactory $blockFactory
    ) {
        $this->blockRepository = $blockRepository;
        $this->blockFactory = $blockFactory;
    }


    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }

    public function apply()
    {
        foreach (InitAppCms::BLOCKS as $blockId => $blockName) {
            $block = $this->blockFactory
                ->create()
                ->setIdentifier($blockId)
                ->setTitle($blockName)
                ->setContent('TODO')
                ->setData('stores', [Store::DEFAULT_STORE_ID]);

            $this->blockRepository->save($block);
        }
    }
}
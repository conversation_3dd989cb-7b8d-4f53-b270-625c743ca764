<?php
declare(strict_types=1);

namespace EatLf\FranchiseGraphQl\Setup\Patch\Data;

use Magento\Cms\Api\BlockRepositoryInterface;
use Magento\Cms\Api\Data\BlockInterfaceFactory;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Store\Model\Store;

class AppContactCms implements DataPatchInterface
{
    private const BLOCKS = [
        'mobile-contact-post-image' => '[Mobile] Contact - image',
        'mobile-contact-post-text' => '[Mobile] Contact - texte',
    ];

    /**
     * @var \Magento\Cms\Api\BlockRepositoryInterface
     */
    private BlockRepositoryInterface $blockRepository;
    /**
     * @var \Magento\Cms\Api\Data\BlockInterfaceFactory
     */
    private BlockInterfaceFactory $blockFactory;

    public function __construct(
        BlockRepositoryInterface $blockRepository,
        BlockInterfaceFactory $blockFactory
    ) {
        $this->blockRepository = $blockRepository;
        $this->blockFactory = $blockFactory;
    }


    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }

    public function apply()
    {
        foreach (AppContactCms::BLOCKS as $blockId => $blockName) {
            $block = $this->blockFactory
                ->create()
                ->setIdentifier($blockId)
                ->setTitle($blockName)
                ->setContent('TODO')
                ->setData('stores', [Store::DEFAULT_STORE_ID]);

            $this->blockRepository->save($block);
        }
    }
}
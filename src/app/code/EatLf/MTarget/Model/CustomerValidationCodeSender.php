<?php

declare(strict_types=1);

namespace EatLf\MTarget\Model;

use EatLf\Customer\Api\PhoneNumberValidatorInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;

class CustomerValidationCodeSender implements PhoneNumberValidatorInterface
{
    public function __construct(
        private readonly MTargetSmsClient $smsClient,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    public function sendValidationCode(string $customerId, string $validationCode): void
    {
        $customer = $this->customerRepository->getById($customerId);

        $telephone = $customer->getCustomAttribute('telephone')->getValue();

        if (substr($telephone, 0, 3) == "+32") {
            $countryCode = "BE";
        } else {
            $countryCode = "FR";
        }

        $text = str_replace(
            '%code%',
            $validationCode,
            $this->scopeConfig->getValue('customer/create_account/sms_text')
        );

        $from = $this->scopeConfig->getValue('customer/create_account/sms_from');

        $this->smsClient->sendSms($text, $from, $telephone, $countryCode);
    }
}

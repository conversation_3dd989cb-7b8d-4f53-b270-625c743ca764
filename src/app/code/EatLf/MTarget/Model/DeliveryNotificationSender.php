<?php

declare(strict_types=1);

namespace EatLf\MTarget\Model;

use EatLf\Livraison\Api\DeliveryNotificationSenderInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class DeliveryNotificationSender implements DeliveryNotificationSenderInterface
{
    private const SMS_TEXT_KEY = 'eat_livraison/general/in_progress_sms_text';

    private const SMS_FROM_KEY = 'eat_livraison/general/in_progress_sms_from';

    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly MTargetSmsClient $smsClient,
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    public function sendDeliveryNotification(string $orderId): void
    {
        $order = $this->orderRepository->get($orderId);

        $telephone = $order->getShippingAddress()->getTelephone();

        if (substr($telephone, 0, 3) == "+32") {
            $countryCode = "BE";
        } else {
            $countryCode = "FR";
        }

        $smsFrom = $this->scopeConfig->getValue(self::SMS_FROM_KEY, 'store');
        $smsText = $this->scopeConfig->getValue(self::SMS_TEXT_KEY, 'store');

        try {
            $this->smsClient->sendSms($smsText, $smsFrom, $telephone, $countryCode);
        } catch (\Exception) {
            // Trap all exceptions to respect the DeliveryNotificationSenderInterface contract.
        }
    }
}

input ContactInput @doc(description: "Contact contains all contact form datas") {
    firstName: String
    lastName: String
    email: String
    company: String
    city: String
    telephone: String
    comment: String
    locality: String
}

type Mutation {
    sendContact(
        emailInput: ContactInput
    ): <PERSON><PERSON><PERSON>

    @resolver(class: "EatLf\\ContactGraphQl\\Model\\Resolver\\SendEmail")

    @doc(description:
    "Send Email with contact form informations"
    )

    @cache(cacheable: false)
}

type Query {
    getLocationInfo: [location]

    @resolver(class:"EatLf\\ContactGraphQl\\Model\\Resolver\\Location")

    @doc(description:
        "Get location list"
    )

    @cache(cacheable: false)
}

type location {
    value: Int
    label: String
}

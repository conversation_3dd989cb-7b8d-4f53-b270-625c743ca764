<?php

namespace EatLf\SalesShipping\Api;

use EatLf\Partenaire\Api\Data\PartenaireInterface;
use Magento\Framework\Exception\NoSuchEntityException;

interface SalesManagementInterface
{
    /**
     * Returns the partenaire selected for the last customer order
     *
     * @param int $customerId
     * @return PartenaireInterface|null
     * @throws NoSuchEntityException
     */
    public function getPartenaireForLastOrder($customerId);
}
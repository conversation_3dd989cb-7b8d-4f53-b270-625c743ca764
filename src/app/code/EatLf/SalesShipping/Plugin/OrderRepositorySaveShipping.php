<?php
declare(strict_types=1);

namespace EatLf\SalesShipping\Plugin;

use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderSearchResultInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class OrderRepositorySaveShipping
{
    /**
     * Saves the partenaire id and shipping date to sales_order table
     *
     * @param OrderRepositoryInterface $orderRepository
     * @param OrderInterface $order
     * @return null
     */
    public function beforeSave(OrderRepositoryInterface $orderRepository, OrderInterface $order)
    {
        $orderExtension = $order->getExtensionAttributes();

        if ($orderExtension === null) {
            return null;
        }

        $order->setData('partenaire_id', $orderExtension->getPartenaireId());
        $order->setData('shipping_date', $orderExtension->getShippingDate());

        return null;
    }

    /**
     * Copies partenaire_id and shipping date to extension attributes
     *
     * @param OrderRepositoryInterface $orderRepository
     * @param OrderInterface $order
     * @return OrderInterface
     */
    public function afterGet(OrderRepositoryInterface $orderRepository, $order)
    {
        $orderExtension = $order->getExtensionAttributes();

        if ($orderExtension === null) {
            return $order;
        }

        $orderExtension
            ->setPartenaireId(
                $order->getData('partenaire_id')
            )
            ->setShippingDate(
                $order->getData('shipping_date')
            );

        return $order;
    }

    /**
     * Copies partenaire_id and shipping date to extension attributes
     *
     * @param OrderRepositoryInterface $orderRepository
     * @param OrderSearchResultInterface $result
     * @return OrderSearchResultInterface
     */
    public function afterGetList(OrderRepositoryInterface $orderRepository, OrderSearchResultInterface $result)
    {
        foreach ($result->getItems() as $order)
        {
            $order
                ->getExtensionAttributes()
                ->setPartenaireId(
                    $order->getData('partenaire_id')
                )
                ->setShippingDate(
                    $order->getData('shipping_date')
                );
        }

        return $result;
    }
}
<?php
declare(strict_types=1);

namespace EatLf\SalesShipping\Model;

use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\SalesShipping\Api\SalesManagementInterface;
use EatLf\Sales\Api\CustomerSalesInterface;

class SalesManagement implements SalesManagementInterface
{
    /**
     * @var PartenaireRepositoryInterface
     */
    private $partenaireRepository;
    /**
     * @var CustomerSalesInterface
     */
    private $customerSales;

    public function __construct(
        PartenaireRepositoryInterface $partenaireRepository,
        CustomerSalesInterface $customerSales
    )
    {
        $this->partenaireRepository = $partenaireRepository;
        $this->customerSales = $customerSales;
    }

    /**
     * {@inheritDoc}
     */
    public function getPartenaireForLastOrder($customerId)
    {
        $order = $this->customerSales->getLastCustomerOrder($customerId);

        return $this->partenaireRepository->getById(
            $order->getExtensionAttributes()->getPartenaireId()
        );
    }
}
<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <title>Create New Account</title>
    </head>
    <body>
        <referenceContainer name="content">
            <block class="EatLf\Customer\Block\Account\Validate" name="customer_form_validate_phone" template="EatLf_Customer::form/validate.phtml" cacheable="false">
                <action method="setTypeFormulaire">
                    <argument name="type_formulaire" xsi:type="string">create</argument>
                </action>
            </block>
        </referenceContainer>
    </body>
</page>

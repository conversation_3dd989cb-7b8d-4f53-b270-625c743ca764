<?php

namespace EatLf\Customer\Plugin;

use Magento\Customer\Model\Customer;
use Magento\Backend\Model\Auth\Session;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory;

class FranchiseFilterGridCollection
{

    /**
     * @var Session
     */
    private $authSession;

    private $messageManager;

    /**
     * @var EavConfig
     */
    private $eavConfig;


    public function __construct(
        Session $authSession,
        EavConfig $eavConfig
    ) {
        $this->authSession = $authSession;
        $this->eavConfig= $eavConfig;
    }

    public function afterGetReport(
        CollectionFactory $subject,
        $result,
        $requestName
    ) {
        /**
         * customer_listing_data_source
         */
        if ($requestName == 'customer_listing_data_source') {

            $franchiseLiesAttributeId = $this->eavConfig
                ->getAttribute(Customer::ENTITY, 'franchises_lies')
                ->getAttributeId();

            if ($result instanceof \Magento\Customer\Model\ResourceModel\Grid\Collection)
            {

                $currentUser = $this->authSession->getUser();

                if($currentUser->getFranchiseId()!="")
                {
                    $select = $result->getSelect();
                    $select
                        ->joinLeft(
                            ['customer_entity_text' => $result->getTable('customer_entity_text')],
                            "main_table.entity_id = customer_entity_text.entity_id and customer_entity_text.attribute_id = ".$franchiseLiesAttributeId,
                            ["customer_franchise_ids" => "customer_entity_text.value"]
                        );
                    $select->where('FIND_IN_SET ('.$currentUser->getFranchiseId().', customer_entity_text.value)');
                }
            }
        }
        return $result;
    }
}
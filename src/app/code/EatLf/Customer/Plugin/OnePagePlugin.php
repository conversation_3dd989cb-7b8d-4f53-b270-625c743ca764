<?php

namespace EatLf\Customer\Plugin;

use EatLf\Customer\Model\Service\Telephone;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Checkout\Controller\Index\Index;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\State\InputMismatchException;


class OnePagePlugin
{

    /**
     * @var Telephone $phoneService
     */
    private Telephone $phoneService;
    /**
     * @var RedirectFactory $resultRedirectFactory
     */
    private RedirectFactory $resultRedirectFactory;

    public function __construct(
        Telephone $phoneService,
        Context $context
    ) {
        $this->phoneService = $phoneService;
        $this->resultRedirectFactory = $context->getResultRedirectFactory();
    }

    /**
     * @param Index $subject
     * @param ResultInterface $result
     * @return Redirect|ResultInterface
     * @throws InputException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @throws InputMismatchException
     */
    public function afterExecute(
        Index $subject,
        ResultInterface $result
    ) {
        switch ($this->phoneService->isPhoneValidOrAlreadyUsed(true)) {
            case Telephone::RETURN_TYPE_VALID:
                break;
            case Telephone::RETURN_TYPE_TO_VALIDATE:
                return $this->resultRedirectFactory->create()->setPath('customer/account/validate');
                break;
            case Telephone::RETURN_TYPE_TO_CHANGE:
                $this->_messageManager->addError(__('There is already a customer account with this number phone. Change your phone number to validate your account.'));
                return $this->resultRedirectFactory->create()->setPath('customer/account/edit');
                break;
        }
        return $result;
    }

}

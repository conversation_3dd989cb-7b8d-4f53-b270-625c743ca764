<?php

namespace EatLf\Customer\Plugin;

use EatLf\Customer\Model\Service\Telephone;
use Magento\Customer\Controller\Account\LoginPost;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\State\InputMismatchException;
use Magento\Framework\Message\ManagerInterface;

class LoginPlugin
{

    /**
     * @var Telephone $phoneService
     */
    private Telephone $phoneService;
    /**
     * @var ManagerInterface $_messageManager
     */
    private ManagerInterface $_messageManager;

    public function __construct(
        Telephone $phoneService,
        ManagerInterface $messageManager
    ) {
        $this->phoneService = $phoneService;
        $this->_messageManager = $messageManager;
    }

    /**
     * @param LoginPost $subject
     * @param $result
     * @return mixed
     * @throws InputException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @throws InputMismatchException
     */
    public function afterExecute(
        LoginPost $subject,
        $result
    ) {
        switch ($this->phoneService->isPhoneValidOrAlreadyUsed(true)) {
            case Telephone::RETURN_TYPE_VALID:
            break;
            case Telephone::RETURN_TYPE_TO_VALIDATE:
                $result->setPath('customer/account/validate');
            break;
            case Telephone::RETURN_TYPE_TO_CHANGE:
                $this->_messageManager->addError(__('There is already a customer account with this number phone. Change your phone number to validate your account.'));
                $result->setPath('customer/account/edit');
            break;
        }
        return $result;
    }

}

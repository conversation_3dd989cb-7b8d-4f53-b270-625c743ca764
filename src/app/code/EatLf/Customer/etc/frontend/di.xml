<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Customer\Block\Widget\Telephone"
                type="EatLf\Customer\Rewrite\Block\Widget\Telephone" />

    <preference for="Magento\Customer\Block\Address\Book"
                type="EatLf\Customer\Rewrite\Block\Address\Book" />

    <type name="\Magento\Customer\Controller\Account\EditPost">
        <plugin name="EatLf_customer_editpostplugin" type="\EatLf\Customer\Plugin\EditPostPlugin" sortOrder="1" />
    </type>
    <type name="\Magento\Customer\Controller\Account\CreatePost">
        <plugin name="EatLf_customer_createpostplugin" type="\EatLf\Customer\Plugin\CreatePostPlugin" sortOrder="1" />
    </type>
    <type name="\Magento\Customer\Controller\Account\LoginPost">
        <plugin name="EatLf_customer_loginpostplugin" type="\EatLf\Customer\Plugin\LoginPlugin" sortOrder="2" />
    </type>
    <type name="\Magento\Checkout\Controller\Cart\Index">
        <plugin name="EatLf_customer_cartplugin" type="\EatLf\Customer\Plugin\CartPlugin" sortOrder="1" />
    </type>
    <type name="Magento\Customer\Api\CustomerRepositoryInterface">
        <plugin name="Eatlf_customer_saveplugin" type="\EatLf\Customer\Plugin\BeforeSavePlugin" sortOrder="2" />
    </type>

</config>

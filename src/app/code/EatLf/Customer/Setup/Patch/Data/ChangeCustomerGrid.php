<?php
declare(strict_types=1);

namespace EatLf\Customer\Setup\Patch\Data;

use Exception;
use Magento\Customer\Model\Customer;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Indexer\Model\IndexerFactory;

class ChangeCustomerGrid implements DataPatchInterface
{

    /**
     * @var ModuleDataSetupInterface $moduleDataSetup
     */
    private ModuleDataSetupInterface $moduleDataSetup;
    /**
     * @var CustomerSetupFactory $customerSetupFactory
     */
    private CustomerSetupFactory $customerSetupFactory;
    /**
     * @var IndexerFactory  $indexerFactory
     */
    private IndexerFactory $indexerFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        CustomerSetupFactory $customerSetupFactory,
        IndexerFactory $indexerFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->customerSetupFactory = $customerSetupFactory;
        $this->indexerFactory = $indexerFactory;
    }

    /**
     * @return ChangeCustomerGrid|void
     * @throws Exception
     */
    public function apply()
    {
        $this->moduleDataSetup->startSetup();

        $customerSetup = $this->customerSetupFactory->create();

        $properties = [
            'is_used_in_grid',
            'is_visible_in_grid',
            'is_filterable_in_grid',
            'is_searchable_in_grid',
        ];

        foreach ($properties as $property) {
            $customerSetup->updateAttribute(
                Customer::ENTITY,
                'telephone',
                $property,
                1
            );
        }

        foreach ($properties as $property) {
            $customerSetup->updateAttribute(
                Customer::ENTITY,
                'numero_mobile',
                $property,
                0
            );
        }
        $index = $this->indexerFactory->create()->load('customer_grid');
        $index->reindexAll();
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}

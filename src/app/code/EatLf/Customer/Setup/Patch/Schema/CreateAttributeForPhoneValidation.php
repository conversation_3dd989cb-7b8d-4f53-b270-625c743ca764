<?php
declare(strict_types=1);

namespace EatLf\Customer\Setup\Patch\Schema;

use Magento\Customer\Model\Customer;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Eav\Model\Entity\Attribute\SetFactory as AttributeSetFactory;
use Magento\Framework\Setup\Patch\SchemaPatchInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;

class CreateAttributeForPhoneValidation implements SchemaPatchInterface
{
    /**
     * @var SchemaSetupInterface $schemaSetup
     */
    private SchemaSetupInterface $schemaSetup;
    /**
     * @var CustomerSetupFactory $customerSetupFactory
     */
    private CustomerSetupFactory $customerSetupFactory;
    /**
     * @var AttributeSetFactory $attributeSetFactory
     */
    private AttributeSetFactory $attributeSetFactory;
    /**
     * @var CustomerRepositoryInterface $customerRepository
     */
    private CustomerRepositoryInterface $customerRepository;

    /**
     * @param SchemaSetupInterface $schemaSetup
     * @param CustomerSetupFactory $customerSetupFactory
     * @param AttributeSetFactory $attributeSetFactory
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        SchemaSetupInterface $schemaSetup,
        CustomerSetupFactory $customerSetupFactory,
        AttributeSetFactory $attributeSetFactory,
        CustomerRepositoryInterface $customerRepository
    ) {
        $this->schemaSetup = $schemaSetup;
        $this->customerSetupFactory = $customerSetupFactory;
        $this->attributeSetFactory = $attributeSetFactory;
        $this->customerRepository = $customerRepository;
    }

    /**
     * @return CreateAttributeForPhoneValidation
     */
    public function apply(): CreateAttributeForPhoneValidation
    {
        $this->schemaSetup->startSetup();

        $customerSetup = $this->customerSetupFactory->create();
        $customerEntity = $customerSetup->getEavConfig()->getEntityType('customer');
        $attributeSetId = $customerEntity->getDefaultAttributeSetId();

        $attributeSet = $this->attributeSetFactory->create();
        $attributeGroupId = $attributeSet->getDefaultGroupId($attributeSetId);

        $customerSetup->addAttribute(
            Customer::ENTITY,
            'validation_code',
            [
                'type' => 'varchar',
                'label' => 'Code de validation',
                'input' => 'hidden',
                'required' => false,
                'sort_order' => 135,
                'visible' => false,
                'position' => 135,
                'user_defined' => true,
                'system' => false,
            ]
        );
        $validationCode = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'validation_code');
        $validationCode->addData(
            [
                'used_in_forms' => null,
                'attribute_set_id' => $attributeSetId,
                'attribute_group_id' => $attributeGroupId,
            ]
        );
       $validationCode->save();

        $customerSetup->addAttribute(
            Customer::ENTITY,
            'validation_code_sent_date',
            [
                'type' => 'datetime',
                'label' => 'Code de validation sent date',
                'input' => 'date',
                'required' => false,
                'sort_order' => 136,
                'visible' => false,
                'position' => 136,
                'user_defined' => true,
                'system' => false,
            ]
        );
        $validationCodeSentDate = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'validation_code_sent_date');
        $validationCodeSentDate->addData(
            [
                'used_in_forms' => null,
                'attribute_set_id' => $attributeSetId,
                'attribute_group_id' => $attributeGroupId,
            ]
        );
        $validationCodeSentDate->save();


        $customerSetup->addAttribute(
            Customer::ENTITY,
            'validation_code_sent_count',
            [
                'type' => 'int',
                'label' => 'Code de validation sent count',
                'input' => 'text',
                'required' => false,
                'sort_order' => 137,
                'visible' => false,
                'position' => 137,
                'user_defined' => true,
                'system' => false,
            ]
        );
        $validationCodeSentCount = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'validation_code_sent_count');
        $validationCodeSentCount->addData(
            [
                'used_in_forms' => null,
                'attribute_set_id' => $attributeSetId,
                'attribute_group_id' => $attributeGroupId,
            ]
        );
        $validationCodeSentCount->save();


        $customerSetup->addAttribute(
            Customer::ENTITY,
            'phone_validated',
            [
                'type' => 'int',
                'backend_model' => 'Magento\Customer\Model\Attribute\Backend\Data\Boolean',
                'label' => 'is phone number validate',
                'input' => 'select',
                'required' => true,
                'default' => 0,
                'sort_order' => 129,
                'visible' => true,
                'position' => 129,
                'user_defined' => true,
                'system' => false,
                'source' => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
            ]
        );
        $validate = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'phone_validated');
        $validate->addData(
            [
                'used_in_forms' => ['adminhtml_customer'],
                'attribute_set_id' => $attributeSetId,
                'attribute_group_id' => $attributeGroupId,
            ]
        );
        $validate->save();


        $this->schemaSetup->endSetup();
        return $this;
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}

<?php
declare(strict_types=1);

namespace EatLf\Customer\Observer;

use EatLf\Customer\Model\Cookies\Id;
use Magento\Customer\Model\Customer;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Stdlib\Cookie\CookieSizeLimitReachedException;
use Magento\Framework\Stdlib\Cookie\FailureToSendException;

class SetCustomerIdCookie implements ObserverInterface
{
    /**
     * @var Id
     */
    private $idCookie;

    public function __construct(Id $idCookie)
    {
        $this->idCookie = $idCookie;
    }

    /**
     * Sets the customer id in a public cookie to be able to identify when disconnected
     *
     * DO NOT USE TO DISPLAY / MODIFY PRIVATE DATA
     *
     * @param Observer $observer
     * @return void
     * @throws InputException
     * @throws CookieSizeLimitReachedException
     * @throws FailureToSendException
     */
    public function execute(Observer $observer)
    {
        /** @var Customer $model */
        $model = $observer->getData('model');

        $this->idCookie->set($model->getId());
    }
}
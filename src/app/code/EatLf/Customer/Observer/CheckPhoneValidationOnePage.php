<?php

namespace EatLf\Customer\Observer;

use EatLf\Customer\Model\Service\Telephone;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\View\Element\Template\Context AS Scope;

class CheckPhoneValidationOnePage implements ObserverInterface
{
    /**
     * @var Telephone $phoneService
     */
    private Telephone $phoneService;
    /**
     * @var RedirectFactory $resultRedirectFactory
     */
    private RedirectFactory $resultRedirectFactory;
    private $scopeConfig;

    public function __construct(
        Telephone $phoneService,
        Context $context,
        Scope $contextScope
    ) {
        $this->scopeConfig = $contextScope->getScopeConfig();
        $this->phoneService = $phoneService;
        $this->resultRedirectFactory = $context->getResultRedirectFactory();
    }

    public function execute(Observer $observer)
    {
        if ($this->scopeConfig->getValue('customer/create_account/validate_phone_enabled') != 1) {
            return;
        }
        if ($this->phoneService->needToValidatePhoneNumber()) {
            /** @var Action $controller */
            $controller = $observer->getControllerAction();
            $controller->getResponse()->setRedirect('/customer/account/validate');
        }
        return $this;

    }
}

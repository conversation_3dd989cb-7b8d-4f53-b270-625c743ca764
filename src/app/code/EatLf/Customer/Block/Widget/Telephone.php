<?php
declare(strict_types=1);

namespace EatLf\Customer\Block\Widget;

use Magento\Customer\Api\CustomerMetadataInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Block\Widget\AbstractWidget;
use <PERSON>gento\Customer\Block\Widget\Name;
use Magento\Customer\Helper\Address as AddressHelper;
use Magento\Customer\Model\Data\Customer;
use Magento\Customer\Model\Options;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\View\Element\Template\Context;

/**
 * @method CustomerInterface getObject()
 * @method Name setObject(CustomerInterface $customer)
 */
class Telephone extends AbstractWidget
{
    /**
     * the attribute code
     */
    public const ATTRIBUTE_CODE = 'telephone';

    /**
     * @var Options
     */
    protected Options $options;

    /**
     * @var ScopeConfigInterface $scopeConfig
     */
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var string $formulaireType
     */
    private string $formulaireType;

    /**
     * @param Context $context
     * @param AddressHelper $addressHelper
     * @param CustomerMetadataInterface $customerMetadata
     * @param Options $options
     * @param array $data
     */
    public function __construct(
        Context $context,
        AddressHelper $addressHelper,
        CustomerMetadataInterface $customerMetadata,
        Options $options,
        array $data = []
    ) {
        $this->options = $options;
        $this->scopeConfig = $context->getScopeConfig();
        $this->formulaireType = '';
        parent::__construct($context, $addressHelper, $customerMetadata, $data);
    }

    /**
     * @return void
     */
    public function _construct()
    {
        parent::_construct();
        $this->setTemplate('EatLf_Customer::widget/account-telephone.phtml');
    }

    /**
     * Retrieve store attribute label
     *
     * @return string
     */
    public function getStoreLabel()
    {
        $attribute = $this->_getAttribute(self::ATTRIBUTE_CODE);
        return $attribute ? __($attribute->getStoreLabel()) : '';
    }

    /**
     * Get string with frontend validation classes for attribute
     *
     * @param string $attributeCode
     *
     * @return string
     */
    public function getAttributeValidationClass()
    {
        $attributeMetadata = $this->_getAttribute(self::ATTRIBUTE_CODE);
        return $attributeMetadata ? $attributeMetadata->getFrontendClass() : '';
    }

    /**
     * Check if telephone attribute enabled in system
     *
     * @return bool
     */
    public function isEnabled()
    {
        return $this->_getAttribute(self::ATTRIBUTE_CODE) && $this->_getAttribute(
                self::ATTRIBUTE_CODE
            )->isVisible();
    }

    /**
     * Check if telephone attribute marked as required
     *
     * @return bool
     */
    public function isRequired()
    {
        return $this->_getAttribute(self::ATTRIBUTE_CODE) && $this->_getAttribute(self::ATTRIBUTE_CODE)
                ->isRequired();
    }

    public function getTelephone()
    {
        if ($this->getObject() instanceof Customer) {
            return $this->getObject()->getCustomAttribute(self::ATTRIBUTE_CODE)->getValue();
        }

        if ($this->getObject() !== null) {
            return $this->getObject()->getTelephone();
        }

        return '';
    }

    /**
     * @return string
     */
    public function getHelpText(): string
    {
        if($this->formulaireType == "creation") {
            return $this->scopeConfig->getValue('customer/create_account/account_creation_phone_tooltip');
        } else {
            return $this->scopeConfig->getValue('customer/create_account/account_edit_phone_tooltip');
        }
    }

    /**
     * @param $formulaireType
     * @return $this
     */
    public function setFormulaireType($formulaireType)
    {
        $this->formulaireType = $formulaireType;
        return $this;
    }
}

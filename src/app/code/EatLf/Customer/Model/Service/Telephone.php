<?php

namespace EatLf\Customer\Model\Service;

use EatLf\Customer\Api\PhoneNumberValidatorInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\Data\Customer as DataCustomer;
use Magento\Customer\Model\Session;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\State;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\State\InputMismatchException;
use Magento\Framework\View\Element\Template\Context;
use Psr\Log\LoggerInterface;

class Telephone
{
    const RETURN_TYPE_VALID = 0;
    const RETURN_TYPE_TO_VALIDATE = 1;
    const RETURN_TYPE_TO_CHANGE = 2;

    /**
     * @var Session $customerSession
     */
    private Session $customerSession;

    /**
     * @var ScopeConfigInterface $scopeConfig
     */
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var CustomerRepositoryInterface $customerRepository
     */
    private CustomerRepositoryInterface $customerRepository;
    /**
     * @var Session $session
     */
    private Session $session;
    /**
     * @var SearchCriteriaBuilder $searchCriteriaBuilder
     */
    private SearchCriteriaBuilder $searchCriteriaBuilder;
    /**
     * @var FilterBuilder $filterBuilder
     */
    private FilterBuilder $filterBuilder;

    /**
     * @var State
     */
    private State $state;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    private LoggerInterface $logger;

    private PhoneNumberValidatorInterface $phoneNumberValidator;

    public function __construct(
        Session $customerSession,
        PhoneNumberValidatorInterface $phoneNumberValidator,
        Context $context,
        CustomerRepositoryInterface $customerRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        State $state,
        LoggerInterface $logger
    ) {
        $this->customerSession = $customerSession;
        $this->phoneNumberValidator = $phoneNumberValidator;
        $this->scopeConfig = $context->getScopeConfig();
        $this->customerRepository = $customerRepository;
        $this->session = $customerSession;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->state = $state;
        $this->logger = $logger;
    }

    public function initData(DataCustomer $customer)
    {
        if (is_object($customer->getCustomAttribute('phone_validated')) && $this->state->getAreaCode() == "frontend") {
            $customer->setCustomAttribute('phone_validated', 0);
        }

        $customer->setCustomAttribute('validation_code_sent_count', 0);
        $customer->setCustomAttribute('validation_code', $this->getNewCode());
        $customer->setCustomAttribute('validation_code_sent_date', null);
    }

    /**
     * @return int
     */
    public function getNewCode(): int
    {
        return rand(1231, 9876);
    }

    /**
     * @param DataCustomer $originData
     * @param CustomerInterface $customer
     *
     * @return bool
     */
    public function phoneChanged(DataCustomer $originData, CustomerInterface $customer): bool
    {
        if ($originData->getCustomAttribute('telephone')->getValue() != $customer->getCustomAttribute(
                'telephone'
            )->getValue()) {
            return true;
        }
        return false;
    }

    /**
     * @param string $submittedCode
     *
     * @return bool
     * @throws InputException
     * @throws InputMismatchException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function validate(string $submittedCode): bool
    {
        $customer = $this->customerSession->getCustomer();
        if (!is_object($customer)) {
            return false;
        }
        $code = $customer->getValidationCode();

        if ($code == $submittedCode) {
            $this->validatePhoneNumber();
            return true;
        }
        return false;
    }

    /**
     * @return $this|false
     * @throws InputException
     * @throws InputMismatchException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    private function validatePhoneNumber()
    {
        $customer = $this->customerSession->getCustomer();
        if (!is_object($customer)) {
            return false;
        }
        $customer = $this->customerRepository->getById($customer->getEntityId());
        $customer->setCustomAttribute('phone_validated', 1);
        $this->customerRepository->save($customer);

        return $this;
    }

    /**
     * @return $this|false
     * @throws InputException
     * @throws InputMismatchException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function sendValidationCode()
    {
        if (!$this->session->isLoggedIn()) {
            return false;
        }

        $customer = $this->customerSession->getCustomer();
        if (!is_object($customer)) {
            return false;
        }

        $customer = $this->customerRepository->getById($customer->getEntityId());

        $code = "";
        if ($customer->getCustomAttribute('validation_code') !== null) {
            $code = $customer->getCustomAttribute('validation_code')->getValue();
        }
        if ($code == "") {
            $code = $this->getNewCode();
        }

        $this->phoneNumberValidator->sendValidationCode((string)$customer->getId(), $code);

        $customer->setCustomAttribute('validation_code_sent_date', date('Y-m-d H:i:s'));
        if ($customer->getCustomAttribute('validation_code_sent_count') === null) {
            $count = 1;
        } else {
            $count = (int)$customer->getCustomAttribute('validation_code_sent_count')->getValue() + 1;
        }
        $customer->setCustomAttribute('validation_code', $code);
        $customer->setCustomAttribute('validation_code_sent_count', $count);
        $this->customerRepository->save($customer);

        return $this;
    }

    /**
     * @return bool
     * @throws InputException
     * @throws InputMismatchException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function sendCodeAgain()
    {
        $customer = $this->customerSession->getCustomer();
        if (!is_object($customer)) {
            return false;
        }
        $customer = $this->customerRepository->getById($customer->getEntityId());
        $count = 0;
        if ($customer->getCustomAttribute('validation_code_sent_count') !== null) {
            $count = (int)$customer->getCustomAttribute('validation_code_sent_count')->getValue() + 1;
            if ($count > 2) {
                //check xxx minutes
                $lastSent = $customer->getCustomAttribute('validation_code_sent_date')->getValue();
                $lastSent = \DateTime::createFromFormat('Y-m-d H:i:s', $lastSent);
                $delay = $this->scopeConfig->getValue('customer/create_account/sms_resend_delay');
                $lastSent->add(new \DateInterval('PT' . $delay . 'M'));
                if ($lastSent->format('YmdHis') > date('YmdHis')) {
                    return false;
                }
            }
        }
        $this->sendValidationCode();
        return true;
    }

    public function isPhoneValidOrAlreadyUsed(bool $force = false)
    {
        if ($this->scopeConfig->getValue('customer/create_account/validate_phone_enabled') == 0 && $force) {
            return self::RETURN_TYPE_VALID;
        }
        if (!$this->session->isLoggedIn()) {
            return self::RETURN_TYPE_VALID;
        }
        $customer = $this->customerSession->getCustomer();
        if (!is_object($customer)) {
            return self::RETURN_TYPE_VALID;
        }
        if ($customer->getPhoneValidated() == 1) {
            return self::RETURN_TYPE_VALID;
        }
        if ($this->phoneNumberAlreadyUsed($customer->getTelephone())) {
            return self::RETURN_TYPE_TO_CHANGE;
        }

        try {
            $this->sendCodeAgain();
        } catch (\Exception $e) {
            $this->logger->warning('Skipping phone validation due to error ' . $e->getMessage(), $e->getTrace());
            return self::RETURN_TYPE_VALID;
        }

        return self::RETURN_TYPE_TO_VALIDATE;
    }

    public function phoneNumberAlreadyUsed($phoneNumber)
    {
        $phoneFilter = $this->filterBuilder->setField('telephone')->setConditionType('like')->setValue(
            '%' . substr($phoneNumber, -9)
        )->create();
        $filter = $this->searchCriteriaBuilder
            ->addFilters([$phoneFilter])
            ->addFilter('phone_validated', 1);

        $customer = $this->customerSession->getCustomer();
        if (is_object($customer) && $customer->getEntityId() != '') {
            $filter->addFilter('entity_id', $customer->getEntityId(), 'neq');
        }

        $customer = $this->customerRepository->getList($filter->create());
        if ($customer->getTotalCount() > 0) {
            return true;
        }
        return false;
    }

    /**
     * @param false $force
     *
     * @return bool
     * @throws InputException
     * @throws InputMismatchException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function needToValidatePhoneNumber(bool $force = false): bool
    {
        if ($this->scopeConfig->getValue('customer/create_account/validate_phone_enabled') == 0 && $force) {
            return false;
        }
        if (!$this->session->isLoggedIn()) {
            return false;
        }
        $customer = $this->customerSession->getCustomer();
        if (!is_object($customer)) {
            return false;
        }
        //need to reload customer now
        $customer = $this->customerRepository->getById($customer->getEntityId());
        if ($customer->getCustomAttribute('phone_validated') !== null && $customer->getCustomAttribute(
                'phone_validated'
            )->getValue() == 1) {
            return false;
        }

        try {
            $this->sendCodeAgain();
        } catch (\Exception $e) {
            $this->logger->warning('Skipping phone validation due to error ' . $e->getMessage(), $e->getTrace());
            return false;
        }

        return true;
    }

    /**
     * @param $phoneNumber
     *
     * @return bool
     * @throws LocalizedException
     */
    public function phoneNumberAlreadyExist($phoneNumber): bool
    {
        $phoneFilter = $this->filterBuilder->setField('telephone')->setConditionType('like')->setValue(
            '%' . substr($phoneNumber, -9)
        )->create();
        $filter = $this->searchCriteriaBuilder->addFilters([$phoneFilter]);

        $customer = $this->customerSession->getCustomer();
        if (is_object($customer)) {
            $filter->addFilter('entity_id', $customer->getEntityId(), 'neq');
        }

        $customer = $this->customerRepository->getList($filter->create());
        if ($customer->getTotalCount() > 0) {
            return true;
        }
        return false;
    }

    /**
     * @param $phoneNumber
     *
     * @return mixed
     */
    public function formatPhoneNumber($phoneNumber)
    {
        if (substr($phoneNumber, 0, 1) != "0") {
            return $phoneNumber;
        }
        $phoneNumber = "+33" . substr($phoneNumber, -9);
        return $phoneNumber;
    }

    public function isValidationEnabled()
    {
        return $this->scopeConfig->getValue('customer/create_account/validate_phone_enabled');
    }
}

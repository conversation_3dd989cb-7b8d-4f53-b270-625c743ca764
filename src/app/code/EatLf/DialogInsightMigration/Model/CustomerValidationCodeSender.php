<?php

declare(strict_types=1);

namespace EatLf\DialogInsightMigration\Model;

use EatLf\Customer\Api\PhoneNumberValidatorInterface;
use EatLf\DialogInsight\Model\CustomerValidationCodeSender as DiCodeSender;
use EatLf\MTarget\Model\CustomerValidationCodeSender as MTargetCodeSender;
use Magento\Framework\App\Config\ScopeConfigInterface;

class CustomerValidationCodeSender implements PhoneNumberValidatorInterface
{
    public function __construct(
        private readonly MTargetCodeSender $mTargetCodeSender,
        private readonly DiCodeSender $diCodeSender,
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    public function sendValidationCode(string $customerId, string $validationCode): void
    {
        $isDiActive = $this->scopeConfig->getValue('dialoginsight/webapi/sms_active');

        if ($isDiActive) {
            $this->diCodeSender->sendValidationCode($customerId, $validationCode);
        } else {
            $this->mTargetCodeSender->sendValidationCode($customerId, $validationCode);
        }
    }
}

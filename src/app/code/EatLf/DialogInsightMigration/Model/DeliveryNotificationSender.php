<?php

declare(strict_types=1);

namespace EatLf\DialogInsightMigration\Model;

use EatLf\DialogInsight\Model\DeliveryNotificationSender as DiCodeSender;
use EatLf\Livraison\Api\DeliveryNotificationSenderInterface;
use EatLf\MTarget\Model\DeliveryNotificationSender as MTargetCodeSender;
use Magento\Framework\App\Config\ScopeConfigInterface;

class DeliveryNotificationSender implements DeliveryNotificationSenderInterface
{
    public function __construct(
        private readonly MTargetCodeSender $mTargetCodeSender,
        private readonly DiCodeSender $diCodeSender,
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    public function sendDeliveryNotification(string $orderId): void
    {
        $isDiActive = $this->scopeConfig->getValue('dialoginsight/webapi/sms_active');

        if ($isDiActive) {
            $this->diCodeSender->sendDeliveryNotification($orderId);
        } else {
            $this->mTargetCodeSender->sendDeliveryNotification($orderId);
        }
    }
}

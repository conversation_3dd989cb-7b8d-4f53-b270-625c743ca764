<?php
declare(strict_types=1);

namespace EatLf\CustomerGraphQl\Model\Resolver;

use EatLf\Customer\Model\Service\Telephone;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class CheckCode implements ResolverInterface
{

    private Telephone $phoneService;
    private CustomerRepositoryInterface $customerRepository;

    public function __construct(
        Telephone $phoneService,
        CustomerRepositoryInterface $customerRepository
    ) {
        $this->phoneService = $phoneService;
        $this->customerRepository = $customerRepository;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null) : array
    {
        $result = [];

        $currentUserId = $context->getUserId();
        if (!$currentUserId) {
            throw new GraphQlInputException(__('User must be authenticated to request a token'));
        }

        if (!isset($args['code'])) {
            throw new GraphQlInputException(__('code parameter is missing'));
        }
        $submittedCode = $args['code'];

        $customer = $this->customerRepository->getById($currentUserId);
        $code = $customer->getCustomAttribute('validation_code')->getValue();

        if($submittedCode == $code) {
            $result['is_code_correct'] = true;
        } else {
            $result['is_code_correct'] = false;
        }
        return $result;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\CustomerGraphQl\Model\Resolver;

use EatLf\Customer\Model\Service\Telephone;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\CustomerGraphQl\Model\Customer\ExtractCustomerData;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class ValidateCode implements ResolverInterface
{

    private Telephone $phoneService;
    private CustomerRepositoryInterface $customerRepository;
    private ExtractCustomerData $extractCustomerData;

    public function __construct(
        Telephone $phoneService,
        CustomerRepositoryInterface $customerRepository,
        ExtractCustomerData $extractCustomerData
    ) {
        $this->phoneService = $phoneService;
        $this->customerRepository = $customerRepository;
        $this->extractCustomerData = $extractCustomerData;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null) : array
    {
        $result = [];

        $currentUserId = $context->getUserId();
        if (!$currentUserId) {
            throw new GraphQlInputException(__('User must be authenticated to request a token'));
        }

        if (!isset($args['code'])) {
            throw new GraphQlInputException(__('code parameter is missing'));
        }
        $submittedCode = $args['code'];

        $this->phoneService->validate($submittedCode);
        $customer = $this->customerRepository->getById($currentUserId);
        $data = $this->extractCustomerData->execute($customer);

        return $data;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\CustomerGraphQl\Model\Resolver;

use EatLf\Customer\Model\Service\Telephone;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class ResendCode implements ResolverInterface
{

    private Telephone $phoneService;
    private CustomerRepositoryInterface $customerRepository;

    public function __construct(
        Telephone $phoneService,
        CustomerRepositoryInterface $customerRepository
    ) {
        $this->phoneService = $phoneService;
        $this->customerRepository = $customerRepository;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null) : array
    {
        $result = [];

        $currentUserId = $context->getUserId();
        if (!$currentUserId) {
            throw new GraphQlInputException(__('User must be authenticated to request a token'));
        }

        $customer = $this->customerRepository->getById($currentUserId);

        $sent = $this->phoneService->sendCodeAgain();
        if($sent) {
            $phoneNumber = $customer->getCustomAttribute('telephone')->getValue();
            $phoneNumber = "+33 ".wordwrap(str_replace('+33','0',$phoneNumber), 2, ' ', true);
            $message = "Nous avons envoyé votre code à 4 chiffres au ".$phoneNumber;
        } else {
            $message = "Le SMS a déjà été envoyé plusieurs fois, veuillez vérifier votre numéro de téléphone entré en revenant à l'étape précédente.";
        }

        $result['is_code_resent'] = $sent;
        $result['message'] = $message;

        return $result;
    }
}

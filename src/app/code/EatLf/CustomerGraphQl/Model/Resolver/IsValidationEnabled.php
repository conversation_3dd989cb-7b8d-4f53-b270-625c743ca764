<?php
declare(strict_types=1);

namespace EatLf\CustomerGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

use EatLf\Customer\Model\Service\Telephone;

class IsValidationEnabled implements ResolverInterface
{

    private Telephone $phoneService;

    public function __construct(
        Telephone $phoneService
    ) {
        $this->phoneService = $phoneService;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null) : array
    {
        $result = [];
        $result['is_validation_enabled'] = $this->phoneService->isValidationEnabled();
        return $result;
    }
}

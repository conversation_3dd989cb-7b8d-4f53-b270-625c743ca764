<?php
declare(strict_types=1);

namespace EatLf\CustomerGraphQl\Model\Resolver;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Store\Model\ScopeInterface;

class Phone implements ResolverInterface
{

    private ScopeConfigInterface $scopeConfig;

    public function __construct(
        ScopeConfigInterface $scopeConfig
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null) : array
    {
        $result = [];
        $result[] =
        [
            "id" => "tooltip_step1",
            "value" =>
                strip_tags($this->scopeConfig->getValue(
                'customer/create_account/account_creation_phone_tooltip',
                ScopeInterface::SCOPE_STORE)
            ),
        ];
        $result[] = [
        "id" => "tooltip_change_phone",
        "value" =>
            strip_tags($this->scopeConfig->getValue(
                'customer/create_account/account_edit_phone_tooltip',
                ScopeInterface::SCOPE_STORE)
            ),
        ];

        $result[] = [
            "id" => "account_edit_phone_used_tooltip",
            "value" =>
                strip_tags($this->scopeConfig->getValue(
                    'customer/create_account/account_edit_phone_used_tooltip',
                    ScopeInterface::SCOPE_STORE)
                ),
        ];

        return $result;
    }
}

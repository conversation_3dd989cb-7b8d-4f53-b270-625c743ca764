<?php

declare(strict_types=1);

namespace EatLf\CustomerGraphQl\Plugin;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\CustomerGraphQl\Model\Customer\ExtractCustomerData;

class AddCustomAttributesToCustomer
{
    public function afterExecute(ExtractCustomerData $extractCustomerData, array $result, CustomerInterface $customer)
    {
        foreach ($result['custom_attributes'] as $customAttribute) {
            if ($this->ignoreAttribute($customAttribute)) {
                continue;
            }

            $result[$customAttribute['code']] = $customAttribute['value'];
        }

        return $result;
    }

    private function ignoreAttribute(array $customAttribute): bool
    {
        $code = $customAttribute['code'];

        return !isset($customAttribute['value']) ||
            str_contains($code, 'systempay') ||
            str_contains($code, 'edenred');
    }
}

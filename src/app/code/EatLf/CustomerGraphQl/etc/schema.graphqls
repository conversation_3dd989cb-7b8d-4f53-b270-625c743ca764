input CustomerCreateInput {
    telephone: String @doc(description: "The customer phone number")
}

input CustomerUpdateInput {
    telephone: String @doc(description: "The customer phone number")
    dob_changed: String @doc(description: "The date of the last birthday change")
}

type Customer {
    telephone: String @doc(description: "The customer phone number")
    phone_validated: Int @doc(description: "Is phone number validated")
    dob_changed: String @doc(description: "The date of the last birthday change")
}

type IsPhoneNumberAvailableOutput {
    is_phone_number_available: <PERSON><PERSON><PERSON> @doc(description: "Is email available value")
}
type IsCodeResent {
    is_code_resent: <PERSON><PERSON><PERSON> @doc(description: "Is code resent")
    message: String @doc(description: "Message to display")
}
type validated {
    is_account_validated: <PERSON><PERSON><PERSON> @doc(description: "Is code correct")
}

type IsValidationEnabled {
    is_validation_enabled: Bo<PERSON>an @doc(description: "Is validation enabled")
}

type IsCodeCorrect {
    is_code_correct: <PERSON><PERSON><PERSON> @doc(description: "Is code correct")
}

type Query {
    getPhoneHelpText: [phoneHelp] @resolver(class:"EatLf\\CustomerGraphQl\\Model\\Resolver\\Phone") @doc(description: "get phone number help text" )  @cache(cacheable: true)
    isPhoneNumberAvailable (
            phoneNumber: String! @doc(description: "The new customer phone number")
        ): IsPhoneNumberAvailableOutput @resolver(class: "EatLf\\CustomerGraphQl\\Model\\Resolver\\IsPhoneAvailable") @cache(cacheable: false)
    checkCode (code: String! @doc(description: "Phone validation code")
    ): IsCodeCorrect @resolver(class: "EatLf\\CustomerGraphQl\\Model\\Resolver\\CheckCode") @doc(description: "Check phone validation code") @cache(cacheable: false)
    isValidationEnabled:IsValidationEnabled @resolver(class:"EatLf\\CustomerGraphQl\\Model\\Resolver\\IsValidationEnabled") @doc(description: "Is validation phone is enabled ?" )  @cache(cacheable: true)
}

type Mutation {
    resendCode:IsCodeResent @resolver(class: "EatLf\\CustomerGraphQl\\Model\\Resolver\\ResendCode") @doc(description: "Send again phone number validation code")
    validateCode (code: String! @doc(description: "Phone validation")
        ): Customer @resolver(class: "EatLf\\CustomerGraphQl\\Model\\Resolver\\ValidateCode") @doc(description: "Validate phone number") @cache(cacheable: false)

}

type phoneHelp {
   value: String
   id: String
}

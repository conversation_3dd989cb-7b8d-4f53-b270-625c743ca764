define([
    'knockout',
    'EatLf_Shipping/js/model/shipping',
], function (
    ko,
    shippingModel
) {
   'use strict';

   return function (ProductPopin) {
       return ProductPopin.extend({
           initialize: function () {
               this._super();

               ko.computed(function () {
                   if (shippingModel.isComplete()) {
                       this.hasTimeslot(true);
                   } else {
                       this.hasTimeslot(false);
                   }
               }.bind(this));

               return this;
           }
       });
   };
});
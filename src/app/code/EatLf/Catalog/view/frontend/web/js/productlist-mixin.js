define([
    'knockout',
    'jquery',
    'EatLf_Shipping/js/model/shipping',
    './action/fetch-prices'
], function (
    ko,
    $,
    shippingModel,
    fetchPrices
) {
    'use strict';

    return function (ProductList) {
        return ProductList.extend({
            initialize: function () {
                this._super();

                this.onPricesFetched = this.onPricesFetched.bind(this);
                this.onShippingDataChange = this.onShippingDataChange.bind(this);
                this.disableLoader = this.disableLoader.bind(this);

                ko.computed(this.onShippingDataChange);

                return this;
            },

            onShippingDataChange: function () {
                if (shippingModel.isComplete()) {
                    $('.no-delivery').addClass('loading');

                    fetchPrices()
                        .done(this.onPricesFetched)
                        .always(this.disableLoader);
                }
            },

            onPricesFetched: function (data) {
                var productIds = data.map(function (product) {
                    return product.id;
                });

                var disabledProducts = this.getDisabledProducts(productIds);

                this.showHiddenProducts(disabledProducts);
                this.disableProducts(disabledProducts);
                this.updatePrice(data);

                $(document).trigger("pricesRetrieved");
            },

            getDisabledProducts: function (priceResponse) {
                return $('.produit[data-id]')
                    .toArray()
                    .map(function (product) {
                        return $(product).attr('data-id');
                    })
                    .filter(function (productId) {
                        return priceResponse.indexOf(productId) === -1;
                    });
            },

            disableLoader: function () {
                $('.no-delivery').removeClass('loading');
            },

            updatePrice: function (productPrices) {
                $.each(productPrices, function (index, product) {
                    var $productQty = $('.quantity[data-id=' + product['id'] + ']');
                    var priceDom = '';

                    if(product['special_ht']!=0)
                    {
                        priceDom += '<div class="ht barre">' + product['ht'] + '<span>€ HT</span></div>';
                    }

                    if(product['special_ttc']!=0)
                    {
                        priceDom += '<div class="ttc barre">' + product['ttc'] + '<span>€ TTC</span></div>';
                    }

                    priceDom += '<br>';

                    if(product['special_ht']==0)
                    {
                        priceDom += '<div class="ht regular">' + product['ht'] + '<span>€ HT</span></div>';
                    } else {
                        priceDom += '<div class="ht regular">' + product['special_ht'] + '<span>€ HT</span></div>';
                    }

                    if(product['special_ttc']==0)
                    {
                        priceDom += '<div class="ttc regular">' + product['ttc'] + '<span>€ TTC</span></div>';
                    } else {
                        priceDom += '<div class="ttc regular">' + product['special_ttc'] + '<span>€ TTC</span></div>';
                    }

                    $productQty.before(priceDom);

                    if (product['special_ttc'] != 0 || product['special_ht'] !=0) {
                        $productQty.parent().addClass('prixbarre');
                    }
                });

                $(".no-delivery").hide();
                $(".addtocart").show();
            }
        });
    };
});

<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <listingToolbar name="listing_top">
        <columnsControls name="columns_controls">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="displayArea" xsi:type="boolean">false</item>
                </item>
            </argument>
        </columnsControls>
        <massaction name="listing_massaction">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="componentDisabled" xsi:type="boolean">true</item>
            </item>
        </argument>
        </massaction>
    </listingToolbar>
    <columns name="product_columns" class="Magento\Catalog\Ui\Component\Listing\Columns">
        <column name="type_id">
            <settings>
                <controlVisibility>false</controlVisibility>
                <dataType>select</dataType>
                <label translate="true">Type</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="attribute_set_id">
            <settings>
                <controlVisibility>false</controlVisibility>
                <dataType>select</dataType>
                <label translate="true">Attribute Set</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="is_active_eatlf" component="Magento_Ui/js/grid/columns/select" sortOrder="90">
            <settings>
                <addField>true</addField>
                <options class="Magento\Catalog\Model\Product\Attribute\Source\Status"/>
                <filter>select</filter>
                <dataType>select</dataType>
                <label translate="true">Status EatLf</label>
            </settings>
        </column>
        <column name="price">
            <settings>
                <controlVisibility>false</controlVisibility>
                <dataType>select</dataType>
                <label translate="true">Price</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="visible_fo">
            <settings>
                <controlVisibility>false</controlVisibility>
                <dataType>select</dataType>
                <label translate="true">Visible en FO</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="visibility">
            <settings>
                <controlVisibility>false</controlVisibility>
                <dataType>select</dataType>
                <label translate="true">Visibility</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="status">
            <settings>
                <controlVisibility>false</controlVisibility>
                <dataType>select</dataType>
                <label translate="true">Status</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="qty">
            <settings>
                <controlVisibility>false</controlVisibility>
                <dataType>select</dataType>
                <label translate="true">Quantity</label>
                <visible>false</visible>
            </settings>
        </column>
    </columns>
    <listingToolbar name="listing_top">
        <filters name="listing_filters">
            <filterSelect
                name="asset_id"
                provider="${ $.parentName }"
                sortOrder="10"
                class="Magento\MediaGalleryUi\Ui\Component\Listing\Filters\Asset"
                component="Magento_Ui/js/grid/filters/elements/ui-select"
                template="Magento_MediaGalleryUi/grid/filters/elements/ui-select">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="entityType" xsi:type="string">catalog_product</item>
                        <item name="identityColumn" xsi:type="string">entity_id</item>
                        <item name="filterOptions" xsi:type="boolean">true</item>
                        <item name="searchOptions" xsi:type="boolean">true</item>
                        <item name="filterPlaceholder" xsi:type="string" translate="true">Asset Title</item>
                        <item name="emptyOptionsHtml" xsi:type="string" translate="true">Start typing to find assets</item>
                        <item name="filterRateLimit" xsi:type="string" translate="true">1000</item>
                        <item name="filterRateLimitMethod" xsi:type="string" translate="true">notifyWhenChangesStop</item>
                        <item name="searchUrl" xsi:type="url" path="media_gallery/asset/search" />
                        <item name="validationUrl" xsi:type="url" path="media_gallery/asset/getSelected"/>
                        <item name="levelsVisibility" xsi:type="number">1</item>
                        <item name="componentDisabled" xsi:type="boolean">true</item>
                    </item>
                </argument>
                <settings>
                    <caption translate="true">– Please Select assets –</caption>
                    <label translate="true">Asset</label>
                    <dataScope>asset_id</dataScope>
                    <disabled>true</disabled>
                </settings>
            </filterSelect>
        </filters>
    </listingToolbar>
</listing>

<?php

declare(strict_types=1);

namespace EatLf\Sales\Model;

use Lf\Sales\Api\OrderMessageSenderInterface;
use Magento\Sales\Api\Data\OrderInterface;

class OrderMessageSenderStub implements OrderMessageSenderInterface
{

    public function publishMessage(OrderInterface $order): void
    {
        // Stub method used for the Corporate => Cantine code merge.
        // Will be replaced by a proper Cantine order sender in the next version.
    }
}

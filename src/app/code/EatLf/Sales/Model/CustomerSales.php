<?php
declare(strict_types=1);

namespace EatLf\Sales\Model;

use EatLf\Sales\Api\CustomerSalesInterface;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\OrderRepositoryInterface;

class CustomerSales implements CustomerSalesInterface
{
    /**
     * @var OrderRepositoryInterface
     */
    private $orderRepository;
    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;
    /**
     * @var SortOrderBuilder
     */
    private $sortOrderBuilder;
    /**
     * @var \Magento\Framework\Api\FilterBuilder
     */
    private $filterBuilder;

    public function __construct(
        OrderRepositoryInterface $orderRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        SortOrderBuilder $sortOrderBuilder,
        FilterBuilder $filterBuilder
    ) {
        $this->orderRepository = $orderRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->filterBuilder = $filterBuilder;
    }

    /**
     * {@inheritDoc}
     */
    public function getLastCustomerOrder($customerId)
    {
        $criteria = $this->searchCriteriaBuilder
            ->addFilter('customer_id', $customerId)
            ->addSortOrder(
                $this->sortOrderBuilder->setField('created_at')->setDescendingDirection()->create()
            )
            ->setPageSize(1)
            ->create();

        $orders = $this->orderRepository->getList($criteria);

        if ($orders->getTotalCount() === 0) {
            throw new NoSuchEntityException();
        }

        $items = $orders->getItems();

        return array_shift($items);
    }

    public function getNumberOfOrders($customerId)
    {
        $date = new \DateTime();

        $criteria = $this->searchCriteriaBuilder
            ->addFilter('customer_id', $customerId)
            ->addFilter('shipping_date', $date->format('Y-m-d'), 'lteq')
            ->addFilters(
                [
                    $this->filterBuilder->setField('state')
                        ->setValue('complete')
                        ->setConditionType('eq')
                        ->create(),
                    $this->filterBuilder->setField('state')
                        ->setValue('processing')
                        ->setConditionType('eq')
                        ->create(),
                ]
            )
            ->create();

        return $this->orderRepository
            ->getList($criteria)
            ->getTotalCount();
    }
}
<?php
declare(strict_types=1);

namespace EatLf\Sales\Rewrite\Block\Order;

use Magento\Framework\App\ObjectManager;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactoryInterface;

class History extends \Magento\Sales\Block\Order\History
{
    /**
     * @var CollectionFactoryInterface
     */
    private $orderCollectionFactory;

    public function getOrders()
    {
        if (!($customerId = $this->_customerSession->getCustomerId())) {
            return false;
        }

        if (!$this->orders) {
            $this->orders = $this->getOrderCollectionFactory()->create($customerId)->addFieldToSelect(
                '*'
            )->addFieldToFilter(
                'status',
                ['in' => $this->_orderConfig->getVisibleOnFrontStatuses()]
            )->setOrder(
                'created_at',
                'desc'
            );

            // Rewrite: Zone and timeslot do no exist on eatlf so they are removed from the query
            $this->orders->getSelect()
                ->joinLeft(
                    ['shipping_data' => $this->orders->getTable('quote_shipping_datas')],
                    "main_table.quote_id = shipping_data.quote_id",
                    ["shipping_date" => "shipping_data.shipping_date"]
                );
        }
        return $this->orders;
    }

    private function getOrderCollectionFactory()
    {
        if ($this->orderCollectionFactory === null) {
            $this->orderCollectionFactory = ObjectManager::getInstance()->get(CollectionFactoryInterface::class);
        }
        return $this->orderCollectionFactory;
    }
}
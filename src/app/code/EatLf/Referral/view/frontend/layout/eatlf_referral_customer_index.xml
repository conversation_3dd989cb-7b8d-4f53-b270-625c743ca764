<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Referral</argument>
            </action>
        </referenceBlock>

        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template"
                   template="EatLf_Referral::customer/account.phtml"
                   name="customer.referral"
                   cacheable="false">
                <arguments>
                    <argument name="view_model" xsi:type="object">
                        EatLf\Referral\Block\Referral
                    </argument>
                </arguments>

                <container name="customer.referral.blocks.left" htmlTag="div">
                    <block class="Magento\Framework\View\Element\Template"
                           template="EatLf_Referral::customer/account/code.phtml"
                           name="customer.referral.left.code">
                        <arguments>
                            <argument name="view_model" xsi:type="object">
                                EatLf\Referral\Block\Referral
                            </argument>
                        </arguments>
                    </block>

                    <block class="Magento\Framework\View\Element\Template"
                           template="EatLf_Referral::customer/account/share.phtml"
                           name="customer.referral.left.share">
                        <arguments>
                            <argument name="view_model" xsi:type="object">
                                EatLf\Referral\Block\Referral
                            </argument>
                        </arguments>
                    </block>
                </container>
            </block>
        </referenceContainer>
    </body>
</page>

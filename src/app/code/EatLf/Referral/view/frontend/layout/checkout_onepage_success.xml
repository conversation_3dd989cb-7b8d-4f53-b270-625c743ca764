<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <referenceContainer name="order.success.additional.info">
        <block class="Magento\Framework\View\Element\Template"
               template="EatLf_Referral::sales/success.phtml"
               name="order.success.referral">
            <arguments>
                <argument name="view_model" xsi:type="object">
                    EatLf\Referral\Block\Referral
                </argument>
            </arguments>
        </block>
    </referenceContainer>
</page>
<?php
declare(strict_types=1);

namespace EatLf\Referral\Setup\Patch\Data;

use Magento\Cms\Api\BlockRepositoryInterface;
use Magento\Cms\Model\BlockFactory;
use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddDashboardCmsBlock implements DataPatchInterface
{
    /**
     * @var \Magento\Cms\Model\BlockFactory
     */
    private BlockFactory $blockFactory;
    /**
     * @var \Magento\Cms\Api\BlockRepositoryInterface
     */
    private BlockRepositoryInterface $blockRepository;
    /**
     * @var \Magento\Framework\App\Config\Storage\WriterInterface
     */
    private WriterInterface $writer;

    public function __construct(
        BlockFactory $blockFactory,
        BlockRepositoryInterface $blockRepository,
        WriterInterface $writer
    ) {
        $this->blockFactory = $blockFactory;
        $this->blockRepository = $blockRepository;
        $this->writer = $writer;
    }


    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }

    public function apply()
    {
        $block = $this->blockFactory->create()
            ->setIdentifier('parrainage-dashboard')
            ->setTitle('Parrainage dashboard client');

        $this->blockRepository->save($block);

        $this->writer->save(
            'referral/referrer/dashboard_cms_block',
            $block->getIdentifier()
        );
    }
}
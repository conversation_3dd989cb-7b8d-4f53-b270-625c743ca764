<?php
declare(strict_types=1);

namespace EatLf\Referral\Setup\Patch\Data;

use Magento\Customer\Api\Data\GroupInterface;
use Magento\Customer\Api\GroupRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Area;
use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\App\State;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\SalesRule\Model\RuleFactory;

class AddReferrerSalesRule implements DataPatchInterface
{
    /**
     * @var \Magento\SalesRule\Model\RuleFactory
     */
    private RuleFactory $ruleFactory;
    /**
     * @var \Magento\Framework\App\State
     */
    private State $state;
    /**
     * @var \Magento\Customer\Api\GroupRepositoryInterface
     */
    private GroupRepositoryInterface $groupRepository;
    /**
     * @var \Magento\Framework\Api\SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $criteriaBuilder;
    /**
     * @var \Magento\Framework\Serialize\SerializerInterface
     */
    private SerializerInterface $serializer;
    /**
     * @var \Magento\Framework\App\Config\Storage\WriterInterface
     */
    private WriterInterface $writer;

    public function __construct(
        State $state,
        RuleFactory $ruleFactory,
        GroupRepositoryInterface $groupRepository,
        SearchCriteriaBuilder $criteriaBuilder,
        SerializerInterface $serializer,
        WriterInterface $writer
    ) {
        $this->ruleFactory = $ruleFactory;
        $this->state = $state;
        $this->groupRepository = $groupRepository;
        $this->criteriaBuilder = $criteriaBuilder;
        $this->serializer = $serializer;
        $this->writer = $writer;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }

    public function apply()
    {
        $criteria = $this->criteriaBuilder->create();

        $groupIds = array_map(function (GroupInterface $group) {
            return $group->getId();
        }, $this->groupRepository->getList($criteria)->getItems());

        $this->state->emulateAreaCode(
            Area::AREA_ADMINHTML,
            function () use ($groupIds) {
                $conditions = [
                    'type' => 'Magento\SalesRule\Model\Rule\Condition\Combine',
                    'attribute' => null,
                    'operator' => null,
                    'value' => 1,
                    'is_value_processed' => null,
                    'aggregator' => 'all',
                    'conditions' => [
                        [
                            'type' => '\EatLf\Referral\Model\SalesRule\ReferralMaxOrders',
                            'attribute' => 'orders_count',
                            'operator' => '<',
                            'value' => 1,
                            'is_value_processed' => false,
                        ],
                        [
                            'type' => '\EatLf\Referral\Model\SalesRule\ActiveReferrer',
                            'attribute' => 'referrer_active',
                            'operator' => '==',
                            'value' => 1,
                            'is_value_processed' => false,
                        ],
                        [
                            'type' => '\EatLf\Referral\Model\SalesRule\OtherAccountsReferral',
                            'attribute' => 'other_accounts_referral',
                            'operator' => '==',
                            'value' => 1,
                            'is_value_processed' => false,
                        ],
                    ],
                ];

                $rule = $this->ruleFactory->create()
                    ->setName('Codes Parrainages')
                    ->setDescription('Règle de gestion des codes parrainages. NE PAS SUPPRIMER.')
                    ->setFromDate('2023-01-01')
                    ->setUsesPerCustomer(0)
                    ->setCustomerGroupIds($groupIds)
                    ->setIsActive(1)
                    ->setStopRulesProcessing(0)
                    ->setIsAdvanced(1)
                    ->setSortOrder(0)
                    ->setSimpleAction('cart_fixed')
                    ->setDiscountAmount(3)
                    ->setDiscountStep(0)
                    ->setStoreLabels(
                        [
                            'store_id' => 0,
                            'store_label' => 'Parrainage',
                        ]
                    )
                    ->setSimpleFreeShipping(0)
                    ->setApplyToShipping(0)
                    ->setTimesUsed(0)
                    ->setIsRss(0)
                    ->setWebsiteIds(['1'])
                    ->setCouponType(2)
                    ->setUseAutoGeneration(1)
                    ->setUsesPerCoupon(null)
                    ->setConditionsSerialized(
                        $this->serializer->serialize($conditions)
                    );

                $rule->save();

                $this->writer->save(
                    'referral/referrer/rule_id',
                    $rule->getId()
                );
            }
        );
    }
}
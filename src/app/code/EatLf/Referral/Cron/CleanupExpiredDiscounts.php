<?php
declare(strict_types=1);

namespace EatLf\Referral\Cron;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class CleanupExpiredDiscounts
{
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private ResourceConnection $resourceConnection;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    private LoggerInterface $logger;

    public function __construct(
        ScopeConfigInterface $scopeConfig,
        ResourceConnection $resourceConnection,
        LoggerInterface $logger
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->resourceConnection = $resourceConnection;
        $this->logger = $logger;
    }

    /**
     * Delete the expired referral discount coupons based on their created dates and the validity period
     * defined in the configuration of the referral module.
     */
    public function execute()
    {
        $ruleId = $this->scopeConfig->getValue('referral/discounts/rule_id');
        $validityPeriod = $this->scopeConfig->getValue('referral/discounts/validity_period');

        if (empty($ruleId)) {
            $this->logger->warning(
                'Could not find the discounts rule id in core_config_data, aborting discounts cleanup'
            );

            return;
        }

        $nbRows = $this->resourceConnection
            ->getConnection()
            ->delete('salesrule_coupon',
                     [
                         'rule_id = ?' => $ruleId,
                         'date_add(date(created_at), interval ? day) < date(now())' => $validityPeriod,
                     ]
            );

        $this->logger->info("{$nbRows} discount coupons have been deleted.");
    }
}
<?php
declare(strict_types=1);

namespace EatLf\Referral\Model\SalesRule;

use EatLf\Referral\Api\CustomerManagementInterface;
use EatLf\Referral\Model\InvalidRuleException;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Model\AbstractModel;
use Magento\Quote\Model\Quote\Address;
use Magento\Rule\Model\Condition\AbstractCondition;
use Magento\Rule\Model\Condition\Context;

class OtherAccountsReferral extends AbstractCondition
{
    /**
     * @var \EatLf\Referral\Api\CustomerManagementInterface
     */
    private CustomerManagementInterface $customerManagement;
    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;

    public function __construct(
        CustomerManagementInterface $customerManagement,
        CustomerRepositoryInterface $customerRepository,
        Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->customerManagement = $customerManagement;
        $this->customerRepository = $customerRepository;
    }

    /**
     * {@inheritdoc}
     */
    public function loadAttributeOptions(): OtherAccountsReferral
    {
        $this->setAttributeOption(
            [
                'other_accounts_referral' => __('Customer can\'t use his own referral code'),
            ]
        );

        return $this;
    }

    /**
     * {@inheritdoc}
     */
    public function getInputType(): string
    {
        return 'select';
    }

    /**
     * {@inheritdoc}
     */
    public function getValueElementType(): string
    {
        return 'select';
    }

    /**
     * {@inheritdoc}
     */
    public function getValueSelectOptions()
    {
        return [
            [
                'value' => 1,
                'label' => __('Yes'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function loadOperatorOptions()
    {
        $this->setOperatorOption(
            [
                '==' => __('is'),
            ]
        );

        return $this;
    }

    /**
     * A customer can not used his/her own referral code.
     *
     * @param Address $model
     *
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function validate(AbstractModel $model)
    {
        $customerId = $model->getCustomerId();
        $coupon = $model->getQuote()->getCouponCode();

        $customer = $this->customerRepository->getById($customerId);
        $customerReferral = $customer->getCustomAttribute('referral_code');

        if ($customerReferral !== null && $customerReferral->getValue() === $coupon) {
            throw new InvalidRuleException(__('You cannot use you own referral code'));
        }

        return true;
    }
}
<?php
declare(strict_types=1);

namespace EatLf\Referral\Model;

use EatLf\Referral\Api\CustomerManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\NotFoundException;
use Magento\SalesRule\Api\CouponManagementInterface;
use Magento\SalesRule\Api\Data\CouponGenerationSpecInterfaceFactory;

class CustomerManagement implements CustomerManagementInterface
{
    private const REFERRAL_PREFIX = 'PAR';

    private const DISCOUNTS_PREFIX = 'FIL';

    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;
    /**
     * @var \Magento\SalesRule\Api\CouponManagementInterface
     */
    private CouponManagementInterface $couponManagement;
    /**
     * @var \Magento\SalesRule\Api\Data\CouponGenerationSpecInterfaceFactory
     */
    private CouponGenerationSpecInterfaceFactory $specInterfaceFactory;
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var \Magento\Framework\Api\SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $criteriaBuilder;

    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        SearchCriteriaBuilder $criteriaBuilder,
        CouponManagementInterface $couponManagement,
        CouponGenerationSpecInterfaceFactory $specInterfaceFactory,
        ScopeConfigInterface $scopeConfig

    ) {
        $this->customerRepository = $customerRepository;
        $this->couponManagement = $couponManagement;
        $this->specInterfaceFactory = $specInterfaceFactory;
        $this->scopeConfig = $scopeConfig;
        $this->criteriaBuilder = $criteriaBuilder;
    }

    /**
     * {@inheritdoc}
     */
    public function generateReferralCode($customerId): CustomerManagementInterface
    {
        $customer = $this->customerRepository->getById($customerId);
        $existingCode = $customer->getCustomAttribute('referral_code');

        if ($existingCode !== null && !empty($existingCode->getValue())) {
            return $this;
        }

        $ruleId = $this->scopeConfig->getValue(
            'referral/referrer/rule_id'
        );

        $couponSpec = $this->specInterfaceFactory->create()
            ->setRuleId($ruleId)
            ->setQuantity(1)
            ->setPrefix(self::REFERRAL_PREFIX)
            ->setLength(8)
            ->setFormat('alphanum');

        $codes = $this->couponManagement->generate($couponSpec);

        $customer->setCustomAttribute(
            'referral_code',
            array_shift($codes)
        );

        $this->customerRepository->save($customer);

        return $this;
    }

    /**
     * {@inheritdoc}
     */
    public function generateDiscountCode(): string
    {
        $ruleId = $this->scopeConfig->getValue(
            'referral/discounts/rule_id'
        );

        $couponSpec = $this->specInterfaceFactory->create()
            ->setRuleId($ruleId)
            ->setQuantity(1)
            ->setPrefix(self::DISCOUNTS_PREFIX)
            ->setLength(8)
            ->setFormat('alphanum');

        $codes = $this->couponManagement->generate($couponSpec);

        return array_shift($codes);
    }

    /**
     * {@inheritdoc}
     */
    public function getReferrerByCode(string $code): CustomerInterface
    {
        $criteria = $this->criteriaBuilder
            ->addFilter('referral_code', $code)
            ->create();

        $customers = $this->customerRepository->getList($criteria);

        if ($customers->getTotalCount() === 0) {
            throw new NotFoundException(__('Referrer not found for code ' . $code));
        }

        $items = $customers->getItems();

        return array_shift($items);
    }
}
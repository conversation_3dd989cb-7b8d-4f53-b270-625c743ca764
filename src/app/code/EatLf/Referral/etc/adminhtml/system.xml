<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="referral" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Referral</label>
            <tab>customer</tab>
            <resource>Magento_Customer::config_customer</resource>
            <group id="referrer" translate="label" type="text" sortOrder="10" showInDefault="1">
                <label>Referrer configuration</label>
                <field id="rule_id" translate="label" type="select" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Sales Rule</label>
                    <comment>The sales rule containing all the referral codes</comment>
                    <source_model>EatLf\Referral\Model\Config\Source\SalesRule</source_model>
                </field>
                <field id="short_text" translate="label" type="text" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Referral program short description</label>
                </field>
                <field id="conditions" translate="label" type="text" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Referral program conditions text</label>
                </field>
                <field id="sharing" translate="label comment" type="textarea" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Referral friend sharing text</label>
                    <comment>The #code# placeholder will be substituted with the customer referral code</comment>
                </field>
                <field id="cms_block_intro" translate="label" type="select" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Referral program CMS block intro</label>
                    <source_model>Magento\Cms\Model\Config\Source\Block</source_model>
                </field>
                <field id="cms_block" translate="label" type="select" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Referral program CMS block</label>
                    <source_model>Magento\Cms\Model\Config\Source\Block</source_model>
                </field>
                <field id="dashboard_cms_block" translate="label" type="select" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Referral program dashboard CMS block</label>
                    <source_model>Magento\Cms\Model\Config\Source\Block</source_model>
                </field>
                <field id="cms_page" translate="label" type="select" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Referral program CMS page</label>
                    <source_model>Magento\Cms\Model\Config\Source\Page</source_model>
                </field>
                <field id="sharing_template" translate="label comment" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Sharing email Template</label>
                    <comment>Template used to send referral sharing emails</comment>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
            </group>
            <group id="discounts" translate="label" type="text" sortOrder="10" showInDefault="1">
                <label>Referral discounts configuration</label>
                <field id="rule_id" translate="label comment" type="select" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Sales Rule</label>
                    <comment>The sales rule containing all the discount codes for referrers</comment>
                    <source_model>EatLf\Referral\Model\Config\Source\SalesRule</source_model>
                </field>
                <field id="validity_period" translate="label" type="text" sortOrder="1" showInDefault="1" canRestore="1">
                    <label>Validity period</label>
                    <comment>Discount code validity period in days</comment>
                </field>
                <field id="email_template" translate="label comment" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Email Template</label>
                    <comment>Email template used to send referral discount codes</comment>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
            </group>
        </section>
    </system>
</config>

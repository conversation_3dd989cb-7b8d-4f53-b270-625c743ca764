<?php
declare(strict_types=1);

namespace EatLf\Referral\Observer\SalesRule;

use EatLf\Referral\Model\SalesRule\ActiveReferrer;
use EatLf\Referral\Model\SalesRule\OtherAccountsReferral;
use EatLf\Referral\Model\SalesRule\ReferralMaxOrders;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class AddReferralConditions implements ObserverInterface
{

    public function execute(Observer $observer)
    {
        $additional = $observer->getAdditional();
        $conditions = (array)$additional->getConditions();

        $conditions = array_merge_recursive($conditions, [
            [
                'label' => __('Referral max orders'),
                'value' => ReferralMaxOrders::class,
            ],
            [
                'label' => __('Active referrer'),
                'value' => ActiveReferrer::class,
            ],
            [
                'label' => __('Customer can\'t use his own referral code'),
                'value' => OtherAccountsReferral::class,
            ],
        ]);

        $additional->setConditions($conditions);
        return $this;
    }
}
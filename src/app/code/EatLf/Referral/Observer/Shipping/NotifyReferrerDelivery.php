<?php
declare(strict_types=1);

namespace EatLf\Referral\Observer\Shipping;

use EatLf\Referral\Api\OrderManagementInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class NotifyReferrerDelivery implements ObserverInterface
{
    /**
     * @var \EatLf\Referral\Api\OrderManagementInterface
     */
    private OrderManagementInterface $orderManagement;

    public function __construct(
        OrderManagementInterface $orderManagement
    ) {
        $this->orderManagement = $orderManagement;
    }

    /**
     * When an order with a referral discount code is set to the 'shipped' state, generate a new discount code
     * for the referrer and send it by email.
     *
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(Observer $observer)
    {
        /** @var \Magento\Sales\Model\Order $order */
        $order = $observer->getData('order');

        $this->orderManagement->notifyReferrer($order);
    }
}
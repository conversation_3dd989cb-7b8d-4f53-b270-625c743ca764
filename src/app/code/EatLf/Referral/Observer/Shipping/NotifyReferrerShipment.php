<?php
declare(strict_types=1);

namespace EatLf\Referral\Observer\Shipping;

use EatLf\Referral\Api\OrderManagementInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;

class NotifyReferrerShipment implements ObserverInterface
{
    /**
     * @var \EatLf\Referral\Api\OrderManagementInterface
     */
    private OrderManagementInterface $orderManagement;

    public function __construct(OrderManagementInterface $orderManagement)
    {
        $this->orderManagement = $orderManagement;
    }

    /**
     * Try to notify the order referrer for new shipments. Do not do anything for shipments that
     * are created when the order is not in the processing state (in that case the notification
     * was already sent by the NotifyReferrerDelivery Observer).
     *
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(Observer $observer)
    {
        $shipment = $observer->getEvent()->getShipment();

        /** @var \Magento\Sales\Model\Order $order */
        $order = $shipment->getOrder();

        if ($order->getState() === Order::STATE_PROCESSING) {
            $this->orderManagement->notifyReferrer($order);
        }
    }
}
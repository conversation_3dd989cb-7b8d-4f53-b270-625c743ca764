<?php
declare(strict_types=1);

namespace EatLf\Referral\Observer\Sales;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\UrlInterface;
use Magento\Sales\Model\Order;

class AddMailVariables implements ObserverInterface
{
    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var \Magento\Framework\UrlInterface
     */
    private UrlInterface $url;

    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        ScopeConfigInterface $scopeConfig,
        UrlInterface $url
    ) {
        $this->customerRepository = $customerRepository;
        $this->scopeConfig = $scopeConfig;
        $this->url = $url;
    }

    public function execute(Observer $observer)
    {
        /** @var \Magento\Framework\App\Action\Action $controller */
        $transport = $observer->getTransport();

        /** @var Order $order */
        $order = $transport->getOrder();

        try {
            $customer = $this->customerRepository->getById(
                $order->getCustomerId()
            );

            $referralAtt = $customer->getCustomAttribute('referral_code');

            $transport['referral_code'] = ($referralAtt !== null && $referralAtt->getValue(
                ) !== null) ? $referralAtt->getValue() : '';

            $transport['referral_short_text'] = $this->scopeConfig->getValue('referral/referrer/short_text');

            $transport['referral_cms_url'] = $this->url->getUrl(
                $this->scopeConfig->getValue('referral/referrer/cms_page')
            );
        } catch (\Exception $e) {
        }
    }
}
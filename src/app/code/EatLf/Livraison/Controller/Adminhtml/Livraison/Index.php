<?php
declare(strict_types=1);

namespace EatLf\Livraison\Controller\Adminhtml\Livraison;

use Magento\Backend\App\Action;
use Magento\Framework\Controller\ResultFactory;

class Index extends Action
{
    /**
     * Index page for the application
     *
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);

        $resultPage->getConfig()->getTitle()->prepend((__('Livraison EatLf')));

        return $resultPage;
    }
}
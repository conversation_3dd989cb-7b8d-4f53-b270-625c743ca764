<?php
declare(strict_types=1);

namespace EatLf\Livraison\Controller\Adminhtml\Livraison;

use EatLf\Livraison\Model\LivraisonManagement;
use Magento\Backend\App\Action;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Pricing\Helper\Data;
use Magento\Sales\Model\Order;

class PartenaireDetails extends Action
{
    /**
     * @var JsonFactory
     */
    private $jsonFactory;
    /**
     * @var Data
     */
    private $priceHelper;
    /**
     * @var LivraisonManagement
     */
    private $livraisonManagement;

    public function __construct(
        Action\Context $context,
        JsonFactory $jsonFactory,
        Data $priceHelper,
        LivraisonManagement $livraisonManagement
    )
    {
        parent::__construct($context);

        $this->jsonFactory = $jsonFactory;
        $this->priceHelper = $priceHelper;
        $this->livraisonManagement = $livraisonManagement;
    }

    /**
     * Retrieves the order details for the specified partenaire
     *
     * @return $this|\Magento\Framework\Controller\Result\Json
     */
    public function execute()
    {
        if (!$this->getRequest()->isAjax()) {
            $this->_forward('noroute');
            return $this;
        }

        $response = $this->jsonFactory->create();
        $tourneeId = $this->getRequest()->getParam('tourneeId');
        $partenaireId = $this->getRequest()->getParam('partenaireId');

        $orders = $this->livraisonManagement->getOrdersForPartenaire($tourneeId, $partenaireId);

        $orders = array_map(function (Order $order) {
            $items = array_map(function (Order\Item $item) {
                return number_format((float)$item->getQtyInvoiced(),0).' x '.ucwords(strtolower($item->getName()));
                },$order->getItems());

            return [
                'id' => $order->getEntityId(),
                'firstname' => $order->getCustomerFirstname(),
                'lastname' => $order->getCustomerLastname(),
                'phone' => $order->getData('telephone'),
                'colis' => $order->getColisId(),
                'items'  => implode('<br>',$items),
                'totalTTC' => $this->priceHelper->currency($order->getGrandTotal(), true, false)
            ];
        }, $orders);

        return $response->setData([
            'orders' => array_values($orders)
        ]);
    }
}
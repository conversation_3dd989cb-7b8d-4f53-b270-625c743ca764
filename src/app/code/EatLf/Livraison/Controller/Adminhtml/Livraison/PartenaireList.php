<?php
declare(strict_types=1);

namespace EatLf\Livraison\Controller\Adminhtml\Livraison;

use EatLf\Livraison\Model\LivraisonManagement;
use Magento\Backend\App\Action;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class PartenaireList extends Action
{
    /**
     * @var TimezoneInterface
     */
    private $timezone;
    /**
     * @var JsonFactory
     */
    private $jsonFactory;
    /**
     * @var Session
     */
    private $authSession;
    /**
     * @var LivraisonManagement
     */
    private $livraisonManagement;

    public function __construct(
        Action\Context $context,
        Session $authSession,
        TimezoneInterface $timezone,
        LivraisonManagement $livraisonManagement,
        JsonFactory $jsonFactory
    )
    {
        parent::__construct($context);

        $this->timezone = $timezone;
        $this->jsonFactory = $jsonFactory;
        $this->authSession = $authSession;
        $this->livraisonManagement = $livraisonManagement;
    }

    /**
     * Retrieves the list of partenaires that have orders to be delivered
     *
     * @return Json
     */
    public function execute()
    {
        $response = $this->jsonFactory->create();
        $today = $this->timezone->date();

        $printerId = $this->authSession->getUser()->getPrinterId();

        if ($printerId === null) {
            return $response->setData([
                'success' => false,
                'error' => __('No printer id in current context')
            ]);
        }

        try {
            $tournee = $this->livraisonManagement->getCurrentTournee($printerId, $today);
        } catch (NoSuchEntityException $e) {
            return $response->setData([
                'success' => false,
                'error' => __('No tournee available')
            ]);
        }

        $tourneeSummary = $this->livraisonManagement->getPartenairesSummary($tournee->getId());

        $resultData = array_map(function (DataObject $item) {
            return $item->toArray();
        }, $tourneeSummary);

        //a t on une commande payée en cagnotte ? si oui afficher une urne
        foreach ($resultData as $key => &$data){
            $partenaireId = $key;
            $data['cagnotte'] = 0;

            $orders = $this->livraisonManagement->getOrdersForPartenaire($tournee->getId(), $partenaireId);
            foreach($orders as $order){
                $method = $order->getPayment()->getLfPaymentsMethods();
                if($method == "wallet"){
                    $data['cagnotte'] = 1;
                    break;
                }
            }
        }

        return $response->setData([
            'success' => true,
            'data' => [
                'tourneeId'   => $tournee->getId(),
                'partenaires' => array_values($resultData),
            ]
        ]);
    }
}

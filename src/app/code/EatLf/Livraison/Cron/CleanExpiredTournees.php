<?php
declare(strict_types=1);

namespace EatLf\Livraison\Cron;

use EatLf\Livraison\Model\ResourceModel\Tournee;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class CleanExpiredTournees
{
    /**
     * @var TimezoneInterface
     */
    private $timezone;
    /**
     * @var Tournee
     */
    private $tourneeResource;

    public function __construct(
        TimezoneInterface $timezone,
        Tournee $tourneeResource
    )
    {
        $this->timezone = $timezone;
        $this->tourneeResource = $tourneeResource;
    }

    /**
     * Deletes past tournees
     */
    public function execute()
    {
        $today = $this->timezone->date();
        $this->tourneeResource->cleanTourneesOlderThan($today);
    }
}
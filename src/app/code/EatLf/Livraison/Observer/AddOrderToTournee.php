<?php
declare(strict_types=1);

namespace EatLf\Livraison\Observer;

use EatLf\Livraison\Model\OrderManagement;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Api\Data\OrderInterface;

class AddOrderToTournee implements ObserverInterface
{
    /**
     * @var OrderManagement
     */
    private $orderManagement;

    public function __construct(
        OrderManagement $orderManagement
    )
    {
        $this->orderManagement = $orderManagement;
    }

    /**
     * Adds new orders to the right tournee
     *
     * @param Observer $observer
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(Observer $observer)
    {
        /** @var OrderInterface $order */
        $order = $observer->getData('order');

        $this->orderManagement->addToTournee($order->getEntityId());
    }
}
<?php
declare(strict_types=1);

namespace EatLf\Livraison\Observer;

use EatLf\Livraison\Model\OrderManagement;
use EatLf\Livraison\Model\ResourceModel\TourneeOrder;
use EatLf\Partenaire\Model\Partenaire;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class ReassignOrders implements ObserverInterface
{
    /**
     * @var OrderManagement
     */
    private $orderManagement;
    /**
     * @var TourneeOrder
     */
    private $tourneeOrderResource;

    public function __construct(
        OrderManagement $orderManagement,
        TourneeOrder $tourneeOrderResource
    )
    {
        $this->orderManagement = $orderManagement;
        $this->tourneeOrderResource = $tourneeOrderResource;
    }

    public function execute(Observer $observer)
    {
        /** @var Partenaire $partenaire */
        $partenaire = $observer->getData('data_object');

        if (!$partenaire->dataHasChangedFor('printer_id')) {
            return;
        }

        $orderIds = $this->tourneeOrderResource->unAssignPartenaireOrders($partenaire->getId());

        foreach ($orderIds as $orderId) {
            $this->orderManagement->addToTournee($orderId);
        }
    }
}
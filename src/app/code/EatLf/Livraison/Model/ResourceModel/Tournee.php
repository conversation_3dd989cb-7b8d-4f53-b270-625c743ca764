<?php
declare(strict_types=1);

namespace EatLf\Livraison\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;

class Tournee extends AbstractDb
{
    protected function _construct()
    {
        $this->_init('eat_tournee', 'id');
    }

    /**
     * Suppresses pointless exception in parent method
     *
     * @return string
     */
    public function getMainTable()
    {
        return $this->getTable($this->_mainTable);
    }

    public function cleanTourneesOlderThan(\DateTime $date)
    {
        $connection = $this->getConnection();

        $select = $connection->select()
            ->from($this->getMainTable())
            ->where('date < ?', $date->format('Y-m-d'));

        $connection->query(
            $connection->deleteFromSelect($select, $this->getMainTable())
        );
    }
}
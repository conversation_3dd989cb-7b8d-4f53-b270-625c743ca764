<?php
declare(strict_types=1);

namespace EatLf\Livraison\Model;

use Lf\Core\Model\Mail\Template\TransportBuilder;
use Lf\Franchise\Model\ResourceModel\FranchiseRepository;
use Lf\Sales\Model\Order\Pdf\Invoice as InvoicePdf;
use Magento\Backend\App\Action\Context;
use Magento\Customer\Model\ResourceModel\AddressRepository;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Sales\Api\Data\OrderAddressInterface;
use Magento\Sales\Model\Convert\Order as ConvertOrder;
use Magento\Sales\Model\Order;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class MailSender
{
    const XML_PATH_MAIL_EMAIL = 'trans_email/ident_sales/email';
    const XML_PATH_MAIL_NAME = 'trans_email/ident_sales/name';
    const CONFIRM_DELIVERY_TEMPLATE_PATH = 'eat_livraison/general/confirm_delivery_template';
    const SHIPPED_EMAIL_TEMPLATE = 'shipped';

    /**
     * @var Context
     */
    private $context;
    /**
     * @var StoreManagerInterface
     */
    private $storeManager;
    /**
     * @var StateInterface
     */
    private $inlineTranslation;
    /**
     * @var ConvertOrder
     */
    private $convertOrder;
    /**
     * @var TransportBuilder
     */
    private $transportBuilder;
    /**
     * @var InvoicePdf
     */
    private $invoicePdf;
    /**
     * @var AddressRepository
     */
    private $customerAddressRepository;
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    private FranchiseRepository $franchiseRepository;

    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        StateInterface $inlineTranslation,
        ConvertOrder $convertOrder,
        TransportBuilder $transportBuilder,
        InvoicePdf $invoicePdf,
        AddressRepository $customerAddressRepository,
        LoggerInterface $logger,
        ScopeConfigInterface $scopeConfig,
        FranchiseRepository $franchiseRepository
    )
    {
        $this->context = $context;
        $this->storeManager = $storeManager;
        $this->inlineTranslation = $inlineTranslation;
        $this->convertOrder = $convertOrder;
        $this->transportBuilder = $transportBuilder;
        $this->invoicePdf = $invoicePdf;
        $this->customerAddressRepository = $customerAddressRepository;
        $this->logger = $logger;
        $this->franchiseRepository = $franchiseRepository;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param Order $order
     * @param $gabarit
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Zend_Pdf_Exception
     */
    public function sendEmail($order, $gabarit)
    {
        $firstname = $order->getCustomerFirstname();
        $lastname = $order->getCustomerLastname();
        $scopeConfig = $this->scopeConfig;

        $franchise = null;
        $franchiseId = $order->getFranchiseId();
        if($franchiseId != 0) {
            $franchise = $this->franchiseRepository->getById($franchiseId);
        }

        $templateVars = array(
            'store' => $this->storeManager->getStore(),
            'customer_name' => $firstname . ' ' . $lastname,
            'franchise' => $franchise->getData()
        );

        $this->inlineTranslation->suspend();

        $fromEmail = $scopeConfig->getValue(self::XML_PATH_MAIL_EMAIL, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
        $fromName = $scopeConfig->getValue(self::XML_PATH_MAIL_NAME, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
        $ccEmail = $this->getCcEmail($order->getBillingAddress());

        /** @var \Zend_Pdf $invoicePdf */
        $invoicePdf = $this->invoicePdf->getPdf($order->getInvoiceCollection());

        if($this->scopeConfig->getValue(self::CONFIRM_DELIVERY_TEMPLATE_PATH, \Magento\Store\Model\ScopeInterface::SCOPE_STORE)){
            $gabarit = $this->scopeConfig->getValue(self::CONFIRM_DELIVERY_TEMPLATE_PATH, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
        }

        $this->transportBuilder->setTemplateIdentifier($gabarit)
            ->setTemplateOptions(
                [
                    'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                    'store' => $this->storeManager->getStore()->getId(),
                ]
            )
            ->setTemplateVars($templateVars)
            ->setFrom(['email' => $fromEmail, 'name' => $fromName,])
            ->addTo($order->getCustomerEmail(), $firstname . ' ' . $lastname)
            ->addAttachment($invoicePdf->render(), 'facture.pdf');

        if ($ccEmail) {
            $this->transportBuilder->addCc($ccEmail);
        }

        $transport = $this->transportBuilder->getTransport();

        try {
            $transport->sendMessage();
        } catch (\Exception $e) {
            $this->logger->error("Impossible d'envoyer le mail", $e->getTrace());
        }

        $this->inlineTranslation->resume();
    }

    public function getCcEmail(OrderAddressInterface $billingAddress)
    {
        try {
            $customerAddress = $this->customerAddressRepository->getById($billingAddress->getCustomerAddressId());
            $emailCustomAttribute = $customerAddress->getCustomAttribute('email');
            if ($emailCustomAttribute) {
                $emailComptable = $emailCustomAttribute->getValue();

                if (strpos($emailComptable, ';') !== false) {
                    $emailComptable = explode(';', $emailComptable);
                }

                if (!is_array($emailComptable) && strpos($emailComptable, ',') !== false) {
                    $emailComptable = explode(',', $emailComptable);
                }
            } else {
                $emailComptable = "";
            }
        } catch (LocalizedException $e) {
            $emailComptable = "";
        }

        return $emailComptable;
    }
}

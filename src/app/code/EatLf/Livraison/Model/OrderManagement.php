<?php
declare(strict_types=1);

namespace EatLf\Livraison\Model;

use DateTime;
use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

class OrderManagement
{
    /**
     * @var ResourceModel\Tournee
     */
    private $tourneeResource;
    /**
     * @var PartenaireRepositoryInterface
     */
    private $partenaireRepository;
    /**
     * @var LivraisonManagement
     */
    private $livraisonManagement;
    /**
     * @var ResourceModel\TourneeOrder
     */
    private $tourneeOrderResource;
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @var OrderRepositoryInterface
     */
    private $orderRepository;

    public function __construct(
        \EatLf\Livraison\Model\ResourceModel\Tournee $tourneeResource,
        \EatLf\Livraison\Model\ResourceModel\TourneeOrder $tourneeOrderResource,
        PartenaireRepositoryInterface $partenaireRepository,
        LivraisonManagement $livraisonManagement,
        OrderRepositoryInterface $orderRepository,
        LoggerInterface $logger
    )
    {
        $this->tourneeResource = $tourneeResource;
        $this->partenaireRepository = $partenaireRepository;
        $this->livraisonManagement = $livraisonManagement;
        $this->tourneeOrderResource = $tourneeOrderResource;
        $this->logger = $logger;
        $this->orderRepository = $orderRepository;
    }

    /**
     * Adds an order to its associated tournee
     *
     * @param $orderId
     */
    public function addToTournee($orderId)
    {
        try {
            $order = $this->orderRepository->get($orderId);

            $partenaire = $this->partenaireRepository->getById(
                $order->getExtensionAttributes()->getPartenaireId()
            );
        } catch (NoSuchEntityException $e) {
            $this->logger->critical(
                'Could not add order to tournee. Partenaire or order not found for orderid ' . $orderId
            );

            return;
        }

        // For now there is always one tournee per printer / date but there will be more in the future
        // so this code will need to be refactored somewhere else.
        $this->tourneeResource
            ->getConnection()
            ->insertOnDuplicate(
                $this->tourneeResource->getMainTable(),
                [
                    'printer_id' => $partenaire->getPrinterId(),
                    'date' => $order->getExtensionAttributes()->getShippingDate(),
                    'number' => 1
                ]
            );

        try {
            $tournee = $this->livraisonManagement->getCurrentTournee(
                $partenaire->getPrinterId(),
                new DateTime($order->getExtensionAttributes()->getShippingDate())
            );
        } catch (NoSuchEntityException $e) {
            // The tournee is created above. Will be refactored for the multitournées functionality
            return;
        }

        $this->tourneeOrderResource
            ->getConnection()
            ->insertOnDuplicate(
                $this->tourneeOrderResource->getMainTable(),
                [
                    'tournee_id' => $tournee->getId(),
                    'order_id' => $order->getEntityId()
                ]
            );
    }
}
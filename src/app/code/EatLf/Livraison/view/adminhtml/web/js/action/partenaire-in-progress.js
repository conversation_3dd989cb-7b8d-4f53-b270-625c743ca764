define([
    'mage/storage',
    '../model/model'
], function (
    storage,
    livraisonModel
) {
    'use strict';

    return function partenaireInProgress(partenaire) {
        partenaire.in_progress = 1;

        return storage.post(
            window.partenaireInProgressUrl,
            {
                partenaireId: partenaire.id,
                tourneeId: livraisonModel.tourneeId()
            },
            true,
            'application/x-www-form-urlencoded'
        );
    };
});
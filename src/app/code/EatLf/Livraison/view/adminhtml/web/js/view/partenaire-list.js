define([
    'jquery',
    'uiComponent',
    'moment',
    '../action/partenaire-list-load',
    '../action/partenaire-details-load',
    '../action/partenaire-orders-deliver',
    '../action/partenaire-in-progress',
    '../model/model'
], function (
    $,
    Component,
    moment,
    partenaireListLoad,
    partenaireDetailsLoad,
    partenaireOrdersDeliver,
    partenaireInProgress,
    livraisonModel
) {
    'use strict';

    return Component.extend({
        defaults: {
            partenaires: livraisonModel.partenaires,
            template: 'EatLf_Livraison/partenaire-list',
            isVisible: livraisonModel.partenaireListVisible,
        },

        initialize: function () {
            this._super();

            partenaireListLoad();

            return this;
        },

        formatTimeslot: function (value) {
            return moment(value, 'Hmm').format('HH[h]mm');
        },

        onWazeClicked: function (partenaire) {
            window.open(
                'https://waze.com/ul?ll=' +
                partenaire.latitude + ',' + partenaire.longitude +
                '&navigate=yes'
            );
        },

        onDetailsClicked: function (partenaire) {
            partenaireDetailsLoad(partenaire);
        },

        onDeliveredClicked: function (partenaire, event) {
            var $button = $(event.target).prop('disabled', true);

            partenaireOrdersDeliver(partenaire).then(function () {
                $button.prop('disabled', false);
            });
        },

        onInProgressClicked: function (partenaire, event) {
            $(event.target).prop('disabled', true);
            partenaireInProgress(partenaire);
        }
    });
});
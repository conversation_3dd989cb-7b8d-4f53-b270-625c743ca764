define([
    'ko',
    'mage/storage',
    '../model/model'
], function (
    ko,
    storage,
    livraisonModel
) {
    'use strict';

    return function loadPartenaireList(partenaire) {
        var url = window.partenaireDetailsUrl +
            '?partenaireId=' + partenaire.id +
            '&tourneeId=' + livraisonModel.tourneeId();

        return storage.get(url).done(function (response) {
            livraisonModel.selectedPartenaire(partenaire);
            livraisonModel.deliveries(response.orders);

            livraisonModel.partenaireListVisible(false);
            livraisonModel.partenaireDetailsVisible(true);
        });
    };
});
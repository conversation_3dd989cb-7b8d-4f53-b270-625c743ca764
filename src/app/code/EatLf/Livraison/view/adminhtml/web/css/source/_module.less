body.livraison-livraison-index {
  min-width: initial;
  font-size: 4rem;

  .page-content {
    padding-left: 0;
    padding-right: 0;
  }

  .page-wrapper {
    width: 100%;
  }

  .livraison-header {
    border: 2px solid black;
    background-color: #4473C5;
    color: white;
    font-weight: bold;
    font-size: 4.5rem;
    padding: 10px;
    height: 130px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    text-align: center;

    .home-button {
      cursor: pointer;
      position: absolute;
      left: 20px;
      top: 12px;
      width: 100px;
      height: 100px;
      background: url('@{baseDir}/EatLf_Livraison/images/home.png') no-repeat;
      background-size: contain;
    }
  }

  .partenaire-name {
    font-weight: bold;
    text-decoration: underline;
  }

  .partenaire-item {
    border-bottom: 2px solid black;
    border-right: 2px solid black;
    border-left: 2px solid black;
    padding: 10px;

    .partenaire-item-actions {
      display: flex;

      .item-action {
        color: white;
        flex: auto;
        font-size: 5rem;
      }

      .action-details {
        background-color: #A5A5A5;
      }

      .action-progress {
        background-color: #ED7D31;
      }

      .action-delivered {
        background-color: #70AD46;
      }
    }

    .partenaire-item-content {
      display: flex;
      align-items: center;

      .partenaire-item-summary {
        flex: auto;
      }
        .partenaire-item-pictos {
            width: 130px;
            height: 130px;
        }
        .picto_urne {
            background: url('@{baseDir}/EatLf_Livraison/images/urne.png');
            background-size: cover;
        }
        .picto_frigidaire {
            background: url('@{baseDir}/EatLf_Livraison/images/frigidaire.png') ;
            background-size: cover;
        }
        .picto_etagere {
            background: url('@{baseDir}/EatLf_Livraison/images/etagere.png') ;
            background-size: cover;
        }


      .partenaire-item-direction {
        cursor: pointer;
        margin-right: 0;
        width: 130px;
        height: 130px;
        background: url('@{baseDir}/EatLf_Livraison/images/waze.png') no-repeat;
        background-size: contain;
      }
    }
  }

  .delivery-header {
    background-color: #C7C7C7C7;
    border-bottom: 2px solid black;
    border-right: 2px solid black;
    border-left: 2px solid black;
    padding: 10px;
  }

  .delivery-item {
    border-bottom: 2px solid black;
    border-right: 2px solid black;
    border-left: 2px solid black;
    display: flex;

    .delivery-summary {
      flex: auto;
      padding: 10px;
      width: 65%;
    }

    .delivery-action {
      display: flex;
      width: 10%;

      button {
        width: 100%;
        background-color: #ED7D31;
        color: white;
        font-size: 15px;
      }
    }
  }

}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
  body.livraison-livraison-index {
    .delivery-item {
      .delivery-action {
        display: flex;
        width: 35%;

        button {
          font-size:4rem;
        }
      }
    }
  }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
  body.livraison-livraison-index {
    .delivery-item {
      .delivery-action {
        display: flex;
        width: 40%;
      }
    }
  }
}

define([
    'mage/storage',
    './partenaire-list-load',
    '../model/model'
], function (
    storage,
    partenaireListLoad,
    livraisonModel
) {
    'use strict';

    return function deliverPartenaireOrders(partenaire) {
        return storage.post(
            window.partenaireOrdersDeliver,
            {
                partenaireId: partenaire.id,
                tourneeId: livraisonModel.tourneeId()
            },
            true,
            'application/x-www-form-urlencoded'
        ).then(function () {
            return partenaireListLoad();
        });
    };
});
<?php

namespace EatLf\SalesRulePartenaire\Block\Adminhtml\Promo\Widget\Chooser;

use EatLf\Partenaire\Model\Config\Source\Partenaire\Type;
use EatLf\Partenaire\Model\ResourceModel\PartenaireGrid\Collection;
use Exception;
use Magento\Backend\Block\Template\Context;
use Magento\Backend\Block\Widget\Grid\Column;
use Magento\Backend\Block\Widget\Grid\Extended;
use Magento\Backend\Helper\Data;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Exception\LocalizedException;

class Partenaire extends Extended
{
    /**
     * @var Collection $partenaireCollection
     */
    private $partenaireCollection;
    /**
     * @var Type $partenaireType
     */
    private $partenaireType;

    /**
     * @param Context $context
     * @param Data $backendHelper
     * @param Collection $partenaireCollection
     * @param Type $partenaireType
     * @param array $data
     */
    public function __construct(
        Context $context,
        Data $backendHelper,
        Collection $partenaireCollection,
        Type $partenaireType,
        array $data = []
    ) {
        $this->partenaireCollection = $partenaireCollection;
        $this->partenaireType = $partenaireType;
        parent::__construct($context, $backendHelper, $data);
    }

    /**
     * @throws FileSystemException
     */
    protected function _construct()
    {
        parent::_construct();

        if ($this->getRequest()->getParam('current_grid_id')) {
            $this->setId($this->getRequest()->getParam('current_grid_id'));
        } else {
            $this->setId('skuChooserGrid_' . $this->getId());
        }

        $form = $this->getJsFormObject();
        $this->setRowClickCallback("{$form}.chooserGridRowClick.bind({$form})");
        $this->setCheckboxCheckCallback("{$form}.chooserGridCheckboxCheck.bind({$form})");
        $this->setRowInitCallback("{$form}.chooserGridRowInit.bind({$form})");
        $this->setDefaultSort('sku');
        $this->setUseAjax(true);
        if ($this->getRequest()->getParam('collapse')) {
            $this->setIsCollapsed(true);
        }
    }

    /**
     * @param Column $column
     * @return $this
     * @throws LocalizedException
     */
    protected function _addColumnFilterToCollection($column)
    {
        // Set custom filter for in product flag
        if ($column->getId() == 'in_products') {
            $selected = $this->_getSelectedPartenaires();
            if (empty($selected)) {
                $selected = '';
            }
            if ($column->getFilter()->getValue()) {
                $this->getCollection()->addFieldToFilter('partenaire_id', ['in' => $selected]);
            } else {
                $this->getCollection()->addFieldToFilter('partenaire_id', ['nin' => $selected]);
            }
        } else {
            parent::_addColumnFilterToCollection($column);
        }
        return $this;
    }

    /**
     * Prepare Catalog Product Collection for attribute SKU in Promo Conditions SKU chooser
     *
     * @return $this
     */
    protected function _prepareCollection()
    {
        $collection = $this->partenaireCollection;
        $this->setCollection($collection);
        return parent::_prepareCollection();
    }

    /**
     * @return Partenaire
     * @throws Exception
     */
    protected function _prepareColumns()
    {
        $this->addColumn(
            'in_products',
            [
                'header_css_class' => 'a-center',
                'type' => 'checkbox',
                'name' => 'in_products',
                'values' => $this->_getSelectedPartenaires(),
                'align' => 'center',
                'index' => 'partenaire_id',
                'use_index' => true
            ]
        );

        $this->addColumn(
            'franchise_name',
            ['header' => __('Franchise'), 'sortable' => false, 'width' => '60px', 'index' => 'franchise_name']
        );

        $this->addColumn(
            'company',
            ['header' => __('Company'), 'sortable' => true, 'width' => '60px', 'index' => 'company']
        );

        $this->addColumn(
            'type',
            [
                'header' => __('Type'),
                'width' => '60px',
                'index' => 'type',
                'type' => 'options',
                'sortable' => false,
                'options' => $this->partenaireType->asArray()
            ]
        );

        $this->addColumn(
            'city',
            [
                'header' => __('City'),
                'width' => '100px',
                'index' => 'city',
                'sortable' => true,

            ]
        );

        $this->addColumn(
            'printer_name',
            [
                'header' => __('Imprimante'),
                'width' => '100px',
                'index' => 'printer_name',
                'sortable' => false,

            ]
        );

        $this->addColumn(
            'is_active',
            [
                'header' => __('Actif / Inactif'),
                'width' => '60px',
                'index' => 'is_active',
                'sortable' => false,
                'type' => 'options',
                'options' => [ 0 => 'Non', 1 => 'Oui']
            ]
        );

        return parent::_prepareColumns();
    }

    /**
     * @return string
     */
    public function getGridUrl()
    {
        return $this->getUrl(
            '*/*/chooser',
            ['_current' => true, 'current_grid_id' => $this->getId(), 'collapse' => null]
        );
    }

    /**
     * @return mixed
     */
    protected function _getSelectedPartenaires()
    {
        $ids = $this->getRequest()->getPost('selected', []);

        return $ids;
    }
}

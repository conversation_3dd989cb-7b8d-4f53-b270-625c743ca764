<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">promo_listing.eatlf_promo_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>spinner_columns</spinner>
        <deps>
            <dep>promo_listing.eatlf_promo_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="eatlf_franchise_promo_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">eatlf_promo_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">rule_id</argument>
            <argument name="requestFieldName" xsi:type="string">rule_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">rule_id</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="sticky" xsi:type="boolean">true</item>
            </item>
        </argument>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <filterSelect name="partenaire" provider="${ $.parentName }">
                <settings>
                    <options class="EatLf\Partenaire\Model\Config\Source\PartenaireOnly" />
                    <label translate="true">Partenaire</label>
                    <dataScope>partenaire</dataScope>
                </settings>
            </filterSelect>
        </filters>
        <paging name="listing_paging"/>
        <exportButton name="export_button"/>
    </listingToolbar>
    <columns name="spinner_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="franchiseeat/promo/eatinlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="component" xsi:type="string">EatLf_SalesRulePartenaire/js/grid/editing/editor-partenaire</param>
                <param name="indexField" xsi:type="string">rule_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">
                    promo_listing.promo_listing.spinner_columns.ids
                </param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">
                        promo_listing.promo_listing.spinner_columns_editor
                    </item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>

        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="controlVisibility" xsi:type="boolean">true</item>
                    <item name="visible" xsi:type="boolean">false</item>
                    <item name="componentDisabled" xsi:type="boolean">false</item>
                </item>
            </argument>
            <settings>
                <indexField>rule_id</indexField>
            </settings>
        </selectionsColumn>

        <column name="rule_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>desc</sorting>
            </settings>
        </column>
        <column name="name">
            <settings>
                <filter>text</filter>
                <label translate="true">Modèle de règle</label>
            </settings>
        </column>
        <column name="franchiseur_name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <!-- inlineEdit code -->
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <!-- End inlineEdit code -->
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Règle</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                </item>
            </argument>
        </column>
        <column name="company"  component="Magento_Ui/js/grid/columns/select">
            <settings>
                <options class="EatLf\Partenaire\Model\Config\Source\Partenaire" />
                <editor>
                    <editorType>select</editorType>
                </editor>
                <dataType>select</dataType>
                <label translate="true">Partenaire</label>
            </settings>
        </column>
        <column name="coupon_code">
            <settings>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                    </validation>
                    <editorType>text</editorType>
                </editor>
                <filter>text</filter>
                <label translate="true">Code du bon de réduction</label>
            </settings>
        </column>
        <column name="usage_limit" >
            <settings>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Nombre d'utilisation total du code</label>
            </settings>
        </column>
        <column name="uses_per_customer" >
            <settings>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Nombre d'utilisation par client</label>
            </settings>
        </column>
        <column name="from_date" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date" >
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filterDateFormat" xsi:type="string">dd/MM/y</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="dateFormat" xsi:type="string">dd/MM/y</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">date</item>
                        <item name="validation" xsi:type="array">
                            <item name="name" xsi:type="boolean">true</item>
                        </item>
                        <item name="options" xsi:type="array">
                            <item name="dateFormat" xsi:type="string">dd-MM-y</item>
                            <item name="inputDateFormat" xsi:type="string">dd-MM-y</item>
                        </item>
                    </item>
                </item>
            </argument>
            <settings>
                <filter>dateRange</filter>
                <label translate="true">Commencer</label>
            </settings>
        </column>
        <column name="to_date" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filterDateFormat" xsi:type="string">dd/MM/y</item>
                    <item name="dateFormat" xsi:type="string">dd/MM/y</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">date</item>
                        <item name="validation" xsi:type="array">
                            <item name="name" xsi:type="boolean">true</item>
                        </item>
                        <item name="options" xsi:type="array">
                            <item name="dateFormat" xsi:type="string">dd-MM-y</item>
                            <item name="inputDateFormat" xsi:type="string">dd-MM-y</item>
                        </item>
                    </item>
                </item>
            </argument>
            <settings>
                <filter>dateRange</filter>
                <label translate="true">Fin</label>
            </settings>
        </column>
        <column name="is_active"  component="Magento_Ui/js/grid/columns/select">
            <settings>
                <options class="Magento\Cms\Model\Block\Source\IsActive"/>
                <filter>select</filter>
                <editor>
                    <editorType>select</editorType>
                </editor>
                <dataType>select</dataType>
                <label translate="true">Statut</label>
            </settings>
        </column>
    </columns>
</listing>


<?php

namespace EatLf\SalesRulePartenaire\Model\Rule\Condition;

use Magento\Backend\Helper\Data;
use Magento\Rule\Model\Condition\AbstractCondition;
use Magento\Rule\Model\Condition\Context;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Exception\LocalizedException;


class Partenaire extends AbstractCondition
{
    /**
     * @var Data $_backendData
     */
    private Data $_backendData;


    public function __construct(
        Context $context,
        Data $backendData,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->_backendData = $backendData;
    }

    public function loadAttributeOptions(): Partenaire
    {
        $this->setAttributeOption([
            'partenaire_id' => __('Partenaire livré(s)')
        ]);
        return $this;
    }

    /**
     * @return mixed
     */
    public function getInputType(): string
    {
        return 'grid';  // input type for admin condition
    }

    /**
     * @return string
     */
    public function getValueElementType(): string
    {
        return 'text';
    }

    /**
     * Load operator options
     *
     * @return $this
     */
    public function loadOperatorOptions()
    {
        $this->setOperatorOption(
            [
                '==' => __('is'),
                '!=' => __('is not'),
                '()' => __('is one of'),
                '!()' => __('is not one of')
            ]
        );
        return $this;
    }

    /**
     * Retrieve value element chooser URL
     *
     * @return string
     */
    public function getValueElementChooserUrl()
    {
        $url = false;
        $url = 'sales_rule_partenaire/promo_widget/chooser/attribute/' . $this->getAttribute();
        if ($this->getJsFormObject()) {
            $url .= '/form/' . $this->getJsFormObject();
        }
        return $url !== false ? $this->_backendData->getUrl($url) : '';
    }

    /**
     * Get chooser container html.
     *
     * @return string
     */
    public function getChooserContainerHtml()
    {
        $url = $this->getValueElementChooserUrl();
        return $url ? '<div class="rule-chooser" url="' . $url . '"></div>' : '';
    }

    /**
     * Retrieve after element HTML
     *
     * @return string
     */
    public function getValueAfterElementHtml()
    {
        $html = '';


       $image = $this->_assetRepo->getUrl('images/rule_chooser_trigger.gif');

        if (!empty($image)) {
            $html = '<a href="javascript:void(0)" class="rule-chooser-trigger"><img src="' .
                $image .
                '" alt="" class="v-middle rule-chooser-trigger" title="' .
                __(
                    'Open Chooser'
                ) . '" /></a>';
        }
        return $html;
    }

    /**
     * Retrieve Explicit Apply
     *
     * @return bool
     * @SuppressWarnings(PHPMD.BooleanGetMethodName)
     */
    public function getExplicitApply()
    {
        return true;
    }

    /**
     * @param AbstractModel $model
     * @return bool
     * @throws LocalizedException
     */
    public function validate(AbstractModel $model): bool
    {
        $quote = $model->getQuote();

        $shippingData = $quote->getExtensionAttributes()->getShippingData();

        if ($shippingData !== null) {
            $partenaireId = $shippingData->getExtensionAttributes()->getPartenaireId();
            $model->setData('partenaire_id', $partenaireId);
        }

        return parent::validate($model);
    }
}

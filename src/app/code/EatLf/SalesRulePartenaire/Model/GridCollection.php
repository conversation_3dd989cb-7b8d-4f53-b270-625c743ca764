<?php
namespace EatLf\SalesRulePartenaire\Model;

use Lf\SalesRule\Model\ResourceModel\SalesRule;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\Data\Collection\Db\FetchStrategyInterface as FetchStrategy;
use Magento\Framework\Data\Collection\EntityFactoryInterface as EntityFactory;
use Magento\Framework\Event\ManagerInterface as EventManager;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult;
use Psr\Log\LoggerInterface as Logger;

/**
 * Class Collection
 * Collection for displaying grid
 */
class GridCollection extends SearchResult
{
    /**
     * @var Session $authSession
     */
    private $authSession;

    /**
     * @param Session $authSession
     * @param EntityFactory $entityFactory
     * @param Logger $logger
     * @param FetchStrategy $fetchStrategy
     * @param EventManager $eventManager
     * @param null $identifierName
     * @param null $connectionName
     * @throws LocalizedException
     */
    public function __construct(
        Session $authSession,
        EntityFactory $entityFactory,
        Logger $logger,
        FetchStrategy $fetchStrategy,
        EventManager $eventManager,
        $identifierName = null,
        $connectionName = null
    )
    {
        parent::__construct(
            $entityFactory,
            $logger,
            $fetchStrategy,
            $eventManager,
            'salesrule',
            SalesRule::class,
            $identifierName,
            $connectionName
        );
        $this->authSession = $authSession;

        $userFranchiseId = $this->authSession->getUser()->getFranchiseId();
        if ($userFranchiseId) {
            $this->addFieldToFilter(
                'main_table.franchise_id',
                ['eq' => $userFranchiseId]
            );
        }
        $this->getSelect()
            ->columns("IF(`coupon`.`usage_limit` IS NOT NULL, `coupon`.`usage_limit`, `main_table`.`uses_per_coupon`) as usage_limit")
            ->joinLeft(
                ['coupon' => $this->getTable('salesrule_coupon')],
                'main_table.rule_id = coupon.rule_id AND coupon.is_primary = 1',
                ['coupon_code' => 'coupon.code', "usage_limit_coupon" => 'coupon.usage_limit']
            )->joinLeft(
                ['partenaire' => $this->getTable('partenaire')],
                'main_table.partenaire = partenaire.id',
                ['company' => "CONCAT(partenaire.company, '&&', partenaire.id)"]
            );
    }

    protected function _initSelect()
    {
        $this->addFilterToMap('rule_id', 'main_table.rule_id');
        $this->addFilterToMap('coupon_code', 'coupon.code');
        $this->addFilterToMap('is_active', 'main_table.is_active');
        $this->addFilterToMap('partenaire', 'main_table.partenaire');

        parent::_initSelect();
    }
}

<?php

namespace EatLf\SalesRulePartenaire\Observer;

use Magento\Framework\Event\ObserverInterface ;
use Magento\Framework\Event\Observer;
use EatLf\SalesRulePartenaire\Model\Rule\Condition\Partenaire;

class PartenaireConditionObserver implements ObserverInterface
{
    /**
     * @param Observer $observer
     * @return PartenaireConditionObserver
     */
    public function execute(Observer $observer): PartenaireConditionObserver
    {
        $additional = $observer->getAdditional();
        $conditions = (array) $additional->getConditions();

        $conditions = array_merge_recursive($conditions, [
            $this->getPartenaireCondition()
        ]);

        $additional->setConditions($conditions);
        return $this;
    }

    /**
     * @return array
     */
    private function getPartenaireCondition(): array
    {
        return [
            'label'=> __('Partenaire livré(s)'),
            'value'=> Partenaire::class
        ];
    }
}

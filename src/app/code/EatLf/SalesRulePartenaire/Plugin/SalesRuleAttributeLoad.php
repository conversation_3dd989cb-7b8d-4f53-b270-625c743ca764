<?php

namespace EatLf\SalesRulePartenaire\Plugin;

use Magento\SalesRule\Model\Rule;

class SalesRuleAttributeLoad
{
    public function beforeSave(Rule $rule)
    {
        $ruleExtension = $rule->getExtensionAttributes();

        if ($ruleExtension === null) {
            return null;
        }

        if (isset($ruleExtension['partenaire'])) {
            $rule->setData('partenaire', $ruleExtension['partenaire']);
        }

        return null;
    }
}

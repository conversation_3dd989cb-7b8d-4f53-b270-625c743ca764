<?php
namespace EatLf\SalesRulePartenaire\Controller\Adminhtml\Promo\Widget;

use EatLf\SalesRulePartenaire\Block\Adminhtml\Promo\Widget\Chooser\Partenaire;

class Chooser extends \Magento\CatalogRule\Controller\Adminhtml\Promo\Widget\Chooser
{
    /**
     * Authorization level of a basic admin session
     *
     * @see _isAllowed()
     */
    const ADMIN_RESOURCE = 'Magento_SalesRule::quote';

    /**
     * Prepare block for chooser
     *
     * @return void
     */
    public function execute()
    {
        $request = $this->getRequest();
        $block = $this->_view->getLayout()->createBlock(
            Partenaire::class,
            'promo_widget_chooser_sku',
            ['data' => ['js_form_object' => $request->getParam('form')]]
        );

        if ($block) {
            $this->getResponse()->setBody($block->toHtml());
        }
    }
}

<?php
declare(strict_types=1);

namespace EatLf\ShippingGraphQl\Model\Resolver;

use EatLf\ShippingAutoSelect\Api\AutoShippingManagementInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\Cart\CustomerCartResolver;

class AutoShipping implements ResolverInterface
{
    /**
     * @var \EatLf\ShippingAutoSelect\Api\AutoShippingManagementInterface
     */
    private AutoShippingManagementInterface $autoShippingManagement;
    /**
     * @var \Magento\Quote\Model\Cart\CustomerCartResolver
     */
    private CustomerCartResolver $customerCartResolver;
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;

    public function __construct(
        CustomerCartResolver $customerCartResolver,
        AutoShippingManagementInterface $autoShippingManagement,
        CartRepositoryInterface $cartRepository
    ) {
        $this->autoShippingManagement = $autoShippingManagement;
        $this->customerCartResolver = $customerCartResolver;
        $this->cartRepository = $cartRepository;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
         $customerId = $context->getUserId();

        if ($customerId === 0) {
            throw new GraphQlAuthorizationException(__('Customer must be authenticated'));
        }

        $cart = $this->customerCartResolver->resolve($customerId);
        $this->autoShippingManagement->autoSelect($customerId, $cart);

        $this->cartRepository->save($cart);

        return [
            'model' => $cart,
        ];
    }
}
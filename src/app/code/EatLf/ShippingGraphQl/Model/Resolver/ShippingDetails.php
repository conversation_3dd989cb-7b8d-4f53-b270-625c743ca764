<?php
declare(strict_types=1);

namespace EatLf\ShippingGraphQl\Model\Resolver;

use EatLf\Shipping\Api\ShippingManagementInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;

class ShippingDetails implements ResolverInterface
{
    /**
     * @var \Magento\QuoteGraphQl\Model\Cart\GetCartForUser
     */
    private GetCartForUser $getCartForUser;
    /**
     * @var \EatLf\Shipping\Api\ShippingManagementInterface
     */
    private ShippingManagementInterface $shippingManagement;

    public function __construct(
        GetCartForUser $getCartForUser,
        ShippingManagementInterface $shippingManagement
    ) {
        $this->getCartForUser = $getCartForUser;
        $this->shippingManagement = $shippingManagement;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        return $this->shippingManagement->shippingDetails(
            $value['model']
        );
    }
}
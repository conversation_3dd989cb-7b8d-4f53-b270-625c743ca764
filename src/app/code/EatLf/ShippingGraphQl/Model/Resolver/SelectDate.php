<?php
declare(strict_types=1);

namespace EatLf\ShippingGraphQl\Model\Resolver;

use EatLf\Shipping\Api\ShippingManagementInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;

class SelectDate implements ResolverInterface
{
    /**
     * @var \Magento\QuoteGraphQl\Model\Cart\GetCartForUser
     */
    private GetCartForUser $getCartForUser;
    /**
     * @var \EatLf\Shipping\Api\ShippingManagementInterface
     */
    private ShippingManagementInterface $shippingManagement;
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;

    public function __construct(
        GetCartForUser $getCartForUser,
        ShippingManagementInterface $shippingManagement,
        CartRepositoryInterface $cartRepository
    ) {
        $this->getCartForUser = $getCartForUser;
        $this->shippingManagement = $shippingManagement;
        $this->cartRepository = $cartRepository;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (empty($args['cartId'])) {
            throw new GraphQlInputException(__('Required parameter "cartId" is missing'));
        }

        if (empty($args['date'])) {
            throw new GraphQlInputException(__('Required parameter "partnerId" is missing'));
        }

        try {
            $date = new \DateTime($args['date']);
        } catch (\Exception $e) {
            throw new GraphQlInputException(__('Date parameter should be in YYYY-MM-DD format'));
        }

        $cart = $this->getCartForUser->execute(
            $args['cartId'],
            $context->getUserId(),
            (int)$context->getExtensionAttributes()->getStore()->getId()
        );

        $this->shippingManagement->selectDate($cart, $date);

        $cart->removeAllItems();
        $this->cartRepository->save($cart);

        return [
            'model' => $cart,
        ];
    }
}
<?php
declare(strict_types=1);

namespace EatLf\ShippingGraphQl\Model\Resolver;

use EatLf\Shipping\Api\ShippingDateManagementInterface;
use EatLf\Shipping\Api\ShippingManagementInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;

class SelectPartner implements ResolverInterface
{
    /**
     * @var \Magento\QuoteGraphQl\Model\Cart\GetCartForUser
     */
    private GetCartForUser $getCartForUser;
    /**
     * @var \EatLf\Shipping\Api\ShippingManagementInterface
     */
    private ShippingManagementInterface $shippingManagement;
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;
    /**
     * @var \EatLf\Shipping\Api\ShippingDateManagementInterface
     */
    private ShippingDateManagementInterface $shippingDateManagement;

    public function __construct(
        GetCartForUser $getCartForUser,
        ShippingManagementInterface $shippingManagement,
        CartRepositoryInterface $cartRepository,
        ShippingDateManagementInterface $shippingDateManagement
    ) {
        $this->getCartForUser = $getCartForUser;
        $this->shippingManagement = $shippingManagement;
        $this->cartRepository = $cartRepository;
        $this->shippingDateManagement = $shippingDateManagement;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (empty($args['cartId'])) {
            throw new GraphQlInputException(__('Required parameter "cartId" is missing'));
        }

        if (empty($args['partnerId'])) {
            throw new GraphQlInputException(__('Required parameter "partnerId" is missing'));
        }

        $cart = $this->getCartForUser->execute(
            $args['cartId'],
            $context->getUserId(),
            (int)$context->getExtensionAttributes()->getStore()->getId()
        );

        $partenaireId = $args['partnerId'];
        $this->shippingManagement->selectPartenaire($cart, $partenaireId);

        $availableDates = $this->shippingDateManagement->getAvailableDates($partenaireId);

        if (!empty($availableDates)) {
            $this->shippingManagement->selectDate($cart, $availableDates[0]);
        } else {
            $cart->getExtensionAttributes()->getShippingData()->setShippingDate(null);
        }

        $cart->removeAllItems();
        $this->cartRepository->save($cart);

        return [
            'model' => $cart,
        ];
    }
}
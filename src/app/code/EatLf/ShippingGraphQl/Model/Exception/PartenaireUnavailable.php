<?php
declare(strict_types=1);

namespace EatLf\ShippingGraphQl\Model\Exception;

use GraphQL\Error\ClientAware;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Phrase;

class PartenaireUnavailable extends LocalizedException implements ClientAware
{
    const EXCEPTION_CATEGORY = 'graphql-partner-unavailable';

    /**
     * @var bool isSafe for clients
     */
    private bool $isSafe;

    public function __construct(Phrase $phrase, \Exception $cause = null, $code = 0)
    {
        $this->isSafe = true;
        parent::__construct($phrase, $cause, $code);
    }

    public function isClientSafe(): bool
    {
        return $this->isSafe;
    }

    /**
     * @inheritdoc
     */
    public function getCategory(): string
    {
        return self::EXCEPTION_CATEGORY;
    }
}

type Mutation {
    selectPartner(partnerId: Int!,cartId: String!): Cart @resolver(class:"EatLf\\ShippingGraphQl\\Model\\Resolver\\SelectPartner") @doc(description:"Associates the specified partner with the current quote")
    selectDate(date: String!,cartId: String!): Cart @resolver(class:"EatLf\\ShippingGraphQl\\Model\\Resolver\\SelectDate") @doc(description:"Associates the specified date with the current quote")
    autoShipping: Cart @resolver(class:"EatLf\\ShippingGraphQl\\Model\\Resolver\\AutoShipping") @doc(description:"Selects the default partner and earliest possible shipping date")
}

type ShippingDetails {
    isTodayAvailable: Boolean,
    maxOrderTime: String,
    otherDates: [String],
    partenaireName: String,
    selectedDate: String,
    selectedPartenaireId: Int,
    shippingSlot: String,
    franchiseId: Int,
    partenaireEnabled: Boolean
    customMessage: String
}

type Cart {
    shipping_details: ShippingDetails @resolver(class:"EatLf\\ShippingGraphQl\\Model\\Resolver\\ShippingDetails")
}

<?php
declare(strict_types=1);

namespace EatLf\ShippingGraphQl\Observer;

use EatLf\ShippingAutoSelect\Api\AutoShippingManagementInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Quote\Api\CartRepositoryInterface;

class AutoShipAssignedCart implements ObserverInterface
{
    /**
     * @var \EatLf\ShippingAutoSelect\Api\AutoShippingManagementInterface
     */
    private AutoShippingManagementInterface $autoShippingManagement;
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;

    public function __construct(
        AutoShippingManagementInterface $autoShippingManagement,
        CartRepositoryInterface $cartRepository
    ) {
        $this->autoShippingManagement = $autoShippingManagement;
        $this->cartRepository = $cartRepository;
    }

    public function execute(Observer $observer)
    {
        /** @var \Magento\Quote\Api\Data\CartInterface $quote */
        $cart = $observer->getData('cart');

        $customerId = $cart->getCustomer()->getId();

        $this->autoShippingManagement->autoSelect($customerId, $cart);

        $this->cartRepository->save($cart);
    }
}
@page {
  header: header;
  footer: bapPageFooter;
  margin-left: 35px;
  margin-right: 35px;
  margin-bottom: 30mm;
}

body {
  font-size: 8px;
  font-family: Helvetica, Arial;
}

p {
  margin: 0;
  padding: 0;
}

.address-table {
  border: 1px solid black;
  border-spacing: 0;
  border-collapse: collapse;
}

.address-billing {
  border: 1px solid black;
}

.address-table td {
  line-height: 150%;
}

.header p {
  margin: 5px;
  text-align: center;
}

.header .column {
  float: left;
  width: 33%;
}

.company-logo {
  display: block;
  margin-bottom: 10px;
}

.company-info {
  width: 150px;
}

.report-number {
  font-weight: bold;
  font-size: 12px;
  text-align: center;
  padding-top: 50px;
}

.address-table {
  margin-left: 70px;
  width: 100%;
}

.contact-header {
  font-weight: bold;
}

.dates-summary p, .appendix p {
  margin: 3px;
}

.dates-summary {
  margin-top: 10px;
}

.appendix {
  margin-top: 30px;
}

.totals-table, .tva-table {
  border: 1px solid black;
  border-collapse: collapse;
  width: 100%;
}

.item-table {
  border-collapse: collapse;
  width: 100%;
}

.invoice-table .footer-row td:nth-child(1) {
  padding-right: 150px;
}

.item-table .topheader-col {
  color: #003456;
  font-size: 9px;
  border: 1px solid black;
}

.item-table .bottom-header {
  background-color: #003456;
  border: 1px solid black;
}

.item-table .bottom-header th {
  border: 1px solid #003456;
  color: white;
  padding: 5px 0;
  margin: 0;
}

.item-table .bottom-header th.contribution-separator {
  border-right: 1px solid black;
}

.item-table tbody tr {
  border: 1px solid black;
}

.item-table tbody tr td {
  padding: 4px 0;
  vertical-align: middle;
  text-align: center;
  line-height: 150%;
}

.item-table tfoot tr td {
  padding: 4px 0;
  font-weight: bold;
  vertical-align: middle;
  text-align: center;
  line-height: 150%;
}

.item-table tfoot .spacer-row td {
  height: 20px;
}

.item-table tfoot .footer-row {
  border: 1px solid black;
}

.item-table tfoot .footer-row td:nth-child(1) {
  text-align: right;
}

.contribution-separator {
  border-right: 1px solid black;
}

.totals-table-spacer {
  clear: both;
  margin-top: 80px
}

.totals-table-container {
  float: right;
  width: 250px;
  text-align: right;
}

.totals-table {
  text-align: right;
  font-weight: bold;
}

.totals-table tbody tr td {
  border: 1px solid black;
  font-size: 9px;
}

.tva-table tbody tr td {
  border: 1px solid black;
  text-align: right;
  font-size: 9px;
}

.tva-table tbody tr td:nth-child(1), .totals-table tbody tr td:nth-child(1) {
  width: 110px;
}

.base-tva-table {
  font-size: 9px;
}

.condition-block {
  background-color: #DBDBDB;
  font-size: 10px;
  margin-bottom: 5px;
  padding: 2px 0 2px 0;
  text-align: center;
}

.condition-text {
  font-style: italic;
  height: 30px;
  line-height: 150%;
  padding-top: 5px;
  text-align: center;
}

.base-tva-table {
  width: 100%;
}

.base-tva-table thead tr th {
  font-weight: normal;
  text-decoration: underline;
  text-align: center;
}

.base-tva-table tbody tr td {
  text-align: right;
}

.franchise-footer {
  border: 1px solid black;
  text-align: center;
}

.franchise-footer p {
  padding: 3px;
}

.franchise-footer .franchise-bank {
  font-size: 10px;
  font-weight: bold;
}

.footer-page {
  text-align: right;
  font-style: italic;
}

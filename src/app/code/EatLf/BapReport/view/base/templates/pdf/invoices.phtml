<?php
/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \Magento\Framework\Escaper $escaper
 * @var \EatLf\BapReport\Model\ReportInvoice\ReportPdfPresenter\Entity\ViewModel $viewModel
 */
$viewModel = $block->getData('view_model');
?>
<br/>

<table class="item-table invoice-table">
    <thead>
    <tr class="bottom-header">
        <th width="10%">JOUR DE LIVRAISON</th>
        <th width="30%">FACTURE</th>
        <th width="20%" class="contribution-separator">EMAIL CLIENT</th>
        <th width="9%">TOTAL TTC</th>
        <th width="9%">TOTAL HT</th>
        <?php foreach ($viewModel->getVatRates() as $rate => $value): ?>
            <th>TVA <?= $escaper->escapeHtml($value) ?></th>
        <?php endforeach ?>
    </tr>
    </thead>
    <tbody>
    <?php foreach ($viewModel->getRowData() as $row): ?>
        <tr>
            <td>
                <?= $escaper->escapeHtml($row->getDate()) ?>
            </td>
            <td>
                <?= $escaper->escapeHtml($row->getInvoiceId()) ?>
            </td>
            <td class="contribution-separator">
                <?= $escaper->escapeHtml($row->getCustomerEmail()) ?>
            </td>
            <td>
                <?= $escaper->escapeHtml($row->getTotalInclTax()) ?>
            </td>
            <td>
                <?= $escaper->escapeHtml($row->getTotalExclTax()) ?>
            </td>
            <?php foreach ($viewModel->getVatRates() as $rate => $value): ?>
                <td>
                    <?= $escaper->escapeHtml($row->getTaxForRate($rate)) ?>
                </td>
            <?php endforeach ?>
        </tr>
    <?php endforeach; ?>
    </tbody>
    <tfoot>
    <tr class="spacer-row">
        <td></td>
    </tr>
    <tr class="footer-row">
        <td colspan="3" class="contribution-separator">TOTAL</td>
        <td><?= $escaper->escapeHtml($viewModel->getTotalInclTax()) ?></td>
        <td><?= $escaper->escapeHtml($viewModel->getTotalExclTax()) ?></td>

        <?php foreach ($viewModel->getVatRates() as $rate => $value): ?>
            <td>
                <?= $escaper->escapeHtml($viewModel->getTotalTaxForRate($rate)) ?>
            </td>
        <?php endforeach ?>
    </tr>
    </tfoot>
</table>

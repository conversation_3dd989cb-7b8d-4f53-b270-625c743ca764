<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportDay\DataGateway;

use EatLf\Bap\Model\Entity\Query\GetBapById;
use EatLf\BapReport\Model\ReportDay\ReportDataBuilder\Entity\DayTotalData;

class GetDayTotals
{
    private GetBapById $getBapById;

    public function __construct(
        GetBapById $getBapById
    ) {
        $this->getBapById = $getBapById;
    }

    public function execute(int $bapId): DayTotalData
    {
        $bap = $this->getBapById->execute($bapId);

        return new DayTotalData(
            $bap->getOrdersTotalInclTax(),
            $bap->getTotalInclTax(),
            $bap->getTotalExclTax(),
            $bap->getTaxDetails()
        );
    }
}

<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportDay\DataGateway;

use EatLf\Bap\Api\Data\BapDayInterface;
use EatLf\Bap\Model\Entity\ResourceModel\BapDay\CollectionFactory;
use EatLf\BapReport\Model\ReportDay\ReportDataBuilder\Entity\DayData;

class GetDayData
{
    private CollectionFactory $collectionFactory;

    public function __construct(CollectionFactory $collectionFactory)
    {
        $this->collectionFactory = $collectionFactory;
    }

    public function execute(int $bapId): array
    {
        $collection = $this->collectionFactory->create();

        $collection->addFieldToFilter(BapDayInterface::BAP_ID_FIELD, $bapId);

        return array_map(
            fn(BapDayInterface $item) => new DayData(
                new \DateTime($item->getDate()),
                $item->getCustomerEmails(),
                $item->getOrdersTotalInclTax(),
                $item->getTotalInclTax(),
                $item->getTotalExclTax(),
                $item->getTaxDetails()
            ),
            $collection->getItems()
        );
    }
}

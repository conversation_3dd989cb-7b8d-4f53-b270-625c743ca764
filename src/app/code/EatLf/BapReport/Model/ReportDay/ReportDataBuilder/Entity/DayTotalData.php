<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportDay\ReportDataBuilder\Entity;

class DayTotalData
{
    private float $ordersTotalInclTax;

    private float $totalInclTax;

    private float $totalExclTax;

    private array $taxDetails;

    public function __construct(
        float $ordersTotalInclTax,
        float $totalInclTax,
        float $totalExclTax,
        array $taxDetails
    ) {
        $this->ordersTotalInclTax = $ordersTotalInclTax;
        $this->totalInclTax = $totalInclTax;
        $this->totalExclTax = $totalExclTax;
        $this->taxDetails = $taxDetails;
    }

    public function getOrdersTotalInclTax(): float
    {
        return $this->ordersTotalInclTax;
    }

    public function getTotalPaidInclTax(): float
    {
        return $this->ordersTotalInclTax - $this->totalInclTax;
    }

    public function getTotalInclTax(): float
    {
        return $this->totalInclTax;
    }

    public function getTotalExclTax(): float
    {
        return $this->totalExclTax;
    }

    public function getTaxDetails(): array
    {
        return $this->taxDetails;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportDay\ReportDataBuilder;

use EatLf\BapReport\Model\ReportDay\ReportDataBuilder\Entity\ReportData;

class ReportDataBuilder implements ReportDataBuilderInterface
{
    private const APPENDIX_NUMBER = 2;

    private const APPENDIX_LABEL = 'détail de la participation employeur par jour';

    private DataGatewayInterface $dataGateway;

    public function __construct(DataGatewayInterface $dataGateway)
    {
        $this->dataGateway = $dataGateway;
    }

    public function getReportData(int $bapId): ReportData
    {
        $dayData = $this->dataGateway->getDayData($bapId);

        $totalsData = $this->dataGateway->getTotalsData($bapId);

        return new ReportData($bapId, $dayData, $totalsData, self::APPENDIX_NUMBER, self::APPENDIX_LABEL);
    }
}

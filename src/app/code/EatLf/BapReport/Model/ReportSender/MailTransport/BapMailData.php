<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportSender\MailTransport;

class BapMailData
{
    private array $franchiseData;

    private array $recipientsEmails;

    private string $reportFilePath;

    private string $reportMonth;

    private string $reportYear;

    public function __construct(
        array $franchiseData,
        array $recipientsEmails,
        string $reportFilePath,
        string $reportMonth,
        string $reportYear
    ) {
        $this->franchiseData = $franchiseData;
        $this->recipientsEmails = $recipientsEmails;
        $this->reportFilePath = $reportFilePath;
        $this->reportMonth = $reportMonth;
        $this->reportYear = $reportYear;
    }

    public function getFranchiseData(): array
    {
        return $this->franchiseData;
    }

    public function getRecipientsEmails(): array
    {
        return $this->recipientsEmails;
    }

    public function getReportFilePath(): string
    {
        return $this->reportFilePath;
    }

    public function getReportMonth(): string
    {
        return $this->reportMonth;
    }

    public function getReportYear(): string
    {
        return $this->reportYear;
    }
}

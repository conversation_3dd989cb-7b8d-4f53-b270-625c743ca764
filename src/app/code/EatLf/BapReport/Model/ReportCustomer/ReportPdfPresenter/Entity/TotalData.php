<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter\Entity;

class TotalData
{
    private int $ordersCount;

    private string $ordersTotalInclTax;

    private string $totalPaidInclTax;

    private string $totalInclTax;

    private string $totalExclTax;

    private array $taxDetails;

    public function __construct(
        string $ordersTotalInclTax,
        string $totalPaidInclTax,
        string $totalInclTax,
        string $totalExclTax,
        array $taxDetails,
        int $ordersCount
    ) {
        $this->ordersTotalInclTax = $ordersTotalInclTax;
        $this->totalPaidInclTax = $totalPaidInclTax;
        $this->totalInclTax = $totalInclTax;
        $this->totalExclTax = $totalExclTax;
        $this->taxDetails = $taxDetails;
        $this->ordersCount = $ordersCount;
    }

    public function getOrdersTotalInclTax(): string
    {
        return $this->ordersTotalInclTax;
    }

    public function getTotalPaidInclTax(): string
    {
        return $this->totalPaidInclTax;
    }

    public function getTotalInclTax(): string
    {
        return $this->totalInclTax;
    }

    public function getTotalExclTax(): string
    {
        return $this->totalExclTax;
    }

    public function getTaxForRate(string $rate): string
    {
        return $this->taxDetails[$rate];
    }

    public function getOrdersCount(): int
    {
        return $this->ordersCount;
    }
}

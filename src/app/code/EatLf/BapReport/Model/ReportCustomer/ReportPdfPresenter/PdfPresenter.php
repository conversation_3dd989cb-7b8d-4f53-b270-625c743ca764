<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter;

use EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\Entity\CustomerData;
use EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\Entity\CustomerTotalsData;
use EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\Entity\ReportData;
use EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter\Entity\RowData;
use EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter\Entity\TotalData;
use EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter\Entity\ViewModel;
use EatLf\BapReport\Model\ReportCustomer\ReportService\ReportPresenterInterface;
use EatLf\BapReport\Model\ReportService\ReportPresenterServiceInterface;

class PdfPresenter implements ReportPresenterInterface
{
    private PdfViewInterface $pdfView;

    private ReportPresenterServiceInterface $reportPresenterService;

    public function __construct(
        ReportPresenterServiceInterface $reportPresenterService,
        PdfViewInterface $pdfView
    ) {
        $this->pdfView = $pdfView;
        $this->reportPresenterService = $reportPresenterService;
    }

    public function render(ReportData $reportData): string
    {
        $viewModel = $this->buildViewModel($reportData);

        return $this->pdfView->render($viewModel);
    }

    public function buildViewModel(ReportData $reportData): ViewModel
    {
        $rows = $this->getFormattedRows($reportData->getCustomers());

        $total = $this->getFormattedTotals($reportData->getTotalsData());

        $vatRates = $this->reportPresenterService->getFormattedVatRates();

        return new ViewModel(
            $reportData->getBapId(),
            $rows,
            $total,
            $vatRates,
            $reportData->getAppendixNumber(),
            $reportData->getAppendixLabel()
        );
    }

    private function getFormattedRows(array $customerData): array
    {
        return array_map(
            fn(CustomerData $data) => new RowData(
                $data->getCustomerEmail(),
                $data->getOrdersCount(),
                $this->reportPresenterService->formatAmount($data->getOrdersTotalInclTax()),
                $this->reportPresenterService->formatAmount($data->getTotalPaidByCustomer()),
                $this->reportPresenterService->formatAmount($data->getTotalInclTax()),
                $this->reportPresenterService->formatAmount($data->getTotalExclTax()),
                $this->reportPresenterService->formatAllRates($data->getTaxDetails())
            ),
            $customerData
        );
    }

    private function getFormattedTotals(CustomerTotalsData $totalsData): TotalData
    {
        return new TotalData(
            $this->reportPresenterService->formatAmount($totalsData->getOrdersTotalInclTax()),
            $this->reportPresenterService->formatAmount($totalsData->getTotalPaidByCustomer()),
            $this->reportPresenterService->formatAmount($totalsData->getTotalInclTax()),
            $this->reportPresenterService->formatAmount($totalsData->getTotalExclTax()),
            $this->reportPresenterService->formatAllRates($totalsData->getTaxDetails()),
            $totalsData->getOrdersCount()
        );
    }
}

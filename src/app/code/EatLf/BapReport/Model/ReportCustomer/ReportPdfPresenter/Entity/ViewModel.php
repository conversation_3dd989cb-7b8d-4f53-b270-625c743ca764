<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter\Entity;

use EatLf\BapReport\Model\ReportPdf\AppendixViewModelInterface;

class ViewModel implements AppendixViewModelInterface
{
    private int $bapId;

    /**
     * @var \EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter\Entity\RowData[]
     */
    private array $customerData;

    private TotalData $totalData;

    private array $vatRates;

    private int $appendixNumber;

    private string $appendixLabel;

    public function __construct(
        int $bapId,
        array $customerData,
        TotalData $totalData,
        array $vatRates,
        int $appendixNumber,
        string $appendixLabel
    ) {
        $this->bapId = $bapId;
        $this->customerData = $customerData;
        $this->totalData = $totalData;
        $this->vatRates = $vatRates;
        $this->appendixNumber = $appendixNumber;
        $this->appendixLabel = $appendixLabel;
    }

    public function getBapId(): int
    {
        return $this->bapId;
    }

    /**
     * @return \EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter\Entity\RowData[]
     */
    public function getRowData(): array
    {
        return $this->customerData;
    }

    public function getTotalOrdersInclTax(): string
    {
        return $this->totalData->getOrdersTotalInclTax();
    }

    public function getTotalPaidInclTax(): string
    {
        return $this->totalData->getTotalPaidInclTax();
    }

    public function getTotalInclTax(): string
    {
        return $this->totalData->getTotalInclTax();
    }

    public function getTotalExclTax(): string
    {
        return $this->totalData->getTotalExclTax();
    }

    public function getTotalTaxForRate(string $rate): string
    {
        return $this->totalData->getTaxForRate($rate);
    }

    public function getTotalOrdersCount(): int
    {
        return $this->totalData->getOrdersCount();
    }

    public function getVatRates(): array
    {
        return $this->vatRates;
    }

    public function getAppendixNumber(): int
    {
        return $this->appendixNumber;
    }

    public function getAppendixLabel(): string
    {
        return $this->appendixLabel;
    }
}

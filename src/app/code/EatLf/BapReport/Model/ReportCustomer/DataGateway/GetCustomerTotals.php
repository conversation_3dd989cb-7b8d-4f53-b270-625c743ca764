<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\DataGateway;

use EatLf\Bap\Api\Data\BapCustomerInterface;
use EatLf\Bap\Model\Entity\Query\GetBapById;
use EatLf\Bap\Model\Entity\ResourceModel\BapCustomer;
use EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\Entity\CustomerTotalsData;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\DB\Select;

class GetCustomerTotals
{
    private ResourceConnection $resourceConnection;

    private Select $select;

    private AdapterInterface $connection;

    private GetBapById $getBapById;

    private int $bapId;

    private array $totals;

    public function __construct(
        GetBapById $getBapById,
        ResourceConnection $resourceConnection
    ) {
        $this->resourceConnection = $resourceConnection;
        $this->getBapById = $getBapById;
    }

    public function execute(int $bapId): CustomerTotalsData
    {
        $this->bapId = $bapId;

        $this->connection = $this->resourceConnection->getConnection();

        $this->buildSelect();

        $this->fetchTotals();

        return $this->buildResultObject();
    }

    private function buildSelect(): void
    {
        $columns = $this->getColumns();

        $this->select = $this->connection->select()
            ->from(BapCustomer::TABLE_NAME, $columns)
            ->group(BapCustomerInterface::BAP_ID_FIELD)
            ->where(sprintf('%s = ?', BapCustomerInterface::BAP_ID_FIELD), $this->bapId);
    }

    private function getColumns(): array
    {
        return [
            'order_count' => new \Zend_Db_Expr(
                sprintf('sum(%s)', BapCustomerInterface::ORDER_COUNT_FIELD)
            ),
        ];
    }

    private function fetchTotals(): void
    {
        $this->totals = $this->connection->fetchRow($this->select);
    }

    private function buildResultObject(): CustomerTotalsData
    {
        $bap = $this->getBapById->execute($this->bapId);

        return new CustomerTotalsData(
            (int)$this->totals['order_count'],
            $bap->getOrdersTotalInclTax(),
            $bap->getTotalInclTax(),
            $bap->getTotalExclTax(),
            $bap->getTaxDetails()
        );
    }
}

<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\DataGateway;

use EatLf\Bap\Api\Data\BapCustomerInterface;
use EatLf\Bap\Model\Entity\ResourceModel\BapCustomer\CollectionFactory;
use EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\Entity\CustomerData;

class GetCustomerData
{
    private CollectionFactory $collectionFactory;

    public function __construct(CollectionFactory $collectionFactory)
    {
        $this->collectionFactory = $collectionFactory;
    }

    public function execute(int $bapId): array
    {
        $collection = $this->collectionFactory->create();

        $collection->addFieldToFilter(BapCustomerInterface::BAP_ID_FIELD, $bapId);

        return array_map(
            fn(BapCustomerInterface $item) => new CustomerData(
                $item->getEmail(),
                $item->getOrdersCount(),
                $item->getOrdersTotalInclTax(),
                $item->getTotalInclTax(),
                $item->getTotalExclTax(),
                $item->getTaxDetails()
            ),
            $collection->getItems()
        );
    }
}

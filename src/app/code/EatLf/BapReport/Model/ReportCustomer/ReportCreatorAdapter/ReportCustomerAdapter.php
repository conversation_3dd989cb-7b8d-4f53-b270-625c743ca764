<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\ReportCreatorAdapter;

use EatLf\BapReport\Model\ReportCreator\Service\ReportInterface;
use EatLf\BapReport\Model\ReportCustomer\ReportService\ReportServiceInterface;

class ReportCustomerAdapter implements ReportInterface
{
    private ReportServiceInterface $reportService;

    public function __construct(ReportServiceInterface $reportService)
    {
        $this->reportService = $reportService;
    }

    public function generatePdf(int $bapId): string
    {
        return $this->reportService->generateReport($bapId);
    }
}

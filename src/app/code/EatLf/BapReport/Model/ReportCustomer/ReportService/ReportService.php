<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\ReportService;

use EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\DataBuilderInterface;

class ReportService implements ReportServiceInterface
{
    private DataBuilderInterface $dataBuilder;

    private ReportPresenterInterface $presenter;

    public function __construct(DataBuilderInterface $dataBuilder, ReportPresenterInterface $presenter)
    {
        $this->dataBuilder = $dataBuilder;
        $this->presenter = $presenter;
    }

    public function generateReport(int $bapId): string
    {
        $viewModel = $this->dataBuilder->build($bapId);

        return $this->presenter->render($viewModel);
    }
}

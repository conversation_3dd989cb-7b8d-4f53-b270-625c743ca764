<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder;

use EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\Entity\ReportData;

class DataBuilder implements DataBuilderInterface
{
    private const APPENDIX_NUMBER = 3;

    private const APPENDIX_LABEL = 'détail de la participation employeur par client';

    private DataGatewayInterface $dataGateway;

    public function __construct(DataGatewayInterface $dataGateway)
    {
        $this->dataGateway = $dataGateway;
    }

    public function build(int $bapId): ReportData
    {
        $customerData = $this->dataGateway->getCustomerData($bapId);

        $totalData = $this->dataGateway->getCustomerTotalData($bapId);

        return new ReportData(
            $bapId,
            $customerData,
            $totalData,
            self::APPENDIX_NUMBER,
            self::APPENDIX_LABEL
        );
    }
}

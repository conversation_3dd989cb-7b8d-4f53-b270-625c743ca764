<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\Entity;

class ReportData
{
    private int $bapId;

    /**
     * @var \EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\Entity\CustomerData[]
     */
    private array $customers;

    private CustomerTotalsData $totalsData;

    private int $appendixNumber;

    private string $appendixLabel;

    public function __construct(
        int $bapId,
        array $customers,
        CustomerTotalsData $totalsData,
        int $appendixNumber,
        string $appendixLabel
    ) {
        $this->bapId = $bapId;
        $this->customers = $customers;
        $this->totalsData = $totalsData;
        $this->appendixNumber = $appendixNumber;
        $this->appendixLabel = $appendixLabel;
    }

    public function getBapId(): int
    {
        return $this->bapId;
    }

    public function getCustomers(): array
    {
        return $this->customers;
    }

    public function getTotalsData(): CustomerTotalsData
    {
        return $this->totalsData;
    }

    public function getAppendixNumber(): int
    {
        return $this->appendixNumber;
    }

    public function getAppendixLabel(): string
    {
        return $this->appendixLabel;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\Entity;

class CustomerTotalsData
{
    private int $ordersCount;

    private float $ordersTotalInclTax;

    private float $totalInclTax;

    private float $totalExclTax;

    private array $taxDetails;

    public function __construct(
        int $ordersCount,
        float $ordersTotalInclTax,
        float $totalInclTax,
        float $totalExclTax,
        array $taxDetails
    ) {
        $this->ordersCount = $ordersCount;
        $this->ordersTotalInclTax = $ordersTotalInclTax;
        $this->totalInclTax = $totalInclTax;
        $this->totalExclTax = $totalExclTax;
        $this->taxDetails = $taxDetails;
    }

    public function getTotalPaidByCustomer(): float
    {
        return $this->ordersTotalInclTax - $this->totalInclTax;
    }

    public function getOrdersCount(): int
    {
        return $this->ordersCount;
    }

    public function getOrdersTotalInclTax(): float
    {
        return $this->ordersTotalInclTax;
    }

    public function getTotalInclTax(): float
    {
        return $this->totalInclTax;
    }

    public function getTotalExclTax(): float
    {
        return $this->totalExclTax;
    }

    public function getTaxDetails(): array
    {
        return $this->taxDetails;
    }
}

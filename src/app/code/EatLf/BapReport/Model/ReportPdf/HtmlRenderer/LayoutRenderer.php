<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportPdf\HtmlRenderer;

use EatLf\BapReport\Model\ReportPdf\HtmlToPdfRenderer\HtmlRendererInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\LayoutInterface;

class LayoutRenderer implements HtmlRendererInterface
{
    private LayoutInterface $layout;

    public function __construct(LayoutInterface $layout)
    {
        $this->layout = $layout;
    }

    public function renderHtml(ArgumentInterface $viewModel, string $template): string
    {
        return $this->layout->createBlock(Template::class)
            ->setTemplate($template)
            ->setData('view_model', $viewModel)
            ->toHtml();
    }
}

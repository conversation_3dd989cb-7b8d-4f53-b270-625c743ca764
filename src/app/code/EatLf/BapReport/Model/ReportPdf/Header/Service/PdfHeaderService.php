<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportPdf\Header\Service;

use EatLf\BapReport\Model\ReportPdf\HeaderViewModel;
use EatLf\BapReport\Model\ReportPdf\HtmlToPdfRenderer\PdfHeaderServiceInterface;
use EatLf\BapReport\Model\ReportService\HeaderData;
use EatLf\BapReport\Model\ReportService\HeaderServiceInterface;
use EatLf\BapReport\Model\ReportService\ReportPresenterServiceInterface;

class PdfHeaderService implements PdfHeaderServiceInterface
{
    private HeaderData $headerData;

    private ReportPresenterServiceInterface $reportPresenterService;

    private FileSystemGatewayInterface $fileSystemGateway;

    private HeaderServiceInterface $headerService;

    public function __construct(
        ReportPresenterServiceInterface $reportPresenterService,
        FileSystemGatewayInterface $fileSystemGateway,
        HeaderServiceInterface $headerService
    ) {
        $this->reportPresenterService = $reportPresenterService;
        $this->fileSystemGateway = $fileSystemGateway;
        $this->headerService = $headerService;
    }

    public function getAppendixViewModel(int $bapId, int $appendixNumber, string $appendixLabel): HeaderViewModel
    {
        return $this->buildViewModel($bapId, $appendixNumber, $appendixLabel);
    }

    public function getViewModel(int $bapId): HeaderViewModel
    {
        return $this->buildViewModel($bapId);
    }

    private function buildViewModel(
        int $bapId,
        int $appendixNumber = null,
        ?string $appendixLabel = null
    ): HeaderViewModel {
        $this->headerData = $this->headerService->getHeaderData($bapId);

        return new HeaderViewModel(
            $this->getBillingContact(),
            $this->getBillingAddress(),
            $this->headerData->getFranchiseName(),
            $this->headerData->getFranchiseStreet(),
            $this->getFranchiseFullAddress(),
            $this->getFranchiseCompany(),
            $this->getFranchiseBank(),
            $this->getFranchiseCity(),
            $this->headerData->getIncrementId(),
            $this->reportPresenterService->getFullDate($this->headerData->getPeriodEndDate()),
            $this->reportPresenterService->getDateRange(
                $this->headerData->getPeriodStartDate(),
                $this->headerData->getPeriodEndDate(),
            ),
            $this->fileSystemGateway->getLogoPath('logo_print.png'),
            $appendixNumber,
            $appendixLabel
        );
    }

    private function getBillingContact(): string
    {
        return mb_strtoupper(
            $this->headerData->getContributionFirstName() .
            ' ' .
            $this->headerData->getContributionLastName() .
            ' / ' .
            $this->headerData->getContributionPhoneNumber() .
            ' / ' .
            $this->headerData->getContributionAddress()
        );
    }

    private function getBillingAddress(): string
    {
        return mb_strtoupper(
            $this->headerData->getPartenaireName() .
            ' / ' .
            $this->headerData->getPartenaireAddress() .
            ' ' .
            $this->headerData->getPartenaireCity()
        );
    }

    private function getFranchiseFullAddress(): string
    {
        return mb_strtoupper($this->headerData->getFranchiseName()) .
            ' - ' .
            $this->headerData->getFranchiseStreet() .
            ' ' .
            $this->headerData->getFranchiseCity() .
            ' tel : ' .
            $this->headerData->getFranchisePhone();
    }

    private function getFranchiseCompany(): string
    {
        return 'SIRET : ' .
            $this->headerData->getFranchiseSiret() .
            ' - TVA ' .
            $this->headerData->getFranchiseVat() .
            ' - Capital ' .
            $this->headerData->getFranchiseCapital() .
            ' - Code APE ' .
            $this->headerData->getFranchiseApe();
    }

    private function getFranchiseBank(): string
    {
        return 'IBAN : ' .
            $this->headerData->getFranchiseIban() .
            ' BIC : ' .
            $this->headerData->getFranchiseCodeBanque();
    }

    private function getFranchiseCity(): string
    {
        return $this->headerData->getFranchisePostcode() .
            ' ' .
            $this->headerData->getFranchiseCity();
    }
}

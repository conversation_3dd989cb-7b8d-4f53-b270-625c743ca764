<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportPdf\HtmlToPdfRenderer;

use EatLf\BapReport\Model\ReportPdf\AppendixViewModelInterface;
use EatLf\BapReport\Model\ReportPdf\HeaderViewModel;
use EatLf\BapReport\Model\ReportPdf\HtmlToPdfRendererInterface;
use EatLf\BapReport\Model\ReportPdf\ReportViewModelInterface;
use EatLf\BapReport\Model\ReportService\ReportNameServiceInterface;

class HtmlToPdfRenderer implements HtmlToPdfRendererInterface
{
    private const HEADER_TEMPLATE = 'EatLf_BapReport::pdf/header.phtml';

    private ReportNameServiceInterface $reportNameService;

    private PdfHeaderServiceInterface $pdfHeaderService;

    private HtmlRendererInterface $htmlRenderer;

    private HtmlToPdfConverterInterface $htmlToPdfConverter;

    public function __construct(
        ReportNameServiceInterface $reportNameService,
        PdfHeaderServiceInterface $pdfHeaderService,
        HtmlRendererInterface $htmlRenderer,
        HtmlToPdfConverterInterface $htmlToPdfConverter
    ) {
        $this->reportNameService = $reportNameService;
        $this->pdfHeaderService = $pdfHeaderService;
        $this->htmlRenderer = $htmlRenderer;
        $this->htmlToPdfConverter = $htmlToPdfConverter;
    }

    public function createPdf(ReportViewModelInterface $viewModel, string $htmlTemplate): string
    {
        $bapId = $viewModel->getBapId();

        $headerViewModel = $this->getHeaderViewModel($bapId);

        $reportName = $this->getReportName($bapId);

        return $this->generatePdf($viewModel, $headerViewModel, $htmlTemplate, $reportName);
    }

    public function createAppendixPdf(AppendixViewModelInterface $viewModel, string $htmlTemplate): string
    {
        $headerViewModel = $this->getAppendixHeaderModel($viewModel);

        $reportName = $this->getAppendixReportName($viewModel);

        return $this->generatePdf($viewModel, $headerViewModel, $htmlTemplate, $reportName);
    }

    private function generatePdf(
        ReportViewModelInterface $viewModel,
        HeaderViewModel $headerViewModel,
        string $htmlTemplate,
        string $reportName
    ): string {
        $headerHtml = $this->renderHeaderHtml($headerViewModel);

        $bodyHtml = $this->renderBodyHtml($viewModel, $htmlTemplate);

        return $this->renderPdf($headerHtml . $bodyHtml, $reportName);
    }

    private function getReportName(int $bapId): string
    {
        return $this->reportNameService->getName($bapId);
    }

    private function getHeaderViewModel(int $bapId): HeaderViewModel
    {
        return $this->pdfHeaderService->getViewModel($bapId);
    }

    private function getAppendixReportName(AppendixViewModelInterface $viewModel): string
    {
        $bapId = $viewModel->getBapId();

        return $this->reportNameService->getAppendixName($bapId, $viewModel->getAppendixNumber());
    }

    private function getAppendixHeaderModel(AppendixViewModelInterface $viewModel): HeaderViewModel
    {
        $bapId = $viewModel->getBapId();

        $appendixNumber = $viewModel->getAppendixNumber();

        $appendixLabel = $viewModel->getAppendixLabel();

        return $this->pdfHeaderService->getAppendixViewModel($bapId, $appendixNumber, $appendixLabel);
    }

    private function renderHeaderHtml(HeaderViewModel $headerViewModel): string
    {
        return $this->htmlRenderer->renderHtml($headerViewModel, self::HEADER_TEMPLATE);
    }

    private function renderBodyHtml(ReportViewModelInterface $viewModel, $template): string
    {
        return $this->htmlRenderer->renderHtml($viewModel, $template);
    }

    private function renderPdf(string $html, string $fileName): string
    {
        return $this->htmlToPdfConverter->convert($html, $fileName);
    }
}

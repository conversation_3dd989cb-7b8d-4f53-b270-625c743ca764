<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportService\ReportHeader\Service\Entity;

class SourceData
{
    private BapData $bapData;

    private ContributionData $contributionData;

    private FranchiseCompanyData $franchiseCompanyData;

    private FranchiseData $franchiseData;

    private PartenaireData $partenaireData;

    public function __construct(
        BapData $bapData,
        ContributionData $contributionData,
        FranchiseData $franchiseData,
        FranchiseCompanyData $franchiseCompanyData,
        PartenaireData $partenaireData,
    ) {
        $this->bapData = $bapData;
        $this->contributionData = $contributionData;
        $this->franchiseCompanyData = $franchiseCompanyData;
        $this->franchiseData = $franchiseData;
        $this->partenaireData = $partenaireData;
    }

    public function getBapData(): BapData
    {
        return $this->bapData;
    }

    public function getContributionData(): ContributionData
    {
        return $this->contributionData;
    }

    public function getFranchiseCompanyData(): FranchiseCompanyData
    {
        return $this->franchiseCompanyData;
    }

    public function getFranchiseData(): FranchiseData
    {
        return $this->franchiseData;
    }

    public function getPartenaireData(): PartenaireData
    {
        return $this->partenaireData;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportService\ReportHeader\Service\Entity;

class PartenaireData
{
    private string $name;

    private string $city;

    private string $address;

    public function __construct(string $name, string $city, string $address)
    {
        $this->name = $name;
        $this->city = $city;
        $this->address = $address;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getAddress(): string
    {
        return $this->address;
    }
}

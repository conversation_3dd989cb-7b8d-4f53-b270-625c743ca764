<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportService\ReportHeader\Service\Entity;

class Appendix
{
    private int $number;

    private string $label;

    public function __construct(int $number, string $label)
    {
        $this->number = $number;
        $this->label = $label;
    }

    public function getNumber(): int
    {
        return $this->number;
    }

    public function getLabel(): string
    {
        return $this->label;
    }
}

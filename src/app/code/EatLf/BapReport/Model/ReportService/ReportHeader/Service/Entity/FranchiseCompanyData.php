<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportService\ReportHeader\Service\Entity;

class FranchiseCompanyData
{
    private string $siret;

    private string $vat;

    private string $ape;

    private string $capital;

    private string $iban;

    private string $codeBanque;

    public function __construct(
        string $siret,
        string $vat,
        string $ape,
        string $capital,
        string $iban,
        string $codeBanque
    ) {
        $this->siret = $siret;
        $this->vat = $vat;
        $this->ape = $ape;
        $this->capital = $capital;
        $this->iban = $iban;
        $this->codeBanque = $codeBanque;
    }

    public function getSiret(): string
    {
        return $this->siret;
    }

    public function getVat(): string
    {
        return $this->vat;
    }

    public function getApe(): string
    {
        return $this->ape;
    }

    public function getCapital(): string
    {
        return $this->capital;
    }

    public function getIban(): string
    {
        return $this->iban;
    }

    public function getCodeBanque(): string
    {
        return $this->codeBanque;
    }
}

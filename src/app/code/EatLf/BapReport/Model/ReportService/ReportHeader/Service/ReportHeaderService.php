<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportService\ReportHeader\Service;

use EatLf\BapReport\Model\ReportService\HeaderData;
use EatLf\BapReport\Model\ReportService\HeaderServiceInterface;

class ReportHeaderService implements HeaderServiceInterface
{
    private DataGatewayInterface $dataGateway;

    public function __construct(DataGatewayInterface $dataGateway)
    {
        $this->dataGateway = $dataGateway;
    }

    public function getHeaderData(int $bapId): HeaderData
    {
        $sourceData = $this->dataGateway->getHeaderData($bapId);

        $month = $sourceData->getBapData()->getMonth();
        $year = $sourceData->getBapData()->getYear();

        return new HeaderData(
            $sourceData->getBapData(),
            $sourceData->getContributionData(),
            $sourceData->getFranchiseData(),
            $sourceData->getFranchiseCompanyData(),
            $sourceData->getPartenaireData(),
            $this->getFirstDay($month, $year),
            $this->getLastDay($month, $year)
        );
    }

    private function getLastDay(int $month, int $year): \DateTimeInterface
    {
        $firstDay = $this->getFirstDay($month, $year);

        $lastDay = clone $firstDay;

        $lastDay->modify('last day of this month');

        return $lastDay;
    }

    private function getFirstDay(int $month, int $year): \DateTimeInterface
    {
        $formattedMonth = $this->formatMonthWithTwoDigits($month);

        $dateString = sprintf('%s-%s-01', $year, $formattedMonth);

        return new \DateTime($dateString);
    }

    private function formatMonthWithTwoDigits(int $month): string
    {
        return str_pad((string)$month, 2, '0', STR_PAD_LEFT);
    }
}

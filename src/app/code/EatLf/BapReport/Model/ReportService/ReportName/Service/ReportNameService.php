<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportService\ReportName\Service;

use EatLf\BapReport\Model\ReportService\ReportName\Service\Entity\ReportData;
use EatLf\BapReport\Model\ReportService\ReportNameServiceInterface;

class ReportNameService implements ReportNameServiceInterface
{
    private const APPENDIX_PREFIX = 'annexe';

    private const APPENDIX_SEPARATOR = '_';

    private DataGatewayInterface $dataGateway;

    public function __construct(DataGatewayInterface $dataGateway)
    {
        $this->dataGateway = $dataGateway;
    }

    public function getName(int $bapId): string
    {
        $reportData = $this->dataGateway->getReportData($bapId);

        $month = $this->getFormattedMonth($reportData->getMonth());

        return $this->getReportName($reportData, $month);
    }

    public function getAppendixName(int $bapId, int $appendixNumber): string
    {
        return self::APPENDIX_PREFIX . $appendixNumber . self::APPENDIX_SEPARATOR . $this->getName($bapId);
    }

    private function getFormattedMonth(int $month): string
    {
        return str_pad((string)$month, 2, '0', STR_PAD_LEFT);
    }

    private function getReportName(ReportData $reportData, string $month): string
    {
        return $reportData->getIncrementId() .
            '_' .
            preg_replace('/[^a-zA-Z0-9\.éàçè_+&-]+/', '-', $reportData->getPartenaireName()) .
            '_' .
            $reportData->getYear() .
            $month;
    }
}

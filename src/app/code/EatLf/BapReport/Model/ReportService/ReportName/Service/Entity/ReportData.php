<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportService\ReportName\Service\Entity;

class ReportData
{
    private string $incrementId;

    private string $partenaireName;

    private int $year;

    private int $month;

    public function __construct(string $incrementId, string $partenaireName, int $year, int $month)
    {
        $this->incrementId = $incrementId;
        $this->partenaireName = $partenaireName;
        $this->year = $year;
        $this->month = $month;
    }

    public function getIncrementId(): string
    {
        return $this->incrementId;
    }

    public function getPartenaireName(): string
    {
        return $this->partenaireName;
    }

    public function getYear(): int
    {
        return $this->year;
    }

    public function getMonth(): int
    {
        return $this->month;
    }
}

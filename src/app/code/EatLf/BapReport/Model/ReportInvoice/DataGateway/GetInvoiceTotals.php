<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportInvoice\DataGateway;

use EatLf\Bap\Model\Entity\Query\GetBapById;
use EatLf\BapReport\Model\ReportInvoice\ReportDataBuilder\Entity\TotalsData;

class GetInvoiceTotals
{
    private GetBapById $getBapById;

    public function __construct(
        GetBapById $getBapById
    ) {
        $this->getBapById = $getBapById;
    }

    public function execute(int $bapId): TotalsData
    {
        $bap = $this->getBapById->execute($bapId);

        return new TotalsData(
            $bap->getTotalInclTax(),
            $bap->getTotalExclTax(),
            $bap->getTaxDetails()
        );
    }
}

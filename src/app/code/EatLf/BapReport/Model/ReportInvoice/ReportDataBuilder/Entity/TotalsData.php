<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportInvoice\ReportDataBuilder\Entity;

class TotalsData
{
    private float $totalInclTax;

    private float $totalExclTax;

    private array $taxDetails;

    public function __construct(float $totalInclTax, float $totalExclTax, array $taxDetails)
    {
        $this->totalInclTax = $totalInclTax;
        $this->totalExclTax = $totalExclTax;
        $this->taxDetails = $taxDetails;
    }

    public function getContributionTotalInclTax(): float
    {
        return $this->totalInclTax;
    }

    public function getContributionTotalExclTax(): float
    {
        return $this->totalExclTax;
    }

    public function getTaxDetails(): array
    {
        return $this->taxDetails;
    }
}

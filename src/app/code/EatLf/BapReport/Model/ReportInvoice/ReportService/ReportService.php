<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportInvoice\ReportService;

use EatLf\BapReport\Model\ReportInvoice\ReportDataBuilder\ReportDataBuilderInterface;

class ReportService implements ReportServiceInterface
{
    private ReportDataBuilderInterface $reportDataBuilder;

    private ReportPresenterInterface $reportPresenter;

    public function __construct(
        ReportDataBuilderInterface $reportDataBuilder,
        ReportPresenterInterface $reportPresenter
    ) {
        $this->reportDataBuilder = $reportDataBuilder;
        $this->reportPresenter = $reportPresenter;
    }

    public function generate(int $bapId): string
    {
        $reportData = $this->reportDataBuilder->getReportData($bapId);

        return $this->reportPresenter->render($reportData);
    }
}

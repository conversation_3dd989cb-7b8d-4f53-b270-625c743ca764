<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportInvoice\HtmlToPdfView;

use EatLf\BapReport\Model\ReportInvoice\ReportPdfPresenter\Entity\ViewModel;
use EatLf\BapReport\Model\ReportInvoice\ReportPdfPresenter\PdfViewInterface;
use EatLf\BapReport\Model\ReportPdf\HtmlToPdfRendererInterface;

class PdfView implements PdfViewInterface
{
    private HtmlToPdfRendererInterface $htmlToPdfRenderer;

    public function __construct(HtmlToPdfRendererInterface $htmlToPdfRenderer)
    {
        $this->htmlToPdfRenderer = $htmlToPdfRenderer;
    }

    public function render(ViewModel $viewModel): string
    {
        return $this->htmlToPdfRenderer->createAppendixPdf($viewModel, 'EatLf_BapReport::pdf/invoices.phtml');
    }
}

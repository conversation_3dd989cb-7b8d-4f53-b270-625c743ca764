<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportInvoice\ReportPdfPresenter\Entity;

class TotalData
{
    private string $totalInclTax;

    private string $totalExclTax;

    private array $taxDetails;

    public function __construct(string $totalInclTax, string $totalExclTax, array $taxDetails)
    {
        $this->totalInclTax = $totalInclTax;
        $this->totalExclTax = $totalExclTax;
        $this->taxDetails = $taxDetails;
    }

    public function getTotalInclTax(): string
    {
        return $this->totalInclTax;
    }

    public function getTotalExclTax(): string
    {
        return $this->totalExclTax;
    }

    public function getTaxForRate(string $rate): string
    {
        return $this->taxDetails[$rate];
    }
}

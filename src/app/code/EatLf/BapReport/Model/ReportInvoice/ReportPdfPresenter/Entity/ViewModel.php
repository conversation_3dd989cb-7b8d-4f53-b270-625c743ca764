<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportInvoice\ReportPdfPresenter\Entity;

use EatLf\BapReport\Model\ReportPdf\AppendixViewModelInterface;

class ViewModel implements AppendixViewModelInterface
{
    private int $bapId;

    private TotalData $totalData;

    /**
     * @var \EatLf\BapReport\Model\ReportInvoice\ReportPdfPresenter\Entity\RowData[]
     */
    private array $rowData;

    private array $vatRates;

    private int $appendixNumber;

    private string $appendixLabel;

    public function __construct(
        int $bapId,
        TotalData $totalData,
        array $rowData,
        array $vatRates,
        int $appendixNumber,
        string $appendixLabel,
    ) {
        $this->bapId = $bapId;
        $this->totalData = $totalData;
        $this->rowData = $rowData;
        $this->vatRates = $vatRates;
        $this->appendixNumber = $appendixNumber;
        $this->appendixLabel = $appendixLabel;
    }

    public function getBapId(): int
    {
        return $this->bapId;
    }

    public function getRowData(): array
    {
        return $this->rowData;
    }

    public function getTotalInclTax(): string
    {
        return $this->totalData->getTotalInclTax();
    }

    public function getTotalExclTax(): string
    {
        return $this->totalData->getTotalExclTax();
    }

    public function getTotalTaxForRate(string $rate): string
    {
        return $this->totalData->getTaxForRate($rate);
    }

    public function getVatRates(): array
    {
        return $this->vatRates;
    }

    public function getAppendixNumber(): int
    {
        return $this->appendixNumber;
    }

    public function getAppendixLabel(): string
    {
        return $this->appendixLabel;
    }
}

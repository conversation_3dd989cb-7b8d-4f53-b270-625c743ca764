<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportBap\DataGateway;

use EatLf\Bap\Model\Entity\Query\GetBapById;
use EatLf\BapReport\Model\ReportBap\ReportDataBuilder\DataGatewayInterface;
use EatLf\BapReport\Model\ReportBap\ReportDataBuilder\Entity\ReportData;

class DbGateway implements DataGatewayInterface
{
    private GetBapById $getBapById;

    public function __construct(
        GetBapById $getBapById
    ) {
        $this->getBapById = $getBapById;
    }

    public function getReportDataByBapId(int $bapId): ReportData
    {
        $bap = $this->getBapById->execute($bapId);

        return new ReportData(
            $bap->getBapId(),
            $bap->getTotalInclTax(),
            $bap->getTotalExclTax(),
            $bap->getTaxDetails(),
            $bap->getTaxBase()
        );
    }
}

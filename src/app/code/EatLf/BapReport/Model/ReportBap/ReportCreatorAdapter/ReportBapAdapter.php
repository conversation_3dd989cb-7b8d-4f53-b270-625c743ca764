<?php
declare(strict_types=1);

namespace EatLf\BapReport\Model\ReportBap\ReportCreatorAdapter;

use EatLf\BapReport\Model\ReportBap\ReportService\ReportServiceInterface;
use EatLf\BapReport\Model\ReportCreator\Service\ReportInterface;

class ReportBapAdapter implements ReportInterface
{
    private ReportServiceInterface $reportService;

    public function __construct(ReportServiceInterface $reportService)
    {
        $this->reportService = $reportService;
    }

    public function generatePdf(int $bapId): string
    {
        return $this->reportService->generate($bapId);
    }
}

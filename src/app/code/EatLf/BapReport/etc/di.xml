<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <preference for="EatLf\BapReport\Api\GenerateReportsInterface"
                type="EatLf\BapReport\Model\ReportCreator\Service\GenerateReport"/>

    <!-- ReportBap -->

    <preference for="EatLf\BapReport\Model\ReportBap\ReportService\ReportServiceInterface"
                type="EatLf\BapReport\Model\ReportBap\ReportService\ReportService"/>

    <preference for="EatLf\BapReport\Model\ReportBap\ReportService\ReportPresenterInterface"
                type="EatLf\BapReport\Model\ReportBap\ReportPdfPresenter\PdfPresenter"/>

    <preference for="EatLf\BapReport\Model\ReportBap\ReportDataBuilder\ReportDataBuilderInterface"
                type="EatLf\BapReport\Model\ReportBap\ReportDataBuilder\ReportDataBuilder"/>

    <preference for="EatLf\BapReport\Model\ReportBap\ReportDataBuilder\DataGatewayInterface"
                type="EatLf\BapReport\Model\ReportBap\DataGateway\DbGateway"/>

    <preference for="EatLf\BapReport\Model\ReportBap\ReportPdfPresenter\PdfViewInterface"
                type="EatLf\BapReport\Model\ReportBap\HtmlToPdfView\PdfView"/>

    <!-- /ReportBap -->

    <!-- ReportInvoice -->

    <preference for="EatLf\BapReport\Model\ReportInvoice\ReportService\ReportServiceInterface"
                type="EatLf\BapReport\Model\ReportInvoice\ReportService\ReportService"/>

    <preference for="EatLf\BapReport\Model\ReportInvoice\ReportService\ReportPresenterInterface"
                type="EatLf\BapReport\Model\ReportInvoice\ReportPdfPresenter\PdfPresenter"/>

    <preference for="EatLf\BapReport\Model\ReportInvoice\ReportDataBuilder\ReportDataBuilderInterface"
                type="EatLf\BapReport\Model\ReportInvoice\ReportDataBuilder\ReportDataBuilder"/>

    <preference for="EatLf\BapReport\Model\ReportInvoice\ReportDataBuilder\DataGatewayInterface"
                type="EatLf\BapReport\Model\ReportInvoice\DataGateway\DbGateway"/>

    <preference for="EatLf\BapReport\Model\ReportInvoice\ReportPdfPresenter\PdfViewInterface"
                type="EatLf\BapReport\Model\ReportInvoice\HtmlToPdfView\PdfView"/>

    <!-- /ReportInvoice -->

    <!-- ReportDay -->

    <preference for="EatLf\BapReport\Model\ReportDay\ReportService\ReportServiceInterface"
                type="EatLf\BapReport\Model\ReportDay\ReportService\ReportService"/>

    <preference for="EatLf\BapReport\Model\ReportDay\ReportService\ReportPresenterInterface"
                type="EatLf\BapReport\Model\ReportDay\ReportPdfPresenter\PdfPresenter"/>

    <preference for="EatLf\BapReport\Model\ReportDay\ReportDataBuilder\ReportDataBuilderInterface"
                type="EatLf\BapReport\Model\ReportDay\ReportDataBuilder\ReportDataBuilder"/>

    <preference for="EatLf\BapReport\Model\ReportDay\ReportDataBuilder\DataGatewayInterface"
                type="EatLf\BapReport\Model\ReportDay\DataGateway\DbGateway"/>

    <preference for="EatLf\BapReport\Model\ReportDay\ReportPdfPresenter\PdfViewInterface"
                type="EatLf\BapReport\Model\ReportDay\HtmlToPdfView\PdfView"/>

    <!-- /ReportDay -->

    <!-- ReportCustomer -->

    <preference for="EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\DataGatewayInterface"
                type="EatLf\BapReport\Model\ReportCustomer\DataGateway\DbGateway"/>

    <preference for="EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter\PdfViewInterface"
                type="EatLf\BapReport\Model\ReportCustomer\HtmlToPdfView\PdfView"/>

    <preference for="EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\DataBuilderInterface"
                type="EatLf\BapReport\Model\ReportCustomer\ReportDataBuilder\DataBuilder"/>

    <preference for="EatLf\BapReport\Model\ReportCustomer\ReportService\ReportPresenterInterface"
                type="EatLf\BapReport\Model\ReportCustomer\ReportPdfPresenter\PdfPresenter"/>

    <preference for="EatLf\BapReport\Model\ReportCustomer\ReportService\ReportServiceInterface"
                type="EatLf\BapReport\Model\ReportCustomer\ReportService\ReportService"/>

    <!-- /ReportCustomer -->

    <!-- ReportCreator -->

    <preference for="EatLf\BapReport\Model\ReportCreator\Service\ArchiveServiceInterface"
                type="EatLf\BapReport\Model\ReportCreator\ZipArchive\ZipArchiveService"/>

    <preference for="EatLf\BapReport\Model\ReportCreator\ZipArchive\FileSystemGatewayInterface"
                type="EatLf\BapReport\Model\ReportCreator\FileSystemGateway\MagentoFileSystem"/>

    <type name="EatLf\BapReport\Model\ReportCreator\Service\GenerateReport">
        <arguments>
            <argument name="reports" xsi:type="array">
                <item name="bapReport" xsi:type="object">
                    EatLf\BapReport\Model\ReportBap\ReportCreatorAdapter\ReportBapAdapter
                </item>
                <item name="invoiceReport" xsi:type="object">
                    EatLf\BapReport\Model\ReportInvoice\ReportCreatorAdapter\ReportInvoiceAdapter
                </item>
                <item name="dayReport" xsi:type="object">
                    EatLf\BapReport\Model\ReportDay\ReportCreatorAdapter\ReportDayAdapter
                </item>
                <item name="customerReport" xsi:type="object">
                    EatLf\BapReport\Model\ReportCustomer\ReportCreatorAdapter\ReportCustomerAdapter
                </item>
            </argument>
        </arguments>
    </type>

    <!-- /ReportCreator -->

    <!-- ReportCommon -->

    <preference for="EatLf\BapReport\Model\ReportCommon\VatServiceInterface"
                type="EatLf\BapReport\Model\ReportCommon\StaticVatService"/>

    <!-- /ReportCommon -->

    <!-- ReportService -->

    <preference for="EatLf\BapReport\Model\ReportService\ReportNameServiceInterface"
                type="EatLf\BapReport\Model\ReportService\ReportName\Service\ReportNameService"/>

    <preference for="EatLf\BapReport\Model\ReportService\HeaderServiceInterface"
                type="EatLf\BapReport\Model\ReportService\ReportHeader\Service\ReportHeaderService"/>

    <preference for="EatLf\BapReport\Model\ReportService\ReportPresenterServiceInterface"
                type="EatLf\BapReport\Model\ReportService\ReportPresenter\ReportPresenterService"/>

    <preference for="EatLf\BapReport\Model\ReportService\ReportName\Service\DataGatewayInterface"
                type="EatLf\BapReport\Model\ReportService\ReportName\DataGateway\DbGateway"/>

    <preference for="EatLf\BapReport\Model\ReportService\ReportHeader\Service\DataGatewayInterface"
                type="EatLf\BapReport\Model\ReportService\ReportHeader\DataGateway\DbGateway"/>

    <!-- /ReportService -->

    <!-- ReportPdf -->

    <preference for="EatLf\BapReport\Model\ReportPdf\HtmlToPdfRenderer\HtmlRendererInterface"
                type="EatLf\BapReport\Model\ReportPdf\HtmlRenderer\LayoutRenderer"/>

    <preference for="EatLf\BapReport\Model\ReportPdf\HtmlToPdfRenderer\HtmlToPdfConverterInterface"
                type="EatLf\BapReport\Model\ReportPdf\HtmlToPdfConverter\MpdfConverter\MpdfConverter"/>

    <preference for="EatLf\BapReport\Model\ReportPdf\HtmlToPdfRenderer\PdfHeaderServiceInterface"
                type="EatLf\BapReport\Model\ReportPdf\Header\Service\PdfHeaderService"/>

    <preference for="EatLf\BapReport\Model\ReportPdf\HtmlToPdfConverter\MpdfConverter\FileSystemGatewayInterface"
                type="EatLf\BapReport\Model\ReportPdf\HtmlToPdfConverter\FileSystemGateway\MagentoFileSystem"/>

    <preference for="EatLf\BapReport\Model\ReportPdf\Header\Service\FileSystemGatewayInterface"
                type="EatLf\BapReport\Model\ReportPdf\Header\Gateway\MagentoFileSystem"/>

    <preference for="EatLf\BapReport\Model\ReportPdf\HtmlToPdfRendererInterface"
                type="EatLf\BapReport\Model\ReportPdf\HtmlToPdfRenderer\HtmlToPdfRenderer"/>

    <!-- /ReportPdf -->

    <!-- ReportSender -->

    <preference for="EatLf\BapReport\Model\ReportSender\ReportSenderInterface"
                type="EatLf\BapReport\Model\ReportSender\ReportSenderService\ReportSender"/>

    <preference for="EatLf\BapReport\Model\ReportSender\ReportSenderService\MonthlyBapsDataGatewayInterface"
                type="EatLf\BapReport\Model\ReportSender\DataGateway\DbMonthlyBapsDataGateway"/>

    <preference for="EatLf\BapReport\Model\ReportSender\ReportSenderService\ReportTransportInterface"
                type="EatLf\BapReport\Model\ReportSender\MailTransport\MagentoMailTransport"/>

    <preference for="EatLf\BapReport\Model\ReportSender\MailTransport\BapMailDataGatewayInterface"
                type="EatLf\BapReport\Model\ReportSender\DataGateway\DbBapMailDataGateway"/>

    <!-- /ReportSender -->
</config>

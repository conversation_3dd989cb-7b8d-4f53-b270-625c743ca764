<?php
declare(strict_types=1);

namespace EatLf\BapReport\Cron;

use EatLf\BapReport\Model\ReportSender\ReportSenderInterface;

class SendReports
{
    private ReportSenderInterface $reportSender;

    public function __construct(ReportSenderInterface $reportSender)
    {
        $this->reportSender = $reportSender;
    }

    public function execute()
    {
        $currentDate = new \DateTime();

        $yesterday = $currentDate->sub(new \DateInterval('P1D'));

        $month = (int)$yesterday->format('m');

        $year = (int)$yesterday->format('Y');

        $this->reportSender->sendAllReports($month, $year);
    }
}

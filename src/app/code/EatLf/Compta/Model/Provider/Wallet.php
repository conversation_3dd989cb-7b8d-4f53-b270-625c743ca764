<?php
/**
 * @category  Compta
 * @package   EatLf_Compta
 * <AUTHOR> <<EMAIL>>
 * @Copyright 2020 Adin
 * @license   Apache License Version 2.0
 */

namespace EatLf\Compta\Model\Provider;

use Ced\Wallet\Model\ResourceModel\Transaction\CollectionFactory as TransactionCollectionFactory;

/**
 * Class Wallet
 * @package EatLf\Compta\Model\Provider
 */
class Wallet implements WalletInterface
{

    /**
     * @var TransactionCollectionFactory
     */
    private $transactionCollectionFactory;

    /**
     * Invoice constructor.
     * @param InvoiceCollectionFactory $transactionCollectionFactory
     */
    public function __construct(
        TransactionCollectionFactory $transactionCollectionFactory
    )
    {
        $this->transactionCollectionFactory = $transactionCollectionFactory;
    }

    /**
     * Récupère la liste des transactions à exporter
     */
    public function getWalletToProcessCollection(
        $franchiseId,
        $extractionMonth
    )
    {
        /** @var \Magento\Sales\Model\ResourceModel\Order\Invoice\Collection $invoiceCollection */
        $transactionCollection = $this->transactionCollectionFactory->create();

        $transactionCollection
            ->getSelect()
            ->joinLeft(
                ['sales_order' => 'sales_order'],
                'main_table.order_id = sales_order.increment_id',
                [
                    'order_status' => 'sales_order.status',
                ])
            ->joinLeft(
                ['customer_entity' => 'customer_entity'],
                'main_table.customer_id = customer_entity.entity_id',
                [
                    'customer_firstname' => 'customer_entity.firstname',
                    'customer_lastname' => 'customer_entity.lastname',
                ]
            )
            ->where('CONVERT_TZ(main_table.created_at,"UTC","Europe/Paris") like "' . $extractionMonth . '%" and  main_table.franchise_id = ' . $franchiseId);

        return $transactionCollection;

    }
}
type CustomerOrder {
    shippingDatas: OrderShippingDatas @resolver(class:"EatLf\\SalesGraphQl\\Model\\Resolver\\OrderShippingDatasResolver")
}

type OrderShippingDatas {
    selectedDate: String,
    shippingSlot: String,
}

interface OrderItemInterface {
    row_total_ttc: Money! @resolver(class:"EatLf\\SalesGraphQl\\Model\\Resolver\\OrderItemDatasResolver")
}

type Customer {
    cantineOrders (
        filter: CustomerOrdersFilterInput @doc(description: "Defines the filter to use for searching customer orders"),
        currentPage: Int = 1 @doc(description: "Specifies which page of results to return. The default value is 1"),
        pageSize: Int = 20 @doc(description: "Specifies the maximum number of results to return at once. The default value is 20"),
    ): CustomerOrders @resolver(class: "EatLf\\SalesGraphQl\\Model\\Resolver\\CustomerOrders") @cache(cacheable: false)
}

type OrderAddress @doc(description: "OrderAddress contains detailed information about an order's billing and shipping addresses"){
    telephone: String @doc(description: "The telephone number")
}
<?php

declare(strict_types=1);

namespace EatLf\PartenaireContributionGraphQl\Model\Resolver;

use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use EatLf\PartenaireContribution\Api\CustomerServiceInterface;
use EatLf\PartenaireContribution\Block\Customer\Dashboard\Contribution;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Orders data resolver
 */
class PartenaireContributions implements ResolverInterface
{
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var \Magento\Framework\Api\SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $criteriaBuilder;
    private ContributionRepositoryInterface $contributionRepository;
    private CustomerRepositoryInterface $customerRepository;
    private CustomerServiceInterface $customerService;
    private Contribution $contribution;

    public function __construct(
        ScopeConfigInterface $scopeConfig,
        CustomerRepositoryInterface $customerRepository,
        CustomerServiceInterface $customerService,
        Contribution $contribution,
        ContributionRepositoryInterface $contributionRepository,
        SearchCriteriaBuilder $criteriaBuilder
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->criteriaBuilder = $criteriaBuilder;
        $this->contributionRepository = $contributionRepository;
        $this->customerRepository = $customerRepository;
        $this->customerService = $customerService;
        $this->contribution = $contribution;
    }

    /**
     * @inheritDoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }

        $customerId = $context->getUserId();

        if ($customerId === 0) {
            return false;
        }

        $contributions = [];
        /** @var Contribution $contribution */
        foreach ($this->customerService->findCustomerContributions($customerId) as $contribution) {
            $contribution->setData('formatted_amount',$this->contribution->formatAmount($contribution));

            if($contribution->getMinPaidAmount())
            {
                $contribution->setData('formatted_min_paid_amount',$this->contribution->formatPrice($contribution->getMinPaidAmount()));
            }

            if($contribution->getMinOrderAmount()) {
                $contribution->setData('formatted_min_order_amount', $this->contribution->formatPrice($contribution->getMinOrderAmount()));
            }
            $contributions[] = $contribution;
        }

        return $contributions;
    }
}

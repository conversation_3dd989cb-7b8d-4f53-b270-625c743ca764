<?php

declare(strict_types=1);

namespace EatLf\PartenaireContributionGraphQl\Model\Resolver;

use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use EatLf\PartenaireContribution\Api\CustomerServiceInterface;
use EatLf\PartenaireContribution\Block\Customer\Dashboard\Contribution;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Orders data resolver
 */
class CartAmount implements ResolverInterface
{
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var \Magento\Framework\Api\SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $criteriaBuilder;
    private ContributionRepositoryInterface $contributionRepository;
    private CustomerRepositoryInterface $customerRepository;
    private CustomerServiceInterface $customerService;
    private Contribution $contribution;

    public function __construct(
        ScopeConfigInterface $scopeConfig,
        CustomerRepositoryInterface $customerRepository,
        CustomerServiceInterface $customerService,
        Contribution $contribution,
        ContributionRepositoryInterface $contributionRepository,
        SearchCriteriaBuilder $criteriaBuilder
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->criteriaBuilder = $criteriaBuilder;
        $this->contributionRepository = $contributionRepository;
        $this->customerRepository = $customerRepository;
        $this->customerService = $customerService;
        $this->contribution = $contribution;
    }

    /**
     * @inheritDoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {

        $customerId = $context->getUserId();

        if ($customerId === 0) {
            return 0;
        }

        /** @var CartInterface $cart */
        $cart = $value['model'];
        return $cart->getData('contribution_amount');
    }
}

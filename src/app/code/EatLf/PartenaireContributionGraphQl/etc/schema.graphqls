type Customer {
    partenaireContributions : [PartenaireContribution] @resolver(class: "EatLf\\PartenaireContributionGraphQl\\Model\\Resolver\\PartenaireContributions") @cache(cacheable: false)
}

type PartenaireContribution @doc(description: "A partenaire contribution") {
    id: ID! @doc(description: "The unique ID for a `PartenaireContribution` object")
    partenaire_id: String! @doc(description: "The partenaire id of the contribution")
    partenaire_name: String! @doc(description: "The partenaire name of the contribution")
    formatted_amount: String! @doc(description: "The real formatted amount of the contribution")
    formatted_min_paid_amount: String! @doc(description: "The real formatted min_paid_amount of the contribution")
    formatted_min_order_amount: String! @doc(description: "The real formatted min_order_amount of the contribution")
    type: Int @doc(description: "The type of the contribution")
    amount: Float @doc(description: "The amount of the contribution")
    min_paid_amount: Float @doc(description: "The min paid amount of the contribution")
    min_order_amount: Float @doc(description: "The min order amount of the contribution")
}

type Cart {
    contribution_amount: Float @resolver(class:"EatLf\\PartenaireContributionGraphQl\\Model\\Resolver\\CartAmount")
}

type CustomerOrder {
    contribution_amount: Float @resolver(class:"EatLf\\PartenaireContributionGraphQl\\Model\\Resolver\\OrderAmount")
}

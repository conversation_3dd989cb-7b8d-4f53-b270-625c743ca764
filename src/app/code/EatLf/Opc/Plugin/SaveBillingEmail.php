<?php

declare(strict_types=1);

namespace EatLf\Opc\Plugin;

use Ma<PERSON>o\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\AddressInterface;
use Magento\Quote\Api\Data\PaymentInterface;
use Psr\Log\LoggerInterface;

class SaveBillingEmail
{
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;
    /**
     * @var \Magento\Customer\Api\AddressRepositoryInterface
     */
    private AddressRepositoryInterface $addressRepository;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    private LoggerInterface $logger;
    /**
     * @var \Magento\Customer\Model\Session
     */
    private Session $session;

    /**
     * @param \Magento\Quote\Api\CartRepositoryInterface $cartRepository
     * @param \Magento\Customer\Api\AddressRepositoryInterface $addressRepository
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        CartRepositoryInterface $cartRepository,
        AddressRepositoryInterface $addressRepository,
        Session $session,
        LoggerInterface $logger
    ) {
        $this->cartRepository = $cartRepository;
        $this->addressRepository = $addressRepository;
        $this->logger = $logger;
        $this->session = $session;
    }

    /**
     * Copy the billing email address to the associated customer address.
     *
     * @param $subject
     * @param $cartId
     * @param \Magento\Quote\Api\Data\PaymentInterface $paymentMethod
     * @param \Magento\Quote\Api\Data\AddressInterface|null $billingAddress
     *
     * @return null
     */
    public function beforeSavePaymentInformation(
        $subject,
        $cartId,
        PaymentInterface $paymentMethod,
        AddressInterface $billingAddress = null
    ) {
        if (!$billingAddress) {
            return null;
        }

        try {
            if ($billingAddress->getSaveInAddressBook() && !$billingAddress->getCustomerAddressId()) {
                $customerAddress = $billingAddress->exportCustomerAddress();
                $customerAddress->setCustomerId($this->session->getCustomerId());
            } else {
                $customerAddress = $this->addressRepository->getById(
                    $billingAddress->getCustomerAddressId()
                );
            }

            $customerAddress->setCustomAttribute(
                'email',
                $billingAddress->getExtensionAttributes()->getBillingEmail()
            );

            $this->addressRepository->save($customerAddress);

            $billingAddress->setSaveInAddressBook(0);
            $billingAddress->setCustomerAddressId($customerAddress->getId());
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        return null;
    }
}

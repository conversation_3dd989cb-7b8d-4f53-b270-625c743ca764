<?php
declare(strict_types=1);

namespace EatLf\Opc\Model;

use EatLf\Sales\Api\CustomerSalesInterface;
use Magento\Checkout\Model\Session;
use Magento\Framework\Exception\NoSuchEntityException;

class ShippingAddress
{
    const DISABLED_FIELDS = [
        'postcode',
        'city',
        'street',
        'region',
        'country_id',
        'company'
    ];

    /**
     * @var Session
     */
    private $checkoutSession;
    /**
     * @var CustomerSalesInterface
     */
    private $customerSales;

    public function __construct(
        Session $checkoutSession,
        CustomerSalesInterface $customerSales)
    {
        $this->checkoutSession = $checkoutSession;
        $this->customerSales = $customerSales;
    }

    public function modifyLayout(array $jsLayout): array
    {
        $shippingAddress = $this->checkoutSession->getQuote()->getShippingAddress();

        $shippingLayout = $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']
        ['children']['shippingAddress']['children']['shipping-address-fieldset']['children'];

        // Limitation de l'adresse sur une ligne
        $shippingLayout['street']['children'] = [
            $shippingLayout['street']['children'][0]
        ];
        $shippingLayout['street']['children'][0]['config']['disabled'] = true;

        // On initialise les champs avec les infos du partenaire
        $shippingLayout['company']['value'] = $shippingAddress->getCompany();
        $shippingLayout['city']['value'] = $shippingAddress->getCity();
        $shippingLayout['postcode']['value'] = $shippingAddress->getPostcode();
        $shippingLayout['country_id']['value'] = $shippingAddress->getCountryId();
        $shippingLayout['street']['children'][0]['value'] = $shippingAddress->getStreet()[0];

        $shippingLayout['firstname']['config']['validation']['validate-lf-name'] = true;
        $shippingLayout['lastname']['config']['validation']['validate-lf-name'] = true;

        // Désactivation des champs non modifiables
        foreach (self::DISABLED_FIELDS as $field) {
            $shippingLayout[$field]['config']['disabled'] = true;
        }

        $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']
        ['children']['shippingAddress']['children']['shipping-address-fieldset']['children'] = $shippingLayout;

        return $jsLayout;
    }
}

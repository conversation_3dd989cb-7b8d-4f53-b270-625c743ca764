<?php

namespace EatLf\Shipping\Controller\Date;

use DateTime;
use EatLf\Shipping\Api\ShippingManagementInterface;
use Exception;
use Laminas\Validator\Date;
use Magento\Checkout\Model\Cart as CustomerCart;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Exception\LocalizedException;

class Select extends Action
{
    /**
     * @var ShippingManagementInterface
     */
    private $shippingManagement;
    /**
     * @var JsonFactory
     */
    private $jsonFactory;
    /**
     * @var ManagerInterface
     */
    private $eventManager;
    /**
     * @var CustomerCart
     */
    private $cart;

    public function __construct(
        Context $context,
        ShippingManagementInterface $shippingManagement,
        JsonFactory $jsonFactory,
        CustomerCart $cart,
        ManagerInterface $eventManager
    )
    {
        parent::__construct($context);

        $this->shippingManagement = $shippingManagement;
        $this->jsonFactory = $jsonFactory;
        $this->eventManager = $eventManager;
        $this->cart = $cart;
    }

    /**
     * @return Json|void
     * @throws Exception
     */
    public function execute()
    {
        if (!$this->getRequest()->isAjax()) {
            $this->_forward('noroute');
            return;
        }

        $response = $this->jsonFactory->create();
        $quote = $this->cart->getQuote();
        $date = $this->getRequest()->getParam('date');
        $validator = new Date();

        if (!$validator->isValid($date)) {
            return $response->setData([
                'error' => true,
                'messages' => [
                    __('Invalid date format provided')
                ]
            ]);
        }

        try {
            $this->shippingManagement->selectDate(
                $quote,
                new DateTime($date)
            );
        } catch (LocalizedException $e) {
            return $response->setData([
                'error' => true,
                'messages' => [
                    $e->getMessage()
                ]
            ]);
        }

        $this->cart->save();
        $this->eventManager->dispatch('eatlf_shipping_dateselected', ['quote' => $quote]);

        return $response->setData([
            'success' => true
        ]);
    }
}

<?php

namespace EatLf\Shipping\Model;

use DateTime;
use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\Shipping\Api\ShippingDateManagementInterface;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Lf\Franchise\Model\Franchise;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class ShippingDateManagement implements ShippingDateManagementInterface
{
    /**
     * @var PartenaireRepositoryInterface
     */
    private $partenaireRepository;
    /**
     * @var TimezoneInterface
     */
    private $timezone;
    /**
     * @var FranchiseRepositoryInterface
     */
    private $franchiseRepository;
    /**
     * @var DateTime[]
     */
    private $availableDates = null;

    public function __construct(
        PartenaireRepositoryInterface $partenaireRepository,
        FranchiseRepositoryInterface $franchiseRepository,
        TimezoneInterface $timezone
    )
    {
        $this->partenaireRepository = $partenaireRepository;
        $this->timezone = $timezone;
        $this->franchiseRepository = $franchiseRepository;
    }

    /**
     * {@inheritDoc}
     */
    public function isTodayAvailable($partenaireId)
    {
        $dates = $this->getAvailableDates($partenaireId);
        $now = $this->timezone->date()->format('Ymd');

        foreach ($dates as $date) {
            if ($date->format('Ymd') === $now) {
                return true;
            }
        }

        return false;
    }

    /**
     * {@inheritDoc}
     */
    public function isValid(DateTime $date, $partenaireId)
    {
        foreach ($this->getAvailableDates($partenaireId) as $otherDate) {
            if ($otherDate->format('Ymd') === $date->format('Ymd')) {
                return true;
            }
        }

        return false;
    }

    /**
     * {@inheritDoc}
     */
    public function getAvailableDates($partenaireId)
    {
        if ($this->availableDates !== null) {
            return $this->availableDates;
        }

        try {
            $partenaire = $this->partenaireRepository->getById($partenaireId);
            /** @var Franchise $franchise */
            $franchise = $this->franchiseRepository->getById($partenaire->getFranchiseId());
        } catch (NoSuchEntityException $e) {
            return [];
        }

        $now = $this->timezone->date();
        // Take one more day than necessary to remain with n days if today is unavailable
        $nextDays = $franchise->getNextWorkedDays(self::AVAILABLE_DATES + 1);

        // Check if today is still available
        if (count($nextDays) > 0) {
            /** @var DateTime $firstDay */
            $firstDay = $nextDays[0];

            if (
                $firstDay->format('Ymd') === $now->format('Ymd') &&
                $partenaire->getLastOrderTime() < $now->format('Gi')
            ) {
                $nextDays = array_slice($nextDays, 1);
            }
        }

        $this->availableDates = array_slice(
            $nextDays,
            0,
            self::AVAILABLE_DATES
        );

        return $this->availableDates;
    }
}
<?php

namespace EatLf\Shipping\Api;

interface ShippingDateManagementInterface
{
    const AVAILABLE_DATES = 3;

    /**
     * Checks whether an order can be placed today for the specified partner.
     *
     * @param int $partenaireId
     * @return bool
     */
    public function isTodayAvailable($partenaireId);

    /**
     * Returns the ordered (asc) list of available shipping dates for the specified partner id.
     *
     * @param int $partenaireId
     * @return \DateTime[]
     */
    public function getAvailableDates($partenaireId);

    /**
     * Checks whether the date can be selected for the specified partner
     *
     * @param int $partenaireId
     * @return bool
     */
    public function isValid(\DateTime $date, $partenaireId);
}
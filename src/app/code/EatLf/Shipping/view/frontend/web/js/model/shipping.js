define([
    'underscore',
    'knockout',
    'mage/storage',
    'EatLf_ShippingBar/js/model/session-storage-mgt',
    '../action/fetch-shipping'
], function (
    _,
    ko,
    storage,
    shippingBarSessionStorage,
    fetchShipping
) {
    'use strict';

    var shippingData = ko.observable({});
    var shippingModel = {
        data: shippingData,

        isComplete: function () {
            var data = this.data();

            return !_.isEmpty(data) &&
                data.selectedPartenaire !== null &&
                data.selectedDate != null;
        },

        resetTypeAndLocality: function () {
            var data = this.data();

            data.typeId = null;
            data.localityId = null;
            shippingBarSessionStorage.clear();

            this.data(data);
        },

        refresh: function () {
            fetchShipping().done(function (data) {
                shippingData(data);
                if(typeof data.localityId !== 'undefined')
                {
                    shippingBarSessionStorage.setType(parseInt(data.typeId));
                    shippingBarSessionStorage.setLocality(parseInt(data.localityId));
                }
            });
        }
    };

    shippingModel.refresh();

    return shippingModel;
});
<?php

namespace EatLf\Shipping\Rewrite\Model\ResourceModel;

class ShippingResource extends \Lf\Opc\Model\ResourceModel\ShippingResource
{
    /**
     * The franchise_id is not stored in quote_shipping_datas in eatlf. For compatibility with code in Lf
     * it is fetched from the partenaire table.
     *
     * @param string $field
     * @param mixed $value
     * @param \Magento\Framework\Model\AbstractModel $object
     * @return \Magento\Framework\DB\Select
     */
    protected function _getLoadSelect($field, $value, $object)
    {
        return parent::_getLoadSelect($field, $value, $object)
            ->joinLeft(
                ['partenaire' => $this->getTable('partenaire')],
                'quote_shipping_datas.partenaire_id = partenaire.id',
                ['franchise_id' => 'partenaire.franchise_id']
            );
    }
}
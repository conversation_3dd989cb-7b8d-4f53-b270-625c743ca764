<?php

declare(strict_types=1);

namespace EatLf\DialogInsight\Model;

use EatLf\Livraison\Api\DeliveryNotificationSenderInterface;
use Lf\DialogInsight\Model\Api\DialogInsightApiInterface;
use Lf\DialogInsight\Model\Api\Payload\SendSingle;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class DeliveryNotificationSender implements DeliveryNotificationSenderInterface
{
    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly DialogInsightApiInterface $api,
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    public function sendDeliveryNotification(string $orderId): void
    {
        $messageId = $this->scopeConfig->getValue('dialoginsight/webapi/message_id_delivery');

        $message = new SendSingle();

        $order = $this->orderRepository->get($orderId);

        $message->setMessageId($messageId)
            ->addContactKeyField(ContactFields::EMAIL->value, $order->getCustomerEmail());

        $this->api->sendSingleSms($message);
    }
}

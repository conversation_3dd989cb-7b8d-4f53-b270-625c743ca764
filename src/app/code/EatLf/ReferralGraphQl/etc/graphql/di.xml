<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\StoreGraphQl\Model\Resolver\Store\StoreConfigDataProvider">
        <arguments>
            <argument name="extendedConfigData" xsi:type="array">
                <item name="referral_conditions_url" xsi:type="string">referral/referrer/cms_page</item>
                <item name="referral_short_text" xsi:type="string">referral/referrer/conditions</item>
                <item name="referral_share_text" xsi:type="string">referral/referrer/sharing</item>
                <item name="referral_desc_text" xsi:type="string">referral/referrer/short_text</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\QuoteGraphQl\Model\Resolver\ApplyCouponToCart">
        <plugin name="wrapRuleException" type="EatLf\ReferralGraphQl\Plugin\InvalidRuleExceptionWrapper"/>
    </type>
</config>
type Customer {
    referral_code: String @doc(description: "The customer referral code")
    canShowReferral: Boolean @resolver(class: "EatLf\\ReferralGraphQl\\Model\\Resolver\\Customer\\CanShowReferral") @doc(description: "Can the customer see his own referral code?")
}

type StoreConfig {
    referral_conditions_url: String @doc(description: "The referral conditions page url")
    referral_short_text: String @doc(description: "The referral program conditions text")
    referral_share_text: String @doc(description: "The referral program sharing text template")
    referral_desc_text: String @doc(description: "The referral program description text")
}

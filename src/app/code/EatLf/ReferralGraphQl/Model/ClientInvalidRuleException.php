<?php
declare(strict_types=1);

namespace EatLf\ReferralGraphQl\Model;

use GraphQL\Error\ClientAware;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Phrase;

class ClientInvalidRuleException extends LocalizedException implements ClientAware
{
    private const EXCEPTION_CATEGORY = 'graphql-invalid-rule';

    /**
     * @var bool isSafe for clients
     */
    private bool $isSafe;

    /**
     * Initialize object
     *
     * @param Phrase $phrase
     * @param \Exception|null $cause
     * @param int $code
     * @param bool $isSafe
     */
    public function __construct(
        Phrase $phrase,
        \Exception $cause = null,
        $code = 0,
    ) {
        $this->isSafe = true;
        parent::__construct($phrase, $cause, $code);
    }

    public function isClientSafe(): bool
    {
        return $this->isSafe;
    }

    public function getCategory(): string
    {
        return self::EXCEPTION_CATEGORY;
    }
}

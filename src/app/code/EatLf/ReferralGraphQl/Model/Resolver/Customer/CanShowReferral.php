<?php
declare(strict_types=1);

namespace EatLf\ReferralGraphQl\Model\Resolver\Customer;

use EatLf\Referral\Api\OrderManagementInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class CanShowReferral implements ResolverInterface
{
    /**
     * @var \EatLf\Referral\Api\OrderManagementInterface
     */
    private OrderManagementInterface $orderManagement;

    public function __construct(
        OrderManagementInterface $orderManagement
    ) {
        $this->orderManagement = $orderManagement;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        $currentUserId = $context->getUserId();

        if (!$currentUserId) {
            throw new GraphQlInputException(__('User not authenticated'));
        }

        return $this->orderManagement->countCompleteOrders($currentUserId) > 0;
    }
}
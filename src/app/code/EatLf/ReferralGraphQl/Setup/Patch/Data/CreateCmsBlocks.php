<?php
declare(strict_types=1);

namespace EatLf\ReferralGraphQl\Setup\Patch\Data;

use Magento\Cms\Api\BlockRepositoryInterface;
use Magento\Cms\Api\Data\BlockInterfaceFactory;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Store\Model\Store;

class CreateCmsBlocks implements DataPatchInterface
{
    /**
     * @var \Magento\Cms\Api\BlockRepositoryInterface
     */
    private BlockRepositoryInterface $blockRepository;
    /**
     * @var \Magento\Cms\Api\Data\BlockInterfaceFactory
     */
    private BlockInterfaceFactory $blockFactory;

    public function __construct(
        BlockRepositoryInterface $blockRepository,
        BlockInterfaceFactory $blockFactory
    ) {
        $this->blockRepository = $blockRepository;
        $this->blockFactory = $blockFactory;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }

    public function apply()
    {
        $mobileImageNew = $this->blockFactory
            ->create()
            ->setIdentifier('mobile-referral-menu-no-order')
            ->setTitle('[Mobile] Parrainage - Compte client pas de commande')
            ->setContent('')
            ->setData('stores', [Store::DEFAULT_STORE_ID]);

        $this->blockRepository->save($mobileImageNew);

        $mobileImage = $this->blockFactory
            ->create()
            ->setIdentifier('mobile-referral-menu')
            ->setTitle('[Mobile] Parrainage - Compte client')
            ->setContent('')
            ->setData('stores', [Store::DEFAULT_STORE_ID]);

        $this->blockRepository->save($mobileImage);

        $mobileDesc = $this->blockFactory
            ->create()
            ->setIdentifier('mobile-referral-customer-desc')
            ->setTitle('[Mobile] Parrainage - Description')
            ->setContent('')
            ->setData('stores', [Store::DEFAULT_STORE_ID]);

        $this->blockRepository->save($mobileDesc);

        $mobileImageTop = $this->blockFactory
            ->create()
            ->setIdentifier('mobile-referral-customer-top')
            ->setTitle('[Mobile] Parrainage - Compte client top')
            ->setContent('')
            ->setData('stores', [Store::DEFAULT_STORE_ID]);

        $this->blockRepository->save($mobileImageTop);
    }
}
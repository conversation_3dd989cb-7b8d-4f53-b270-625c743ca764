<?php
declare(strict_types=1);

namespace EatLf\ReferralGraphQl\Plugin;

use EatLf\Referral\Model\InvalidRuleException;
use EatLf\ReferralGraphQl\Model\ClientInvalidRuleException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\QuoteGraphQl\Model\Resolver\ApplyCouponToCart;

class InvalidRuleExceptionWrapper
{
    /**
     * Watches for \EatLf\Referral\Model\InvalidRuleException in the exception tree
     * to wrap them in a ClientAwareInterface exception.
     *
     * It enables the graphql client to receive the original invalid sales rule error message.
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function aroundResolve(
        ApplyCouponToCart $subject,
        \Closure $proceed,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        try {
            return $proceed($field, $context, $info, $value, $args);
        } catch (LocalizedException $e) {
            if (
                $e->getPrevious() !== null &&
                $e->getPrevious()->getPrevious() !== null &&
                $e->getPrevious()->getPrevious() instanceof InvalidRuleException) {
                throw new ClientInvalidRuleException(__($e->getMessage()));
            }

            throw $e;
        }
    }
}
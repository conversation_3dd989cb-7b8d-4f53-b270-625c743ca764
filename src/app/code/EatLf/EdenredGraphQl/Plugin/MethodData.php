<?php
declare(strict_types=1);

namespace EatLf\EdenredGraphQl\Plugin;

use Adin\Edenred\Model\ConfigManagement;
use Lf\Opc\Model\Ui\CheckoutConfigProvider;
use Magento\Framework\App\Area;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\QuoteGraphQl\Model\Resolver\AvailablePaymentMethods;
use Magento\Store\Model\App\Emulation;

class MethodData
{
    /**
     * @var \Lf\Opc\Model\Ui\CheckoutConfigProvider
     */
    private CheckoutConfigProvider $checkoutConfigProvider;
    /**
     * @var \Magento\Store\Model\App\Emulation
     */
    private Emulation $emulation;

    public function __construct(
        CheckoutConfigProvider $checkoutConfigProvider,
        Emulation $emulation
    ) {
        $this->checkoutConfigProvider = $checkoutConfigProvider;
        $this->emulation = $emulation;
    }

    public function afterResolve(
        AvailablePaymentMethods $subject,
        $result,
        Field $field,
        $context
    ) {
        $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();

        $this->emulation->startEnvironmentEmulation($storeId, Area::AREA_FRONTEND, true);
        $images = $this->checkoutConfigProvider->getConfig();
        $this->emulation->stopEnvironmentEmulation();

        $methods = [];

        foreach ($result as $method) {
            if ($method['code'] === ConfigManagement::METHOD_CODE) {
                $method['image'] = $images['edenred_logo'];
            }

            $methods[] = $method;
        }

        return $methods;
    }
}

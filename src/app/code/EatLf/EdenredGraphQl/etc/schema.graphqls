type Query {
    edenredStatus: <PERSON><PERSON><PERSON> @resolver(class: "EatLf\\EdenredGraphQl\\Model\\Resolver\\EdenredStatus") @doc(description:"Check Edenred login status for logged in customer") @cache(cacheable: false)
}

type Mutation {
    edenredLogin(input: EdenredLoginInput): <PERSON><PERSON><PERSON> @resolver(class: "EatLf\\EdenredGraphQl\\Model\\Resolver\\EdenredLogin") @doc(description:"Login a customer with an edenred code")
}

input EdenredLoginInput {
    code: String
}

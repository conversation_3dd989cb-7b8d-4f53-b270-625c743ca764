<?php
declare(strict_types=1);

namespace EatLf\ShippingInventory\Plugin;

use EatLf\FranchiseInventory\Api\InventoryManagementInterface;
use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\Shipping\Api\ShippingDateManagementInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Psr\Log\LoggerInterface;

class ShippingDateCheckStock
{
    /**
     * @var InventoryManagementInterface
     */
    private $inventoryManagement;
    /**
     * @var PartenaireRepositoryInterface
     */
    private $partenaireRepository;
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @var TimezoneInterface
     */
    private $timezone;

    public function __construct(
        InventoryManagementInterface $inventoryManagement,
        PartenaireRepositoryInterface $partenaireRepository,
        TimezoneInterface $timezone,
        LoggerInterface $logger
    )
    {
        $this->inventoryManagement = $inventoryManagement;
        $this->partenaireRepository = $partenaireRepository;
        $this->logger = $logger;
        $this->timezone = $timezone;
    }

    /**
     * Filter the available shipping dates to keep those with at least one product in stock.
     *
     * @param ShippingDateManagementInterface $subject
     * @param $result
     * @param $partenaireId
     * @return array
     */
    public function afterGetAvailableDates(ShippingDateManagementInterface $subject, $result, $partenaireId)
    {
        try {
            $partenaire = $this->partenaireRepository->getById($partenaireId);

            $availableDates = array_filter($result, function ($date) use ($partenaire) {
                return $this->inventoryManagement->franchiseHasStock($partenaire->getFranchiseId(), $date);
            });

            $filteredDates =  array_values($availableDates);
            $missingDates = ShippingDateManagementInterface::AVAILABLE_DATES - count($filteredDates);

            if ($missingDates === 0) {
                return $filteredDates;
            }

            $nextDates = $this->inventoryManagement->getNextAvailableDates(
                $partenaire->getFranchiseId(),
                end($filteredDates) ?: $this->timezone->date(),
                $missingDates
            );

            return array_merge($filteredDates, $nextDates);
        } catch (NoSuchEntityException $e) {
            $this->logger->error('Cannot find partenaire for id ' . $partenaireId, ['exception' => $e]);
            return [];
        }
    }
}
<?php
declare(strict_types=1);

namespace EatLf\ShippingAutoSelect\Observer;

use Magento\Customer\Model\Session;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class AutoSelectDisable implements ObserverInterface
{
    /**
     * @var Session
     */
    private $customerSession;

    public function __construct(
        Session $customerSession
    )
    {
        $this->customerSession = $customerSession;
    }

    public function execute(Observer $observer)
    {
        $this->customerSession->setData('eatlf_autoselect_disable', true);
    }
}
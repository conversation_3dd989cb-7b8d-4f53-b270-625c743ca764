<?php

namespace EatLf\ShippingAutoSelect\Observer;

use EatLf\Customer\Model\Cookies\Id;
use EatLf\SalesShipping\Api\SalesManagementInterface;
use EatLf\Shipping\Api\ShippingDateManagementInterface;
use EatLf\Shipping\Api\ShippingManagementInterface;
use EatLf\Shipping\Helper\Shipping;
use EatLf\ShippingAutoSelect\Api\AutoShippingManagementInterface;
use Magento\Checkout\Model\Cart as CustomerCart;
use Magento\Customer\Model\Session;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\NoSuchEntityException;

class AutoSelectShipping implements ObserverInterface
{
    /**
     * @var Id
     */
    private $idCookie;
    /**
     * @var Session
     */
    private $customerSession;
    /**
     * @var SalesManagementInterface
     */
    private $salesManagement;
    /**
     * @var Shipping
     */
    private $shippingHelper;
    /**
     * @var ShippingManagementInterface
     */
    private $shippingManagement;
    /**
     * @var ShippingDateManagementInterface
     */
    private $shippingDateManagement;
    /**
     * @var CustomerCart
     */
    private $cart;
    /**
     * @var \EatLf\ShippingAutoSelect\Api\AutoShippingManagementInterface
     */
    private AutoShippingManagementInterface $autoShippingManagement;

    public function __construct(
        Id $idCookie,
        Session $customerSession,
        SalesManagementInterface $salesManagement,
        Shipping $shippingHelper,
        CustomerCart $cart,
        ShippingManagementInterface $shippingManagement,
        ShippingDateManagementInterface $shippingDateManagement,
        AutoShippingManagementInterface $autoShippingManagement
    )
    {
        $this->idCookie = $idCookie;
        $this->customerSession = $customerSession;
        $this->salesManagement = $salesManagement;
        $this->shippingHelper = $shippingHelper;
        $this->shippingManagement = $shippingManagement;
        $this->shippingDateManagement = $shippingDateManagement;
        $this->cart = $cart;
        $this->autoShippingManagement = $autoShippingManagement;
    }

    /**
     * Shipping data is selected automatically based on the last customer order.
     *
     * @param Observer $observer
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(Observer $observer)
    {
        // If a flag was explicitly set to prevent autoshipping, do nothing
        if ($this->customerSession->getData('eatlf_autoselect_disable') !== null) {
            return;
        }

        // Retrieve customer id from session or cookie
        if ($this->customerSession->isLoggedIn()) {
            $customerId = $this->customerSession->getCustomerId();
        } else {
            $customerId = $this->idCookie->get();
        }

        // We can't identify the customer, nothing to do
        if ($customerId === null) {
            return;
        }

        $this->autoShippingManagement->autoSelect($customerId, $this->cart->getQuote());
        $this->cart->save();
    }
}
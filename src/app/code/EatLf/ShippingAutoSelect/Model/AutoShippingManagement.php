<?php
declare(strict_types=1);

namespace EatLf\ShippingAutoSelect\Model;

use EatLf\SalesShipping\Api\SalesManagementInterface;
use EatLf\Shipping\Api\ShippingDateManagementInterface;
use EatLf\Shipping\Api\ShippingManagementInterface;
use EatLf\Shipping\Helper\Shipping;
use EatLf\ShippingAutoSelect\Api\AutoShippingManagementInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\Data\CartInterface;

class AutoShippingManagement implements AutoShippingManagementInterface
{
    /**
     * @var \EatLf\SalesShipping\Api\SalesManagementInterface
     */
    private SalesManagementInterface $salesManagement;
    /**
     * @var \EatLf\Shipping\Api\ShippingManagementInterface
     */
    private ShippingManagementInterface $shippingManagement;
    /**
     * @var \EatLf\Shipping\Api\ShippingDateManagementInterface
     */
    private ShippingDateManagementInterface $shippingDateManagement;
    /**
     * @var \EatLf\Shipping\Helper\Shipping
     */
    private Shipping $shippingHelper;

    public function __construct(
        SalesManagementInterface $salesManagement,
        ShippingManagementInterface $shippingManagement,
        ShippingDateManagementInterface $shippingDateManagement,
        Shipping $shippingHelper
    )
    {
        $this->salesManagement = $salesManagement;
        $this->shippingManagement = $shippingManagement;
        $this->shippingDateManagement = $shippingDateManagement;
        $this->shippingHelper = $shippingHelper;
    }

    /**
     * {@inheritDoc}
     */
    public function autoSelect($customerId, CartInterface $cart): void
    {
        $shippingDatas = $this->shippingHelper->getShippingDatas($cart);

        $currentPartenaireId = $shippingDatas
            ->getExtensionAttributes()
            ->getPartenaireId();

        $currentDate = $shippingDatas
            ->getShippingDate();

        if ($currentPartenaireId !== null || $currentDate !== null) {
            return;
        }

        try {
            $partenaire = $this->salesManagement->getPartenaireForLastOrder($customerId);

            $this->shippingManagement->selectPartenaire(
                $cart,
                (int)$partenaire->getId()
            );

            $shippingDates = $this->shippingDateManagement->getAvailableDates($partenaire->getId());

            if (count($shippingDates) > 0) {
                $this->shippingManagement->selectDate(
                    $cart,
                    $shippingDates[0]
                );
            }
        } catch (NoSuchEntityException $e) {}
    }
}
<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="eatlf_shipping_refresh_before">
        <observer name="eatlf_shipping_auto_select" instance="EatLf\ShippingAutoSelect\Observer\AutoSelectShipping" />
    </event>

    <event name="eatlf_shipping_reset">
        <observer name="eatlf_disable_auto_select" instance="EatLf\ShippingAutoSelect\Observer\AutoSelectDisable" />
    </event>
</config>
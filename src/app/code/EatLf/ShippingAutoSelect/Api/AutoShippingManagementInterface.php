<?php

namespace EatLf\ShippingAutoSelect\Api;

use Magento\Quote\Api\Data\CartInterface;

interface AutoShippingManagementInterface
{
    /**
     * If possible, auto selects the last used partner and the earliest shipping date
     * available for it.
     *
     * @param $customerId
     * @param \Magento\Quote\Api\Data\CartInterface $cart
     */
    public function autoSelect($customerId, CartInterface $cart): void;
}
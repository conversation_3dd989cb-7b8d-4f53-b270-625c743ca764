<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Setup\Patch\Data;

use Magento\Cms\Api\BlockRepositoryInterface;
use Magento\Cms\Model\BlockFactory;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class CreateCustomerBlock implements DataPatchInterface
{
    /**
     * @var \Magento\Cms\Model\BlockFactory
     */
    private BlockFactory $blockFactory;
    /**
     * @var \Magento\Cms\Api\BlockRepositoryInterface
     */
    private BlockRepositoryInterface $blockRepository;

    /**
     * @param \Magento\Cms\Model\BlockFactory $blockFactory
     * @param \Magento\Cms\Api\BlockRepositoryInterface $blockRepository
     */
    public function __construct(
        BlockFactory $blockFactory,
        BlockRepositoryInterface $blockRepository
    ) {
        $this->blockFactory = $blockFactory;
        $this->blockRepository = $blockRepository;
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function apply()
    {
        $block = $this->blockFactory->create()
            ->setIdentifier('abondement-dashboard-default')
            ->setTitle('Abondement compte client')
            ->setContent('Abondement');

        $this->blockRepository->save($block);
    }
}

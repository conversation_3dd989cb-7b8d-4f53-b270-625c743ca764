<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Setup\Patch\Data;

use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class SetMysqlMessageRetention implements DataPatchInterface
{
    /**
     * @var \Magento\Framework\App\Config\Storage\WriterInterface
     */
    private WriterInterface $writer;

    public function __construct(WriterInterface $writer)
    {
        $this->writer = $writer;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }

    public function apply()
    {
        $this->writer->save('system/mysqlmq/successful_messages_lifetime', 86400);
        $this->writer->save('system/mysqlmq/failed_messages_lifetime', 86400);
        $this->writer->save('system/mysqlmq/new_messages_lifetime', 86400);
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\Api\SearchCriteria\JoinProcessor;

use Magento\Framework\Api\SearchCriteria\CollectionProcessor\JoinProcessor\CustomJoinInterface;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Db\Select;

class Employee implements CustomJoinInterface
{
    private const EMPLOYEE_ALIAS = 'epcm';

    /**
     * @inheritDoc
     */
    public function apply(AbstractDb $collection): void
    {
        $isNotApplied = !array_key_exists(
            self::EMPLOYEE_ALIAS,
            $collection->getSelect()->getPart(Select::FROM)
        );

        if ($isNotApplied) {
            $collection->getSelect()->join(
                ['epcm' => 'eatlf_partenaire_contribution_member'],
                'epcm.contribution_id = main_table.contribution_id',
                []
            );
        }
    }
}

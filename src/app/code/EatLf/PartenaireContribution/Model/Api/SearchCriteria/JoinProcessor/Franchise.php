<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\Api\SearchCriteria\JoinProcessor;

use Magento\Framework\Api\SearchCriteria\CollectionProcessor\JoinProcessor\CustomJoinInterface;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Db\Select;

class Franchise implements CustomJoinInterface
{
    private const PARTENAIRE_ALIAS = 'partenaire';

    /**
     * @inheritDoc
     */
    public function apply(AbstractDb $collection): void
    {
        $isNotApplied = !array_key_exists(
            self::PARTENAIRE_ALIAS,
            $collection->getSelect()->getPart(Select::FROM)
        );

        if ($isNotApplied) {
            $collection->getSelect()->join(
                ['partenaire' => 'partenaire'],
                'partenaire.id = main_table.partenaire_id',
                []
            );
        }
    }
}

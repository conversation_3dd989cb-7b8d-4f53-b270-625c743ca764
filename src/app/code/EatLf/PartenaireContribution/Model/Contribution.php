<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model;

use EatLf\PartenaireContribution\Api\Data\ContributionInterface;
use Magento\Framework\Model\AbstractModel;

class Contribution extends AbstractModel implements ContributionInterface
{
    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(ResourceModel\Contribution::class);
    }

    /**
     * @inheritDoc
     */
    public function getContributionId()
    {
        return $this->_getData(self::CONTRIBUTION_ID);
    }

    /**
     * @inheritDoc
     */
    public function getPartenaireId()
    {
        return $this->_getData(self::PARTENAIRE_ID);
    }

    /**
     * @inheritDoc
     */
    public function isEnabled(): bool
    {
        return $this->_getData(self::ENABLED) === '1';
    }

    /**
     * @inheritDoc
     */
    public function getType(): int
    {
        return (int)$this->_getData(self::TYPE);
    }

    /**
     * @inheritDoc
     */
    public function getAmount(): string
    {
        return $this->_getData(self::AMOUNT);
    }

    /**
     * @inheritDoc
     */
    public function getMinPaidAmount(): string
    {
        return $this->_getData(self::MIN_PAID_AMOUNT);
    }

    /**
     * @inheritDoc
     */
    public function getMinOrderAmount(): string
    {
        return $this->_getData(self::MIN_ORDER_AMOUNT);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceFirstname(): string
    {
        return $this->_getData(self::INVOICE_FIRSTNAME);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceLastname(): string
    {
        return $this->_getData(self::INVOICE_LASTNAME);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceAddress(): string
    {
        return $this->_getData(self::INVOICE_ADDRESS);
    }

    /**
     * @inheritDoc
     */
    public function getInvoicePhoneNumber(): string
    {
        return $this->_getData(self::INVOICE_PHONE_NUMBER);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceEmails(): array
    {
        return explode(',', $this->_getData(self::INVOICE_EMAILS));
    }
}

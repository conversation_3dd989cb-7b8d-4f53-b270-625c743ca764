<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model;

use EatLf\PartenaireContribution\Api\EmployeeServiceInterface;
use Magento\Framework\App\ResourceConnection;

class EmployeeService implements EmployeeServiceInterface
{
    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private ResourceConnection $resourceConnection;

    /**
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     */
    public function __construct(ResourceConnection $resourceConnection)
    {
        $this->resourceConnection = $resourceConnection;
    }

    /**
     * @inheritDoc
     */
    public function countEmployees($contributionId): int
    {
        $select = $this->resourceConnection->getConnection()
            ->select()
            ->from('eatlf_partenaire_contribution_member', new \Zend_Db_Expr('COUNT(*) as nbr'))
            ->where('contribution_id = ?', $contributionId);

        return (int)$this->resourceConnection->getConnection()->fetchRow($select)['nbr'];
    }

    /**
     * @inheritDoc
     */
    public function getEmployees($contributionId): array
    {
        $select = $this->resourceConnection->getConnection()
            ->select()
            ->from('eatlf_partenaire_contribution_member', ['email', 'telephone'])
            ->where('contribution_id = ?', $contributionId);

        return $this->resourceConnection->getConnection()->fetchAssoc($select);
    }
}

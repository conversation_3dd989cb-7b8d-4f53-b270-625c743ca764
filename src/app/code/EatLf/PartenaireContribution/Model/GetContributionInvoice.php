<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model;

use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Model\Order\Invoice;

class GetContributionInvoice
{
    /**
     * Return the contribution invoice increment id if it exists.
     *
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     *
     * @return \Magento\Sales\Model\Order\Invoice|null
     */
    public function execute(OrderInterface $order): ?Invoice
    {
        /** @var \Magento\Sales\Model\Order\Invoice $invoice */
        foreach ($order->getInvoiceCollection()->getItems() as $invoice) {
            if ($invoice->getIncrementId() && $invoice->getData('is_contribution') == 1) {
                return $invoice;
            }
        }

        return null;
    }
}

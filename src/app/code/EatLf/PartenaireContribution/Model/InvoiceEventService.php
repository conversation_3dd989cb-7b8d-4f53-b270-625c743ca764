<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model;

use EatLf\PartenaireContribution\Api\Data\InvoiceEventDataInterfaceFactory;
use EatLf\PartenaireContribution\Api\Data\InvoiceEventInterface;
use EatLf\PartenaireContribution\Api\Data\InvoiceEventInterfaceFactory;
use EatLf\PartenaireContribution\Api\Data\TaxRateInterfaceFactory;
use Magento\Sales\Model\Order\Invoice;

class InvoiceEventService
{
    /**
     * @var \EatLf\PartenaireContribution\Api\Data\InvoiceEventInterfaceFactory
     */
    private InvoiceEventInterfaceFactory $eventFactory;
    /**
     * @var \EatLf\PartenaireContribution\Api\Data\TaxRateInterfaceFactory
     */
    private TaxRateInterfaceFactory $taxRateFactory;
    /**
     * @var \EatLf\PartenaireContribution\Api\Data\InvoiceEventDataInterfaceFactory
     */
    private InvoiceEventDataInterfaceFactory $invoiceEventDataFactory;

    public function __construct(
        InvoiceEventInterfaceFactory $eventFactory,
        InvoiceEventDataInterfaceFactory $invoiceEventDataFactory,
        TaxRateInterfaceFactory $taxRateFactory
    ) {
        $this->eventFactory = $eventFactory;
        $this->taxRateFactory = $taxRateFactory;
        $this->invoiceEventDataFactory = $invoiceEventDataFactory;
    }

    /**
     * Create an InvoiceEventInterface object from the specified new invoice.
     *
     * @param \Magento\Sales\Model\Order\Invoice $invoice
     *
     * @return \EatLf\PartenaireContribution\Model\Data\InvoiceEvent
     */
    public function createAddInvoiceEvent(Invoice $invoice): InvoiceEventInterface
    {
        $order = $invoice->getOrder();
        $shippingDate = $order->getExtensionAttributes()->getShippingDate();
        $contributionId = $order->getData('contribution_id');
        $totalInclTax = $invoice->getGrandTotal();
        $totalExclTax = 0;
        $taxDetails = [];

        if ($order->getShippingTaxAmount() > 0) {
            $shippingRate = round($order->getShippingTaxAmount() / $order->getShippingAmount(), 2);
            $base = $invoice->getBaseShippingAmount();

            $taxDetails[(string)$shippingRate] = [
                'amount' => $invoice->getBaseShippingTaxAmount(),
                'base' => $base
            ];

            $totalExclTax += $base;
        }

        /** @var \Magento\Sales\Model\Order\Invoice\Item $item */
        foreach ($invoice->getAllItems() as $item) {
            $orderItem = $item->getOrderItem();

            $taxPercent = (string)($orderItem->getTaxPercent() + 0);

            $base = $item->getRowTotal() - $item->getDiscountAmount() + $item->getDiscountTaxCompensationAmount();

            $amount = $item->getTaxAmount();

            $totalExclTax += $base;

            if (!isset($taxDetails[$taxPercent])) {
                $taxDetails[$taxPercent] = [
                    'amount' => $amount,
                    'base' => $base
                ];
            } else {
                $taxDetails[$taxPercent]['amount'] += $amount;
                $taxDetails[$taxPercent]['base'] += $base;
            }
        }

        $taxDetails = array_map(
            function ($values, $rate) {
                $tax = $this->taxRateFactory->create();

                $tax->setAmount((float)$values['amount']);
                $tax->setRate((string)$rate);
                $tax->setBase((float)$values['base']);

                return $tax;
            },
            $taxDetails,
            array_keys($taxDetails)
        );

        $event = $this->eventFactory->create();
        $event->setInvoiceIncrementId($invoice->getIncrementId());

        $eventData = $this->invoiceEventDataFactory->create();

        $eventData->setFranchiseId((string)$order->getData('franchise_id'));
        $eventData->setContributionId((int)$contributionId);
        $eventData->setShippingDate($shippingDate);
        $eventData->setOrderTotalInclTax((float)$order->getGrandTotal());
        $eventData->setTotalExclTax((float)$totalExclTax);
        $eventData->setTotalInclTax((float)$totalInclTax);
        $eventData->setTaxDetails($taxDetails);
        $eventData->setCustomerEmail($order->getCustomerEmail());

        $event->setEventType(InvoiceEventInterface::TYPE_ADD);
        $event->setInvoiceEventData($eventData);

        return $event;
    }

    public function createCancelInvoiceEvent(string $invoiceIncrementId): InvoiceEventInterface
    {
        $cancelEvent = $this->eventFactory->create();

        $cancelEvent->setInvoiceIncrementId($invoiceIncrementId);
        $cancelEvent->setEventType(InvoiceEventInterface::TYPE_CANCEL);

        return $cancelEvent;
    }
}

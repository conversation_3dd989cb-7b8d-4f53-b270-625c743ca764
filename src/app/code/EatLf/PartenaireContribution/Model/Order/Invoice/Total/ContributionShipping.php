<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\Order\Invoice\Total;

use Magento\Sales\Model\Order\Invoice;
use Magento\Sales\Model\Order\Invoice\Total\AbstractTotal;

class ContributionShipping extends AbstractTotal
{
    /**
     * Collect shipping totals for contribution invoices.
     *
     * @param \Magento\Sales\Model\Order\Invoice $invoice
     *
     * @return $this
     */
    public function collect(Invoice $invoice)
    {
        $order = $invoice->getOrder();

        if (!$order->getData('contribution_id') || $order->getShippingAmount() === 0) {
            return $this;
        }

        $grandTotal = $order->getGrandTotal();
        $contributionAmount = $order->getData('contribution_amount');

        if ($order->getData('contribution_invoice')) {
            $ratio = $contributionAmount / $grandTotal;
            $this->collectContributionInvoice($invoice, $ratio);
        } else {
            $ratio = ($grandTotal - $contributionAmount) / $grandTotal;
            $this->collectCustomerInvoice($invoice, $ratio);
        }

        return $this;
    }

    /**
     * Collect the customer invoice.
     *
     * The customer invoice is created after the contribution invoice, thus we only need to add the remaining
     * shipping amount, discount and tax to the invoice.
     *
     * @param \Magento\Sales\Model\Order\Invoice $invoice
     * @param float $ratio
     */
    private function collectCustomerInvoice($invoice, $ratio)
    {
        $order = $invoice->getOrder();

        // Set prorata discount amounts
        $invoice->setDiscountAmount(
            round(
                $invoice->getDiscountAmount() - ($order->getShippingDiscountAmount() * $ratio),
                2
            )
        );

        $invoice->setBaseDiscountAmount(
            round(
                $invoice->getBaseDiscountAmount() - ($order->getBaseShippingDiscountAmount() * $ratio),
                2
            )
        );

        // Set prorata tax amounts
        $invoice->setShippingTaxAmount(round($order->getShippingTaxAmount() * $ratio, 2));
        $invoice->setBaseShippingTaxAmount(round($order->getBaseShippingTaxAmount() * $ratio, 2));
        $invoice->setShippingDiscountTaxCompensationAmount(
            round($order->getShippingDiscountTaxCompensationAmount() * $ratio, 2)
        );
        $invoice->setBaseShippingDiscountTaxCompensationAmnt(
            round($order->getBaseShippingDiscountTaxCompensationAmnt() * $ratio, 2)
        );

        // Set prorata shipping amounts
        $invoice->setShippingAmount(round($order->getShippingAmount() * $ratio, 2));
        $invoice->setBaseShippingAmount(round($order->getBaseShippingAmount() * $ratio, 2));
        $invoice->setShippingInclTax(round($order->getShippingInclTax() * $ratio, 2));
        $invoice->setBaseShippingInclTax(round($order->getBaseShippingInclTax() * $ratio, 2));

        // Recompute tax totals
        $invoice->setTaxAmount($invoice->getTaxAmount() + $invoice->getShippingTaxAmount());
        $invoice->setBaseTaxAmount($invoice->getBaseTaxAmount() + $invoice->getBaseShippingTaxAmount());

        $invoice->setDiscountTaxCompensationAmount(
            $invoice->getDiscountTaxCompensationAmount() + $invoice->getShippingDiscountTaxCompensationAmount()
        );

        $invoice->setBaseDiscountTaxCompensationAmount(
            $invoice->getBaseDiscountTaxCompensationAmount() + $invoice->getBaseShippingDiscountTaxCompensationAmnt()
        );

        // Set correct grand totals
        $invoice->setGrandTotal(
            $invoice->getGrandTotal() + $invoice->getShippingInclTax(
            ) + $invoice->getShippingDiscountTaxCompensationAmount() - round(
                $order->getShippingDiscountAmount() * $ratio,
                2
            )
        );

        $invoice->setBaseGrandTotal(
            $invoice->getBaseGrandTotal() + $invoice->getBaseShippingInclTax(
            ) + $invoice->getBaseShippingDiscountTaxCompensationAmnt() - round(
                $order->getBaseShippingDiscountAmount() * $ratio,
                2
            )
        );
    }

    /**
     * Collect the contribution invoice
     *
     * By default, Magento sets all the shipping amounts, discounts and taxes on the first invoice created.
     *
     * In this function we unset these values and correctly compute the prorata values for all shipping properties.
     *
     * @param \Magento\Sales\Model\Order\Invoice $invoice
     * @param float $ratio
     */
    private function collectContributionInvoice($invoice, $ratio)
    {
        $order = $invoice->getOrder();

        // Remove shipping / shipping discount / shipping tax from totals
        $invoice->setGrandTotal(
            $invoice->getGrandTotal() + $order->getShippingDiscountAmount() - $order->getShippingAmount()
            - $order->getShippingTaxAmount() - $order->getShippingDiscountTaxCompensationAmount()
        );
        $invoice->setBaseGrandTotal(
            $invoice->getBaseGrandTotal() + $order->getBaseShippingDiscountAmount() - $order->getBaseShippingAmount()
            - $order->getBaseShippingTaxAmount() - $order->getBaseShippingDiscountTaxCompensationAmnt()
        );

        // Set prorata discount amounts
        $invoice->setDiscountAmount(
            round(
                $invoice->getDiscountAmount() + $order->getShippingDiscountAmount(
                ) - ($order->getShippingDiscountAmount() * $ratio),
                2
            )
        );

        $invoice->setBaseDiscountAmount(
            round(
                $invoice->getBaseDiscountAmount() + $order->getBaseShippingDiscountAmount(
                ) - ($order->getBaseShippingDiscountAmount() * $ratio),
                2
            )
        );

        // Set prorata shipping amounts
        $invoice->setShippingAmount(round($order->getShippingAmount() * $ratio, 2));
        $invoice->setBaseShippingAmount(round($order->getBaseShippingAmount() * $ratio, 2));
        $invoice->setShippingInclTax(round($order->getShippingInclTax() * $ratio, 2));
        $invoice->setBaseShippingInclTax(round($order->getBaseShippingInclTax() * $ratio, 2));

        // Set prorata tax amounts
        $invoice->setShippingTaxAmount(round($order->getShippingTaxAmount() * $ratio, 2));
        $invoice->setBaseShippingTaxAmount(round($order->getBaseShippingTaxAmount() * $ratio, 2));
        $invoice->setShippingDiscountTaxCompensationAmount(
            round($order->getShippingDiscountTaxCompensationAmount() * $ratio, 2)
        );
        $invoice->setBaseShippingDiscountTaxCompensationAmnt(
            round($order->getBaseShippingDiscountTaxCompensationAmnt() * $ratio, 2)
        );

        // Recompute tax totals
        $invoice->setTaxAmount(
            $invoice->getTaxAmount() - $order->getShippingTaxAmount() + $invoice->getShippingTaxAmount()
        );

        $invoice->setBaseTaxAmount(
            $invoice->getBaseTaxAmount() - $order->getBaseShippingTaxAmount() + $invoice->getBaseShippingTaxAmount()
        );

        $invoice->setDiscountTaxCompensationAmount(
            $invoice->getDiscountTaxCompensationAmount() - $order->getShippingDiscountTaxCompensationAmount(
            ) + $invoice->getShippingDiscountTaxCompensationAmount()
        );

        $invoice->setBaseDiscountTaxCompensationAmount(
            $invoice->getBaseDiscountTaxCompensationAmount() - $order->getBaseShippingDiscountTaxCompensationAmnt(
            ) + $invoice->getBaseShippingDiscountTaxCompensationAmnt()
        );

        // Set correct grand totals
        $invoice->setGrandTotal(
            $invoice->getGrandTotal() + $invoice->getShippingInclTax(
            ) + $invoice->getShippingDiscountTaxCompensationAmount() - round(
                $order->getShippingDiscountAmount() * $ratio,
                2
            )
        );

        $invoice->setBaseGrandTotal(
            $invoice->getBaseGrandTotal() + $invoice->getBaseShippingInclTax(
            ) + $invoice->getBaseShippingDiscountTaxCompensationAmnt() - round(
                $order->getBaseShippingDiscountAmount() * $ratio,
                2
            )
        );
    }
}

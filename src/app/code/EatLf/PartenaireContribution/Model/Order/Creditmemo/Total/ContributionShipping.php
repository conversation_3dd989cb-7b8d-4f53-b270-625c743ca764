<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\Order\Creditmemo\Total;

use Magento\Sales\Model\Order\Creditmemo;
use Magento\Sales\Model\Order\Creditmemo\Total\AbstractTotal;

class ContributionShipping extends AbstractTotal
{
    /**
     * Copy contribution invoices information to creditmemos
     *
     * @param \Magento\Sales\Model\Order\Creditmemo $creditmemo
     *
     * @return $this|\EatLf\PartenaireContribution\Model\Order\Creditmemo\Total\ContributionShipping|void
     */
    public function collect(Creditmemo $creditmemo)
    {
        $invoice = $creditmemo->getInvoice();

        if (!$invoice || !$invoice->getOrder()->getData('contribution_id')) {
            return $this;
        }

        $creditmemo->setGrandTotal($invoice->getGrandTotal());
        $creditmemo->setTaxAmount($invoice->getTaxAmount());
        $creditmemo->setDiscountAmount($invoice->getDiscountAmount());
        $creditmemo->setDiscountTaxCompensationAmount($invoice->getDiscountTaxCompensationAmount());
        $creditmemo->setShippingAmount($invoice->getShippingAmount());
        $creditmemo->setShippingTaxAmount($invoice->getShippingTaxAmount());
        $creditmemo->setShippingInclTax($invoice->getShippingInclTax());
        $creditmemo->setShippingDiscountTaxCompensationAmount($invoice->getShippingDiscountTaxCompensationAmount());

        $creditmemo->setBaseGrandTotal($invoice->getBaseGrandTotal());
        $creditmemo->setBaseTaxAmount($invoice->getBaseTaxAmount());
        $creditmemo->setBaseDiscountAmount($invoice->getBaseDiscountAmount());
        $creditmemo->setBaseDiscountTaxCompensationAmount($invoice->getBaseDiscountTaxCompensationAmount());
        $creditmemo->setBaseShippingAmount($invoice->getBaseShippingAmount());
        $creditmemo->setBaseShippingTaxAmount($invoice->getBaseShippingTaxAmount());
        $creditmemo->setBaseShippingInclTax($invoice->getBaseShippingInclTax());
        $creditmemo->setBaseShippingDiscountTaxCompensationAmnt($invoice->getBaseShippingDiscountTaxCompensationAmnt());
    }
}

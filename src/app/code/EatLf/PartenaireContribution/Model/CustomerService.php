<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model;

use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use EatLf\PartenaireContribution\Api\CustomerServiceInterface;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;

class CustomerService implements CustomerServiceInterface
{
    private CustomerRepositoryInterface $customerRepository;

    private SearchCriteriaBuilder $criteriaBuilder;

    private ContributionRepositoryInterface $contributionRepository;

    private PartenaireRepositoryInterface $partenaireRepository;

    public function __construct(
        SearchCriteriaBuilder $criteriaBuilder,
        CustomerRepositoryInterface $customerRepository,
        ContributionRepositoryInterface $contributionRepository,
        PartenaireRepositoryInterface $partenaireRepository
    ) {
        $this->customerRepository = $customerRepository;
        $this->criteriaBuilder = $criteriaBuilder;
        $this->contributionRepository = $contributionRepository;
        $this->partenaireRepository = $partenaireRepository;
    }

    /**
     * @inheritDoc
     */
    public function findCustomerContributions($customerId): array
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
        } catch (\Exception $e) {
            return [];
        }

        $criteria = $this->criteriaBuilder
            ->addFilter('enabled', '1')
            ->addFilter('email', $customer->getEmail())
            ->addFilter('telephone', $this->formatCustomerTelephone($customerId))
            ->create();

        $contributionsList = $this->contributionRepository->getList($criteria)->getItems();

        foreach ($contributionsList as $contribution) {
            $partenaire = $this->partenaireRepository->getById($contribution->getPartenaireId());
            $contribution->setData('partenaire_name', $partenaire->getCompany());
        }

        return $contributionsList;
    }

    /**
     * @inheritDoc
     */
    public function formatCustomerTelephone($customerId): string
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
        } catch (\Exception $e) {
            return '';
        }

        $telAtt = $customer->getCustomAttribute('telephone');

        if ($telAtt === null) {
            return '';
        }

        return $this->formatTelephone($telAtt->getValue());
    }

    /**
     * @inheritDoc
     */
    public function formatTelephone($value): string
    {
        $phoneUtil = PhoneNumberUtil::getInstance();

        try {
            return $phoneUtil
                ->parse($value, 'FR')
                ->getNationalNumber();
        } catch (NumberParseException $e) {
            return '';
        }
    }
}

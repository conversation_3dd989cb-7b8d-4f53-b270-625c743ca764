<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\Data;

use EatLf\PartenaireContribution\Api\Data\TaxRateInterface;

class TaxRate implements TaxRateInterface
{
    /**
     * @var string
     */
    public string $rate;

    /**
     * @var float
     */
    public float $amount;

    /**
     * @var float
     */
    public float $base;

    /**
     * @inheritdoc
     */
    public function getRate(): string
    {
        return $this->rate;
    }

    /**
     * @inheritdoc
     */
    public function setRate(string $rate): void
    {
        $this->rate = $rate;
    }

    /**
     * @inheritdoc
     */
    public function getAmount(): float
    {
        return $this->amount;
    }

    /**
     * @inheritdoc
     */
    public function setAmount(float $amount): void
    {
        $this->amount = $amount;
    }

    /**
     * @inheritdoc
     */
    public function setBase(float $base): void
    {
        $this->base = $base;
    }

    /**
     * @inheritdoc
     */
    public function getBase(): float
    {
        return $this->base;
    }
}

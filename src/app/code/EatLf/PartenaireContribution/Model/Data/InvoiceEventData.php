<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\Data;

use EatLf\PartenaireContribution\Api\Data\InvoiceEventDataInterface;

class InvoiceEventData implements InvoiceEventDataInterface
{
    /**
     * @var int
     */
    private int $contributionId;
    /**
     * @var string
     */
    private string $franchiseId;
    /**
     * @var string
     */
    private string $shippingDate;
    /**
     * @var float
     */
    private float $totalInclTax;
    /**
     * @var float
     */
    private float $totalExclTax;
    /**
     * @var array
     */
    private array $taxDetails;
    /**
     * @var string
     */
    private string $customerEmail;
    /**
     * @var float
     */
    private float $orderTotalInclTax;

    /**
     * @inheritdoc
     */
    public function getContributionId(): int
    {
        return $this->contributionId;
    }

    /**
     * @inheritdoc
     */
    public function setContributionId(int $contributionId): void
    {
        $this->contributionId = $contributionId;
    }

    /**
     * @inheritdoc
     */
    public function getShippingDate(): string
    {
        return $this->shippingDate;
    }

    /**
     * @inheritdoc
     */
    public function setShippingDate(string $shippingDate): void
    {
        $this->shippingDate = $shippingDate;
    }

    /**
     * @inheritdoc
     */
    public function getTotalInclTax(): float
    {
        return $this->totalInclTax;
    }

    /**
     * @inheritdoc
     */
    public function setTotalInclTax(float $totalInclTax): void
    {
        $this->totalInclTax = $totalInclTax;
    }

    /**
     * @inheritdoc
     */
    public function getTotalExclTax(): float
    {
        return $this->totalExclTax;
    }

    /**
     * @inheritdoc
     */
    public function setTotalExclTax(float $totalExclTax): void
    {
        $this->totalExclTax = $totalExclTax;
    }

    /**
     * @inheritdoc
     */
    public function getTaxDetails(): array
    {
        return $this->taxDetails;
    }

    /**
     * @inheritdoc
     */
    public function setTaxDetails(array $taxDetails): void
    {
        $this->taxDetails = $taxDetails;
    }

    /**
     * @inheritdoc
     */
    public function getCustomerEmail(): string
    {
        return $this->customerEmail;
    }

    /**
     * @inheritdoc
     */
    public function setCustomerEmail(string $customerEmail): void
    {
        $this->customerEmail = $customerEmail;
    }

    /**
     * @inheritdoc
     */
    public function getOrderTotalInclTax(): float
    {
        return $this->orderTotalInclTax;
    }

    /**
     * @inheritdoc
     */
    public function setOrderTotalInclTax(float $orderTotalIncTax): void
    {
        $this->orderTotalInclTax = $orderTotalIncTax;
    }

    /**
     * @inheritdoc
     */
    public function getFranchiseId(): string
    {
        return $this->franchiseId;
    }

    /**
     * @inheritdoc
     */
    public function setFranchiseId(string $franchiseId): void
    {
        $this->franchiseId = $franchiseId;
    }
}

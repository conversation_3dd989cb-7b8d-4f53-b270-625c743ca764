<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\Data;

use EatLf\PartenaireContribution\Api\Data\InvoiceEventDataInterface;
use EatLf\PartenaireContribution\Api\Data\InvoiceEventInterface;

class InvoiceEvent implements InvoiceEventInterface
{
    private string $invoiceIncrementId;

    private string $eventType;

    private ?InvoiceEventDataInterface $invoiceEventData = null;

    public function getInvoiceIncrementId(): string
    {
        return $this->invoiceIncrementId;
    }

    public function setInvoiceIncrementId(string $invoiceIncrementId): void
    {
        $this->invoiceIncrementId = $invoiceIncrementId;
    }

    public function getEventType(): string
    {
        return $this->eventType;
    }

    public function setEventType(string $eventType): void
    {
        $this->eventType = $eventType;
    }

    public function getInvoiceEventData(): ?InvoiceEventDataInterface
    {
        return $this->invoiceEventData;
    }

    public function setInvoiceEventData(?InvoiceEventDataInterface $invoiceEventData): void
    {
        $this->invoiceEventData = $invoiceEventData;
    }
}

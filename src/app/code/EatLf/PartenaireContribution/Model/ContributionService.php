<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model;

use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use EatLf\PartenaireContribution\Api\ContributionServiceInterface;
use EatLf\PartenaireContribution\Api\CustomerServiceInterface;
use EatLf\PartenaireContribution\Api\Data\ContributionInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class ContributionService implements ContributionServiceInterface
{
    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;
    /**
     * @var \Magento\Sales\Api\OrderRepositoryInterface
     */
    private OrderRepositoryInterface $orderRepository;
    /**
     * @var \Magento\Framework\Api\SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $criteriaBuilder;
    /**
     * @var \EatLf\PartenaireContribution\Api\CustomerServiceInterface
     */
    private CustomerServiceInterface $customerService;
    /**
     * @var \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface
     */
    private ContributionRepositoryInterface $contributionRepository;

    /**
     * @param \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface $contributionRepository
     * @param \EatLf\PartenaireContribution\Api\CustomerServiceInterface $customerService
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $criteriaBuilder
     */
    public function __construct(
        ContributionRepositoryInterface $contributionRepository,
        CustomerServiceInterface $customerService,
        CustomerRepositoryInterface $customerRepository,
        OrderRepositoryInterface $orderRepository,
        SearchCriteriaBuilder $criteriaBuilder
    ) {
        $this->contributionRepository = $contributionRepository;
        $this->customerService = $customerService;
        $this->customerRepository = $customerRepository;
        $this->orderRepository = $orderRepository;
        $this->criteriaBuilder = $criteriaBuilder;
    }

    /**
     * @inheritDoc
     */
    public function applyContributionToCart(CartInterface $cart, float $grandTotal): void
    {
        $cart->setData('contribution_amount', 0);
        $cart->setData('contribution_id');

        if (!$this->isContributionPossible($cart, $grandTotal)) {
            return;
        }

        $contributions = $this->findEligibleContributions($cart, $grandTotal);

        foreach ($contributions as $contribution) {
            $amount = $this->calculateContributionAmount($contribution, $grandTotal);

            if ($amount > $cart->getData('contribution_amount')) {
                $cart->setData('contribution_amount', $amount);
                $cart->setData('contribution_id', $contribution->getContributionId());
            }
        }
    }

    /**
     * Calculate the amount that can be applied for a Contribution definition and a cart grand total.
     *
     * @param \EatLf\PartenaireContribution\Api\Data\ContributionInterface $contribution
     * @param float $grandTotal
     *
     * @return float
     */
    public function calculateContributionAmount(ContributionInterface $contribution, float $grandTotal): float
    {
        if ($contribution->getType() === ContributionInterface::TYPE_PERCENT) {
            $contributionAmount = round($grandTotal * $contribution->getAmount() / 100, 2);
        } else {
            $contributionAmount = $contribution->getAmount();
        }

        if ($contributionAmount > $grandTotal) {
            $contributionAmount = $grandTotal;
        }

        if ($grandTotal - $contributionAmount >= $contribution->getMinPaidAmount()) {
            return (float)$contributionAmount;
        } else {
            return $grandTotal - $contribution->getMinPaidAmount();
        }
    }

    /**
     * Return the list of active contributions matching the specified cart.
     *
     * @param \Magento\Quote\Api\Data\CartInterface $cart
     * @param float $grandTotal
     *
     * @return \EatLf\PartenaireContribution\Api\Data\ContributionInterface[]
     */
    public function findEligibleContributions(CartInterface $cart, float $grandTotal): array
    {
        $customerId = $cart->getCustomerId();

        try {
            $customer = $this->customerRepository->getById($customerId);
        } catch (\Exception $e) {
            return [];
        }

        $criteria = $this->criteriaBuilder
            ->addFilter('enabled', '1')
            ->addFilter('email', $customer->getEmail())
            ->addFilter('telephone', $this->customerService->formatCustomerTelephone($customerId))
            ->addFilter('min_order_amount', $grandTotal, 'lteq')
            ->addFilter('franchise_id', $cart->getExtensionAttributes()->getShippingData()->getFranchiseId())
            ->create();

        return $this->contributionRepository->getList($criteria)->getItems();
    }

    /**
     * Return true if it is possible to apply company contribution amount to the specified cart.
     *
     * @param \Magento\Quote\Api\Data\CartInterface $cart
     * @param float $grandTotal
     *
     * @return bool
     */
    public function isContributionPossible(CartInterface $cart, float $grandTotal): bool
    {
        $customerId = $cart->getCustomerId();
        $shippingData = $cart->getExtensionAttributes()->getShippingData();

        if ($grandTotal == 0 || !$customerId || !$shippingData || !$shippingData->getShippingDate()) {
            return false;
        }

        $criteria = $this->criteriaBuilder
            ->addFilter('shipping_date', $shippingData->getShippingDate())
            ->addFilter('customer_id', $customerId)
            ->addFilter('status', ['canceled', 'closed'], 'nin')
            ->addFilter('contribution_amount', 0, 'gt')
            ->create();

        return $this->orderRepository->getList($criteria)->getTotalCount() === 0;
    }
}

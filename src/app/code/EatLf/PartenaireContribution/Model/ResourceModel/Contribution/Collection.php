<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\ResourceModel\Contribution;

use EatLf\PartenaireContribution\Model\Contribution;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'contribution_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            Contribution::class,
            \EatLf\PartenaireContribution\Model\ResourceModel\Contribution::class
        );
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\ResourceModel;

use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use EatLf\PartenaireContribution\Api\Data\ContributionInterface;
use EatLf\PartenaireContribution\Api\Data\ContributionSearchResultsInterface;
use EatLf\PartenaireContribution\Api\Data\ContributionSearchResultsInterfaceFactory;
use EatLf\PartenaireContribution\Model\ContributionFactory;
use EatLf\PartenaireContribution\Model\ResourceModel\Contribution\CollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Reflection\DataObjectProcessor;

class ContributionRepository implements ContributionRepositoryInterface
{
    /**
     * @var \EatLf\PartenaireContribution\Model\ResourceModel\Contribution\CollectionFactory
     */
    private CollectionFactory $collectionFactory;
    /**
     * @var \EatLf\PartenaireContribution\Model\ContributionFactory
     */
    private ContributionFactory $contributionFactory;
    /**
     * @var \EatLf\PartenaireContribution\Api\Data\ContributionSearchResultsInterfaceFactory
     */
    private ContributionSearchResultsInterfaceFactory $searchResultsFactory;
    /**
     * @var \Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface
     */
    private CollectionProcessorInterface $collectionProcessor;
    /**
     * @var \EatLf\PartenaireContribution\Model\ResourceModel\Contribution
     */
    private Contribution $resource;
    /**
     * @var \Magento\Framework\Reflection\DataObjectProcessor
     */
    private DataObjectProcessor $dataObjectProcessor;

    /**
     * @param \EatLf\PartenaireContribution\Model\ResourceModel\Contribution\CollectionFactory $collectionFactory
     * @param \EatLf\PartenaireContribution\Model\ContributionFactory $contributionFactory
     * @param \EatLf\PartenaireContribution\Model\ResourceModel\Contribution $resource
     * @param \EatLf\PartenaireContribution\Api\Data\ContributionSearchResultsInterfaceFactory $searchResultsFactory
     * @param \Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface $collectionProcessor
     * @param \Magento\Framework\Reflection\DataObjectProcessor $dataObjectProcessor
     */
    public function __construct(
        CollectionFactory $collectionFactory,
        ContributionFactory $contributionFactory,
        Contribution $resource,
        ContributionSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor,
        DataObjectProcessor $dataObjectProcessor
    ) {
        $this->collectionFactory = $collectionFactory;
        $this->contributionFactory = $contributionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
        $this->resource = $resource;
        $this->dataObjectProcessor = $dataObjectProcessor;
    }

    /**
     * @inheritDoc
     */
    public function getList(SearchCriteriaInterface $criteria): ContributionSearchResultsInterface
    {
        $searchResults = $this->searchResultsFactory
            ->create()
            ->setSearchCriteria($criteria);

        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($criteria, $collection);

        $searchResults->setTotalCount($collection->getSize());
        $items = [];

        foreach ($collection->getItems() as $contribution) {
            $items[] = $contribution;
        }

        $searchResults->setItems($items);

        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function getById($id): ContributionInterface
    {
        $contribution = $this->contributionFactory->create();
        $this->resource->load($contribution, $id);

        if (!$contribution->getId()) {
            throw NoSuchEntityException::singleField('contribution_id', $id);
        }

        return $contribution;
    }

    /**
     * @inheritDoc
     */
    public function save(ContributionInterface $contribution): ContributionInterface
    {
        $data = $this->dataObjectProcessor->buildOutputDataArray(
            $contribution,
            ContributionInterface::class
        );

        if ($contribution->getContributionId()) {
            $contributionModel = $this->getById($contribution->getId());
        } else {
            $contributionModel = $this->contributionFactory->create();
        }

        $data[ContributionInterface::INVOICE_EMAILS] = implode(',', $contribution->getInvoiceEmails());
        $contributionModel->addData($data);

        $this->resource->save($contributionModel);

        return $contributionModel;
    }
}

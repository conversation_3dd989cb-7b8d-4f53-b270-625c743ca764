<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model;

use EatLf\PartenaireContribution\Api\Data\InvoiceEventInterface;
use Magento\Framework\MessageQueue\PublisherInterface;

class SendInvoiceEvent
{
    /**
     * @var \Magento\Framework\MessageQueue\PublisherInterface
     */
    private PublisherInterface $publisher;

    /**
     * @param \Magento\Framework\MessageQueue\PublisherInterface $publisher
     */
    public function __construct(PublisherInterface $publisher)
    {
        $this->publisher = $publisher;
    }

    /**
     * Dispatch the Contribution InvoiceNew event.
     *
     * @param \EatLf\PartenaireContribution\Model\Data\InvoiceEvent $event
     *
     * @return void
     */
    public function execute(InvoiceEventInterface $event): void
    {
        $this->publisher->publish('contribution.invoice', $event);
    }
}

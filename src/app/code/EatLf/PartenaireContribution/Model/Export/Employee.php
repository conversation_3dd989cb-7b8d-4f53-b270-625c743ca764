<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\Export;

use EatLf\PartenaireContribution\Api\EmployeeServiceInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\WriteInterface;

class Employee
{
    /**
     * @var \Magento\Framework\Filesystem\Directory\WriteInterface
     */
    private WriteInterface $targetDir;
    /**
     * @var \EatLf\PartenaireContribution\Api\EmployeeServiceInterface
     */
    private EmployeeServiceInterface $employeeService;

    /**
     * @param \EatLf\PartenaireContribution\Api\EmployeeServiceInterface $employeeService
     * @param \Magento\Framework\Filesystem $filesystem
     *
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function __construct(
        EmployeeServiceInterface $employeeService,
        Filesystem $filesystem
    ) {
        $this->employeeService = $employeeService;
        $this->targetDir = $filesystem->getDirectoryWrite(DirectoryList::TMP);
    }

    /**
     * Generate a csv file containing all the contribution members.
     *
     * @param string $contributionId
     *
     * @return string
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function export($contributionId): string
    {
        $filename = "employees_{$contributionId}.csv";

        $stream = $this->targetDir->openFile($filename, 'w+');

        $stream->lock();
        $stream->writeCsv(["email", "telephone"]);

        foreach ($this->employeeService->getEmployees($contributionId) as $data) {
            $row = [];
            $row[] = $data['email'];
            $row[] = $data['telephone'];
            $stream->writeCsv($row);
        }

        $stream->unlock();
        $stream->close();

        return $this->targetDir->getAbsolutePath($filename);
    }
}

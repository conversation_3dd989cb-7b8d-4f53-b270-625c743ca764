<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Model\Import;

use EatLf\PartenaireContribution\Api\CustomerServiceInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Driver\File;

class Employee
{
    private const BATCH_SIZE = 100;

    private const TARGET_TABLE = 'eatlf_partenaire_contribution_member';

    /**
     * @var \Magento\Framework\Filesystem
     */
    private Filesystem $filesystem;
    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private ResourceConnection $resourceConnection;
    /**
     * @var \Magento\Framework\Filesystem\Driver\File
     */
    private File $fileDriver;
    /**
     * @var \EatLf\PartenaireContribution\Api\CustomerServiceInterface
     */
    private CustomerServiceInterface $customerService;

    /**
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     * @param \Magento\Framework\Filesystem\Driver\File $fileDriver
     * @param \EatLf\PartenaireContribution\Api\CustomerServiceInterface $customerService
     */
    public function __construct(
        Filesystem $filesystem,
        ResourceConnection $resourceConnection,
        File $fileDriver,
        CustomerServiceInterface $customerService
    ) {
        $this->filesystem = $filesystem;
        $this->resourceConnection = $resourceConnection;
        $this->fileDriver = $fileDriver;
        $this->customerService = $customerService;
    }

    /**
     * Import an employee csv file into the database.
     *
     * @param string $contributionId
     * @param string $path
     * @param string $filename
     *
     * @return array
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function import($contributionId, $path, $filename): array
    {
        $this->resourceConnection->getConnection()->beginTransaction();

        $this->resetEmployees($contributionId);

        $importResult = $this->importCsv($contributionId, $path, $filename);

        $this->resourceConnection->getConnection()->commit();

        $this->cleanup($path, $filename);

        return $importResult;
    }

    /**
     * Cleanup existing employees for contribution.
     *
     * @param string $contributionId
     */
    public function resetEmployees(string $contributionId): void
    {
        $connection = $this->resourceConnection->getConnection();
        $connection->delete(
            'eatlf_partenaire_contribution_member',
            ['contribution_id = ?' => $contributionId]
        );
    }

    /**
     * Read csv file and batch insert data.
     *
     * @param string $contributionId
     * @param string $dirName
     * @param string $filename
     *
     * @return int[]
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function importCsv(string $contributionId, string $dirName, string $filename): array
    {
        $batch = [];
        $total = 0;
        $rowsImported = 0;

        $directory = $this->filesystem->getDirectoryReadByPath($dirName);
        $sourceFile = $directory->openFile($filename);

        // Skip header line
        $sourceFile->readCsv();

        while (($data = $sourceFile->readCsv()) !== false) {
            $total++;

            if (!$this->validateRow($data)) {
                continue;
            }

            $rowsImported++;

            $batch[$data[0]] = $this->formatRow($contributionId, $data);

            if (count($batch) === self::BATCH_SIZE) {
                $this->saveBatch(array_values($batch));
                $batch = [];
            }
        }

        // Persist remaining data if necessary
        if (count($batch) > 0) {
            $this->saveBatch($batch);
        }

        return ['total' => $total, 'imported' => $rowsImported];
    }

    /**
     * Insert a batch of data in the database.
     *
     * @param array $batch
     */
    public function saveBatch(array $batch): void
    {
        $this->resourceConnection
            ->getConnection()
            ->insertMultiple(
                self::TARGET_TABLE,
                $batch
            );
    }

    /**
     * Format row data for import.
     *
     * @param string $contributionId
     * @param array $data
     *
     * @return array
     */
    public function formatRow($contributionId, $data): array
    {
        return [
            'contribution_id' => $contributionId,
            'email' => $data[0],
            'telephone' => $this->customerService->formatTelephone($data[1]),
        ];
    }

    /**
     * Validate a single row of data from the csv file.
     *
     * @param mixed $data
     *
     * @return bool
     */
    public function validateRow($data): bool
    {
        return is_array($data) && count($data) >= 2;
    }

    /**
     * Cleanup after import.
     *
     * @param string $path
     * @param string $filename
     *
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function cleanup(string $path, string $filename): void
    {
        $this->fileDriver->deleteFile($path . $filename);
    }
}

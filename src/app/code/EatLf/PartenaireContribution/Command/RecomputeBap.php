<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Command;

use EatLf\Bap\Api\Data\BapStatusInterface;
use EatLf\Bap\Model\Entity\Bap;
use EatLf\Bap\Model\Entity\Query\GetBapById;
use EatLf\Bap\Model\Entity\ResourceModel\BapCustomer;
use EatLf\Bap\Model\Entity\ResourceModel\BapDay;
use EatLf\Bap\Model\Entity\ResourceModel\BapInvoice;
use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use EatLf\PartenaireContribution\Model\InvoiceEventService;
use EatLf\PartenaireContribution\Model\SendInvoiceEvent;
use InvalidArgumentException;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Sales\Api\Data\OrderSearchResultInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order\Invoice;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class RecomputeBap extends Command
{
    private const BAP_ID = 'bap-id';

    private GetBapById $getBapById;

    private OrderRepositoryInterface $orderRepository;

    private SearchCriteriaBuilder $criteriaBuilder;

    private ContributionRepositoryInterface $contributionRepository;

    private PartenaireRepositoryInterface $partenaireRepository;

    private InvoiceEventService $invoiceEventService;

    private SendInvoiceEvent $sendInvoiceEvent;

    private \EatLf\Bap\Model\Entity\ResourceModel\Bap $bapResource;

    private State $state;

    public function __construct(
        State $state,
        \EatLf\Bap\Model\Entity\ResourceModel\Bap $bapResource,
        GetBapById $getBapById,
        OrderRepositoryInterface $orderRepository,
        SearchCriteriaBuilder $criteriaBuilder,
        ContributionRepositoryInterface $contributionRepository,
        PartenaireRepositoryInterface $partenaireRepository,
        InvoiceEventService $invoiceEventService,
        SendInvoiceEvent $sendInvoiceEvent,
        string $name = null
    ) {
        parent::__construct($name);

        $this->getBapById = $getBapById;
        $this->orderRepository = $orderRepository;
        $this->criteriaBuilder = $criteriaBuilder;
        $this->contributionRepository = $contributionRepository;
        $this->partenaireRepository = $partenaireRepository;
        $this->invoiceEventService = $invoiceEventService;
        $this->sendInvoiceEvent = $sendInvoiceEvent;
        $this->bapResource = $bapResource;
        $this->state = $state;
    }

    protected function configure(): void
    {
        $options = [
            new InputOption(
                self::BAP_ID,
                null,
                InputOption::VALUE_REQUIRED,
                'BAP id'
            ),
        ];
        $this->setName('eatlf:bap:recompute');
        $this->setDescription('Recompute a BAP by id');
        $this->setDefinition($options);

        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->state->setAreaCode(Area::AREA_FRONTEND);

        $bapId = $input->getOption(self::BAP_ID);

        $bap = $this->getBapById->execute($bapId);

        $this->clearBap($bap);

        $ordersList = $this->getOrdersList($bap);

        foreach ($ordersList->getItems() as $order) {
            /** @var \Magento\Sales\Model\Order\Invoice $invoice */
            foreach ($order->getInvoiceCollection()->getItems() as $invoice) {
                $this->sendInvoiceEvent($invoice);
            }
        }
    }

    private function clearBap(Bap $bap)
    {
        if ($bap->getStatus() !== BapStatusInterface::STATUS_CREATED) {
            throw new InvalidArgumentException('Bap must be in created status');
        }

        $connection = $this->bapResource->getConnection();

        $connection->delete(BapInvoice::TABLE_NAME, 'bap_id = ' . $bap->getBapId());
        $connection->delete(BapDay::TABLE_NAME, 'bap_id = ' . $bap->getBapId());
        $connection->delete(BapCustomer::TABLE_NAME, 'bap_id = ' . $bap->getBapId());

        $bap->setTaxBase([]);
        $bap->setTaxDetails([]);
        $bap->setTotalExclTax(0);
        $bap->setTotalInclTax(0);
        $bap->setOrdersTotalInclTax(0);

        $this->bapResource->save($bap);
    }

    private function getOrdersList(Bap $bap): OrderSearchResultInterface
    {
        $bapMonth = str_pad((string)$bap->getMonth(), 2, '0', STR_PAD_LEFT);

        $contribution = $this->contributionRepository->getById($bap->getContributionId());

        $partenaire = $this->partenaireRepository->getById($contribution->getPartenaireId());

        $criteria = $this->criteriaBuilder
            ->addFilter('franchise_id', $partenaire->getFranchiseId())
            ->addFilter('contribution_id', $contribution->getContributionId())
            ->addFilter('shipping_date', $bap->getYear() . '-' . $bapMonth . '%', 'like')
            ->addFilter('state', 'closed', 'neq')
            ->addFilter('state', 'canceled', 'neq')
            ->create();

        return $this->orderRepository->getList($criteria);
    }

    private function sendInvoiceEvent(Invoice $invoice): void
    {
        if ($invoice->getData('is_contribution') == 1) {
            // Create event object
            $event = $this->invoiceEventService->createAddInvoiceEvent($invoice);

            // Send event
            $this->sendInvoiceEvent->execute($event);
        }
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Observer\Sales;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Payment\Model\Method\Free;
use Magento\Sales\Model\Order;

class FlagContributionInvoice implements ObserverInterface
{
    /**
     * An invoice created for the Free payment with an order grand total > 0 must be flagged as a contribution invoice.
     *
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(Observer $observer)
    {
        /** @var Order $order */
        $order = $observer->getData('order');

        if ($order->getGrandTotal() > 0 && $order->getPayment()->getMethod() === Free::PAYMENT_METHOD_FREE_CODE) {
            $order->setTotalPaid(0);
            $order->setBaseTotalPaid(0);

            foreach ($order->getInvoiceCollection()->getItems() as $invoice) {
                $invoice->setData('is_contribution', 1);
            }
        }
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Observer\Sales;

use EatLf\PartenaireContribution\Model\InvoiceEventService;
use EatLf\PartenaireContribution\Model\SendInvoiceEvent;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class CancelInvoice implements ObserverInterface
{
    private SendInvoiceEvent $sendInvoiceEvent;

    private InvoiceEventService $invoiceEventService;

    public function __construct(
        SendInvoiceEvent $sendInvoiceEvent,
        InvoiceEventService $invoiceEventService
    ) {
        $this->sendInvoiceEvent = $sendInvoiceEvent;
        $this->invoiceEventService = $invoiceEventService;
    }

    /**
     * Dispatch invoice cancel event for contribution invoices.
     *
     * @param \Magento\Framework\Event\Observer $observer
     *
     * @return void
     */
    public function execute(Observer $observer)
    {
        /** @var \Magento\Sales\Model\Order $order */
        $order = $observer->getData('order');

        /** @var \Magento\Sales\Model\Order\Invoice $invoice */
        foreach ($order->getInvoiceCollection() as $invoice) {
            if ($invoice->getData('is_contribution') == 1) {
                $cancelEvent = $this->invoiceEventService->createCancelInvoiceEvent($invoice->getIncrementId());

                $this->sendInvoiceEvent->execute($cancelEvent);

                return;
            }
        }
    }
}

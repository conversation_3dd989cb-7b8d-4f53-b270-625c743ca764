<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Observer\Sales;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Pricing\Helper\Data as PricingHelper;
use Magento\Sales\Model\Order;

class AddMailVariables implements ObserverInterface
{
    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    private PricingHelper $helper;

    /**
     * @param \Magento\Framework\Pricing\Helper\Data $helper
     */
    public function __construct(
        PricingHelper $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * Add contribution amount to the mail template variables if applicable.
     *
     * @param \Magento\Framework\Event\Observer $observer
     *
     * @return void
     */
    public function execute(Observer $observer)
    {
        /** @var \Magento\Framework\App\Action\Action $controller */
        $transport = $observer->getData('transport');

        /** @var Order $order */
        $order = $transport->getOrder();

        if ($order->getData('contribution_amount') > 0) {
            $formattedAmount = $this->helper->currency(
                $order->getData('contribution_amount') * -1,
                true,
                false
            );

            $transport['contribution_amount'] = $formattedAmount;
        }
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Observer\Sales;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Payment\Model\Method\Free;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Service\InvoiceService;
use Psr\Log\LoggerInterface;

class CreateContributionInvoice implements ObserverInterface
{
    /**
     * @var \Magento\Sales\Model\Service\InvoiceService
     */
    private InvoiceService $invoiceService;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    private LoggerInterface $logger;

    public function __construct(
        InvoiceService $invoiceService,
        LoggerInterface $logger
    ) {
        $this->invoiceService = $invoiceService;
        $this->logger = $logger;
    }

    /**
     * Create the contribution invoice for the order if applicable.
     *
     * @param \Magento\Framework\Event\Observer $observer
     *
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(Observer $observer)
    {
        /** @var Order $order */
        $order = $observer->getData('order');
        $contribution = $order->getData('contribution_amount');

        $this->logger->info('[Contribution] Handling order ' . $order->getIncrementId());

        // No contribution, nothing to do.
        if (!$contribution || $contribution <= 0) {
            $this->logger->info('[Contribution] No contribution for order ' . $order->getIncrementId());
            return;
        }

        $grandTotal = $order->getGrandTotal();

        // Set transaction_pending to true so that the register() method does not call pay() on the invoice.
        $transactionPending = $order->getPayment()->getIsTransactionPending();
        $order->getPayment()->setIsTransactionPending(true);

        // In this case we only need one invoice with the full quantities, we let the standard create it.
        if ($grandTotal > 0 && $order->getPayment()->getMethod() === Free::PAYMENT_METHOD_FREE_CODE) {
            $this->logger->info('[Contribution] Invoice with free method for order ' . $order->getIncrementId());
            return;
        }

        $this->logger->info('[Contribution] Creating contribution invoice for order ' . $order->getIncrementId());

        $contributionQtys = [];

        foreach ($order->getAllItems() as $item) {
            // Force allow decimal qty for order items.
            $item->setIsQtyDecimal(true);
            // Temporarily use product id as item id to be able to transfer qtys to invoice
            $item->setId($item->getProductId());

            $contributionQtys[$item->getId()] = max(
                round($item->getQtyOrdered() * $contribution / $grandTotal, 8),
                0
            );
        }

        // Create & register the contribution invoice.
        $order->setData('contribution_invoice', true);

        $contributionInvoice = $this->invoiceService->prepareInvoice($order, $contributionQtys);
        $contributionInvoice->setData('is_contribution', 1);
        $contributionInvoice->register();

        $order->setData('contribution_invoice');

        foreach ($order->getAllItems() as $item) {
            // Reset item ids
            $item->setId(null);
        }

        // Revert the original value and schedule the new invoice for saving with the order.
        $order->getPayment()->setIsTransactionPending($transactionPending);
        $order->addRelatedObject($contributionInvoice);
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Controller\Adminhtml\Contribution;

use EatLf\Partenaire\Model\Partenaire\Access;
use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Result\PageFactory;

class Edit extends Action
{
    public const ADMIN_RESOURCE = Access::ACCESS_EDIT;

    /**
     * @var PageFactory
     */
    private PageFactory $pageFactory;
    /**
     * @var \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface
     */
    private ContributionRepositoryInterface $contributionRepository;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $pageFactory
     * @param \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface $contributionRepository
     */
    public function __construct(
        Context $context,
        PageFactory $pageFactory,
        ContributionRepositoryInterface $contributionRepository
    ) {
        parent::__construct($context);

        $this->pageFactory = $pageFactory;
        $this->contributionRepository = $contributionRepository;
    }

    /**
     * Partenaire contribution edit form.
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->pageFactory->create();

        $resultPage->setActiveMenu('EatLf_PartenaireContribution::contributions');
        $resultPage->getConfig()->getTitle()->prepend((__('Company contribution')));

        $id = $this->getRequest()->getParam('id');

        try {
            $this->contributionRepository->getById($id);
        } catch (NoSuchEntityException $e) {
            $this->messageManager->addExceptionMessage($e, __('This contribution does not exist.'));
            return $this->resultRedirectFactory->create()
                ->setPath('*/*/listing');
        }

        return $resultPage;
    }
}

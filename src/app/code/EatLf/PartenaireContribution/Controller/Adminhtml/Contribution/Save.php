<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Controller\Adminhtml\Contribution;

use EatLf\Partenaire\Model\Partenaire\Access;
use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use EatLf\PartenaireContribution\Api\Data\ContributionInterfaceFactory;
use EatLf\PartenaireContribution\Model\Import\Employee;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\AuthorizationInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Message\ManagerInterface;

class Save implements HttpPostActionInterface
{
    /**
     * @var \Magento\Framework\AuthorizationInterface
     */
    private AuthorizationInterface $authorization;
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    private RequestInterface $request;
    /**
     * @var \Magento\Framework\Message\ManagerInterface
     */
    private ManagerInterface $messageManager;
    /**
     * @var \Magento\Framework\Controller\Result\RedirectFactory
     */
    private RedirectFactory $redirectFactory;
    /**
     * @var \EatLf\PartenaireContribution\Api\Data\ContributionInterfaceFactory
     */
    private ContributionInterfaceFactory $contributionFactory;
    /**
     * @var \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface
     */
    private ContributionRepositoryInterface $contributionRepository;
    /**
     * @var \EatLf\PartenaireContribution\Model\Import\Employee
     */
    private Employee $employeeImport;

    /**
     * @param \EatLf\PartenaireContribution\Api\Data\ContributionInterfaceFactory $contributionFactory
     * @param \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface $contributionRepository
     * @param \Magento\Framework\AuthorizationInterface $authorization
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     * @param \Magento\Framework\Controller\Result\RedirectFactory $redirectFactory
     * @param \EatLf\PartenaireContribution\Model\Import\Employee $employeeImport
     */
    public function __construct(
        ContributionInterfaceFactory $contributionFactory,
        ContributionRepositoryInterface $contributionRepository,
        AuthorizationInterface $authorization,
        RequestInterface $request,
        ManagerInterface $messageManager,
        RedirectFactory $redirectFactory,
        Employee $employeeImport
    ) {
        $this->authorization = $authorization;
        $this->request = $request;
        $this->messageManager = $messageManager;
        $this->redirectFactory = $redirectFactory;
        $this->contributionFactory = $contributionFactory;
        $this->contributionRepository = $contributionRepository;
        $this->employeeImport = $employeeImport;
    }

    /**
     * Partenaire contribution create/edit action.
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $result = $this->redirectFactory->create()->setPath('*/*/listing');
        $postData = $this->request->getParams();

        if (empty($postData['contribution_id'])) {
            $postData['contribution_id'] = null;
        }

        if (!$this->isAllowed($postData['contribution_id'])) {
            $this->messageManager->addErrorMessage(__('User is not allowed to modify contribution'));
            return $result;
        }

        $contribution = $this->contributionFactory
            ->create()
            ->addData($postData);

        try {
            $contribution = $this->contributionRepository->save($contribution);
            $this->messageManager->addSuccessMessage(__('The contribution has been saved'));
        } catch (LocalizedException $e) {
            $this->messageManager->addExceptionMessage($e, __('Could not save contribution'));
            return $result;
        }

        if (isset($postData['employees']) && count($postData['employees']) === 1) {
            try {
                $importedRows = $this->employeeImport->import(
                    $contribution->getContributionId(),
                    $postData['employees'][0]['path'],
                    $postData['employees'][0]['file']
                );

                $this->messageManager->addSuccessMessage(
                    __('%1 lines were imported successfully', $importedRows['imported'])
                );

                if ($importedRows['total'] - $importedRows['imported'] > 1) {
                    $this->messageManager->addErrorMessage(
                        __('%1 lines were rejected', $importedRows['total'] - $importedRows['imported'])
                    );
                }

                if ($importedRows['total'] - $importedRows['imported'] === 1) {
                    $this->messageManager->addErrorMessage(
                        __('1 line was rejected')
                    );
                }
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage($e, __('An error occurred while importing employees'));
            }
        }

        return $result;
    }

    /**
     * Return true if user is allowed to edit/create a contribution
     *
     * @param string? $contributionId
     *
     * @return bool
     */
    public function isAllowed($contributionId): bool
    {
        if ($contributionId) {
            return $this->authorization->isAllowed(Access::ACCESS_EDIT);
        }

        return $this->authorization->isAllowed(Access::ACCESS_CREATE);
    }
}

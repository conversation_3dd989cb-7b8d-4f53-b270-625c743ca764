<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Controller\Adminhtml\Contribution;

use EatLf\Partenaire\Model\Partenaire\Access;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Model\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;

class Create extends Action
{
    public const ADMIN_RESOURCE = Access::ACCESS_CREATE;

    /**
     * @var PageFactory
     */
    private PageFactory $pageFactory;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $pageFactory
     */
    public function __construct(
        Context $context,
        PageFactory $pageFactory
    ) {
        parent::__construct($context);

        $this->pageFactory = $pageFactory;
    }

    /**
     * @inheritDoc
     */
    public function execute(): Page
    {
        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->pageFactory->create();

        $resultPage->setActiveMenu('EatLf_PartenaireContribution::contributions')
            ->getConfig()
            ->getTitle()
            ->prepend((__('Add a contribution')));

        return $resultPage;
    }
}

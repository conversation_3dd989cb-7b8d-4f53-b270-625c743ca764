<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Controller\Adminhtml\Employee;

use EatLf\Partenaire\Model\Partenaire\Access;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\AuthorizationInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\File\UploaderFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\WriteInterface;

class Upload implements HttpPostActionInterface
{
    /**
     * @var \Magento\Framework\File\UploaderFactory
     */
    private UploaderFactory $uploaderFactory;
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    private RequestInterface $request;
    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    private JsonFactory $jsonFactory;
    /**
     * @var \Magento\Framework\AuthorizationInterface
     */
    private AuthorizationInterface $authorization;
    /**
     * @var \Magento\Framework\Filesystem\Directory\WriteInterface
     */
    private WriteInterface $targetDir;

    /**
     * @param \Magento\Framework\File\UploaderFactory $uploaderFactory
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magento\Framework\Controller\Result\JsonFactory $jsonFactory
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\Framework\AuthorizationInterface $authorization
     *
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function __construct(
        UploaderFactory $uploaderFactory,
        RequestInterface $request,
        JsonFactory $jsonFactory,
        Filesystem $filesystem,
        AuthorizationInterface $authorization
    ) {
        $this->uploaderFactory = $uploaderFactory;
        $this->request = $request;
        $this->jsonFactory = $jsonFactory;
        $this->authorization = $authorization;

        $this->targetDir = $filesystem->getDirectoryWrite(DirectoryList::TMP);
    }

    /**
     * Save employee csv file to temporary folder.
     *
     * @return \Magento\Framework\Controller\Result\Json
     * @throws \Exception
     */
    public function execute()
    {
        $response = $this->jsonFactory->create([]);

        if (!$this->authorization->isAllowed(Access::ACCESS_EDIT)
            || !$this->authorization->isAllowed(Access::ACCESS_CREATE)) {
            return $response->setStatusHeader(503);
        }

        $fileId = $this->request->getParam('param_name');

        $result = $this->uploaderFactory
            ->create(['fileId' => $fileId])
            ->setAllowedExtensions(['csv'])
            ->save($this->targetDir->getAbsolutePath());

        return $response->setData($result);
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Controller\Adminhtml\Employee;

use EatLf\Partenaire\Model\Partenaire\Access;
use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use EatLf\PartenaireContribution\Model\Export\Employee;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\AuthorizationInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Message\ManagerInterface;

class Export implements HttpGetActionInterface
{
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    private RequestInterface $request;
    /**
     * @var \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface
     */
    private ContributionRepositoryInterface $contributionRepository;
    /**
     * @var \Magento\Framework\AuthorizationInterface
     */
    private AuthorizationInterface $authorization;
    /**
     * @var \Magento\Framework\Message\ManagerInterface
     */
    private ManagerInterface $messageManager;
    /**
     * @var \Magento\Framework\Controller\Result\RedirectFactory
     */
    private RedirectFactory $redirectFactory;
    /**
     * @var \Magento\Framework\App\Response\Http\FileFactory
     */
    private FileFactory $fileFactory;
    /**
     * @var \EatLf\PartenaireContribution\Model\Export\Employee
     */
    private Employee $employeeExport;

    /**
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface $contributionRepository
     * @param \Magento\Framework\AuthorizationInterface $authorization
     * @param \Magento\Framework\App\Response\Http\FileFactory $fileFactory
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     * @param \Magento\Framework\Controller\Result\RedirectFactory $redirectFactory
     * @param \EatLf\PartenaireContribution\Model\Export\Employee $employeeExport
     */
    public function __construct(
        RequestInterface $request,
        ContributionRepositoryInterface $contributionRepository,
        AuthorizationInterface $authorization,
        FileFactory $fileFactory,
        ManagerInterface $messageManager,
        RedirectFactory $redirectFactory,
        Employee $employeeExport
    ) {
        $this->request = $request;
        $this->contributionRepository = $contributionRepository;
        $this->authorization = $authorization;
        $this->messageManager = $messageManager;
        $this->redirectFactory = $redirectFactory;
        $this->fileFactory = $fileFactory;
        $this->employeeExport = $employeeExport;
    }

    /**
     * Export the list of employees in a csv file.
     *
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Redirect|\Magento\Framework\Controller\ResultInterface
     * @throws \Exception
     */
    public function execute()
    {
        if (!$this->authorization->isAllowed(Access::ACCESS_EDIT)) {
            $this->messageManager->addErrorMessage(__('User is not allowed to access contribution'));

            return $this->redirectFactory
                ->create()
                ->setPath('*/contribution/listing');
        }

        $contributionId = $this->request->getParam('contribution_id');

        try {
            $this->contributionRepository->getById($contributionId);
        } catch (NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__('Requested contribution does not exist'));

            return $this->redirectFactory
                ->create()
                ->setPath('*/contribution/listing');
        }

        $filename = $this->employeeExport->export($contributionId);

        return $this->fileFactory->create(
            "contribution_{$contributionId}.csv",
            ['type' => 'filename', 'rm' => 1, 'value' => $filename]
        );
    }
}

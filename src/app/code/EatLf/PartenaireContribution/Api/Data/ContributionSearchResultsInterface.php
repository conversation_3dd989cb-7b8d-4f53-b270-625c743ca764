<?php

namespace EatLf\PartenaireContribution\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

interface ContributionSearchResultsInterface extends SearchResultsInterface
{
    /**
     * Get customers list.
     *
     * @return \EatLf\PartenaireContribution\Api\Data\ContributionInterface[]
     */
    public function getItems();

    /**
     * Set customers list.
     *
     * @param \EatLf\PartenaireContribution\Api\Data\ContributionInterface[] $items
     *
     * @return $this
     */
    public function setItems(array $items);
}

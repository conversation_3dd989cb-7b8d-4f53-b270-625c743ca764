<?php

namespace EatLf\PartenaireContribution\Api\Data;

interface InvoiceEventInterface
{
    public const TYPE_ADD = "ADD";

    public const TYPE_CANCEL = "CANCEL";

    /**
     * Return the invoice increment id.
     *
     * @return string
     */
    public function getInvoiceIncrementId(): string;

    /**
     * Return the event type.
     *
     * Can be one of :
     *
     * - ADD
     * - CANCEL
     *
     * @return string
     */
    public function getEventType(): string;

    /**
     * Return invoice event data for ADD events.
     *
     * @return \EatLf\PartenaireContribution\Api\Data\InvoiceEventDataInterface|null
     */
    public function getInvoiceEventData(): ?InvoiceEventDataInterface;

    /**
     * Set invoice increment id.
     *
     * @param string $invoiceIncrementId
     *
     * @return void
     */
    public function setInvoiceIncrementId(string $invoiceIncrementId): void;

    /**
     * Set invoice event data
     *
     * @param \EatLf\PartenaireContribution\Api\Data\InvoiceEventDataInterface $invoiceEventData
     *
     * @return void
     */
    public function setInvoiceEventData(InvoiceEventDataInterface $invoiceEventData): void;

    /**
     * Set invoice type
     *
     * @param string $eventType
     *
     * @return void
     */
    public function setEventType(string $eventType): void;
}

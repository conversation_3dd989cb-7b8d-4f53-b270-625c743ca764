<?php

namespace EatLf\PartenaireContribution\Api\Data;

interface TaxRateInterface
{
    /**
     * Return tax rate.
     *
     * @return string
     */
    public function getRate(): string;

    /**
     * Set tax rate.
     *
     * @param string $rate
     *
     * @return void
     */
    public function setRate(string $rate): void;

    /**
     * Set tax base.
     *
     * @param float $base
     *
     * @return void
     */
    public function setBase(float $base): void;

    /**
     * Return tax base.
     *
     * @return float
     */
    public function getBase(): float;

    /**
     * Return tax amount
     *
     * @return float
     */
    public function getAmount(): float;

    /**
     * Set Tax amount
     *
     * @param float $amount
     *
     * @return void
     */
    public function setAmount(float $amount): void;
}

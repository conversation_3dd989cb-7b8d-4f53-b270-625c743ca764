<?php

namespace EatLf\PartenaireContribution\Api\Data;

interface InvoiceEventDataInterface
{
    /**
     * Return the contribution id.
     *
     * @return int
     */
    public function getContributionId(): int;

    /**
     * Return the franchise id.
     *
     * @return string
     */
    public function getFranchiseId(): string;

    /**
     * Return the shipping date.
     *
     * @return string
     */
    public function getShippingDate(): string;

    /**
     * Return order total including tax.
     *
     * @return float
     */
    public function getOrderTotalInclTax(): float;

    /**
     * Return the total including tax.
     *
     * @return float
     */
    public function getTotalInclTax(): float;

    /**
     * Return the total excluding tax.
     *
     * @return float
     */
    public function getTotalExclTax(): float;

    /**
     * Return the tax details.
     *
     * @return \EatLf\PartenaireContribution\Api\Data\TaxRateInterface[]
     */
    public function getTaxDetails(): array;

    /**
     * Return the customer email.
     *
     * @return string
     */
    public function getCustomerEmail(): string;

    /**
     * Set contribution id.
     *
     * @param int $contributionId
     *
     * @return void
     */
    public function setContributionId(int $contributionId): void;

    /**
     * Set franchise id.
     *
     * @param string $franchiseId
     *
     * @return void
     */
    public function setFranchiseId(string $franchiseId): void;

    /**
     * Set shipping date.
     *
     * @param string $shippingDate
     *
     * @return void
     */
    public function setShippingDate(string $shippingDate): void;

    /**
     * Set order total including tax.
     *
     * @param float $orderTotalIncTax
     *
     * @return void
     */
    public function setOrderTotalInclTax(float $orderTotalIncTax): void;

    /**
     * Set total including tax.
     *
     * @param float $totalInclTax
     *
     * @return void
     */
    public function setTotalInclTax(float $totalInclTax): void;

    /**
     * Set total excluding tax.
     *
     * @param float $totalExclTax
     *
     * @return void
     */
    public function setTotalExclTax(float $totalExclTax): void;

    /**
     * Set tax details.
     *
     * @param \EatLf\PartenaireContribution\Api\Data\TaxRateInterface[] $taxDetails
     *
     * @return void
     */
    public function setTaxDetails(array $taxDetails): void;

    /**
     * Set customer email.
     *
     * @param string $customerEmail
     *
     * @return void
     */
    public function setCustomerEmail(string $customerEmail): void;
}

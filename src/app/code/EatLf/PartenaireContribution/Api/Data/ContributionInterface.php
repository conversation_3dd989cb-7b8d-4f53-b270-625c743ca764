<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Api\Data;

interface ContributionInterface
{
    public const CONTRIBUTION_ID = 'contribution_id';
    public const PARTENAIRE_ID = 'partenaire_id';
    public const TYPE = 'type';
    public const ENABLED = 'enabled';
    public const AMOUNT = 'amount';
    public const MIN_PAID_AMOUNT = 'min_paid_amount';
    public const MIN_ORDER_AMOUNT = 'min_order_amount';
    public const INVOICE_FIRSTNAME = 'invoice_firstname';
    public const INVOICE_LASTNAME = 'invoice_lastname';
    public const INVOICE_ADDRESS = 'invoice_address';
    public const INVOICE_PHONE_NUMBER = 'invoice_phone_number';
    public const INVOICE_EMAILS = 'invoice_emails';

    public const TYPE_MONETARY = 1;
    public const TYPE_PERCENT = 2;

    /**
     * Get contribution id
     *
     * @return mixed
     */
    public function getContributionId();

    /**
     * Get partenaire id
     *
     * @return mixed
     */
    public function getPartenaireId();

    /**
     * Get contribution status
     *
     * @return bool
     */
    public function isEnabled(): bool;

    /**
     * Get contribution type
     *
     * @return int
     */
    public function getType(): int;

    /**
     * Get contribution amount
     *
     * @return string
     */
    public function getAmount(): string;

    /**
     * Get minimum amount left to pay for the customer
     *
     * @return string
     */
    public function getMinPaidAmount(): string;

    /**
     * Get minimum order total to be eligible to this contribution
     *
     * @return string
     */
    public function getMinOrderAmount(): string;

    /**
     * Get invoice firstname
     *
     * @return string
     */
    public function getInvoiceFirstname(): string;

    /**
     * Get invoice lastname
     *
     * @return string
     */
    public function getInvoiceLastname(): string;

    /**
     * Get invoice address
     *
     * @return string
     */
    public function getInvoiceAddress(): string;

    /**
     * Get invoice phone number
     *
     * @return string
     */
    public function getInvoicePhoneNumber(): string;

    /**
     * Get invoice emails
     *
     * @return array
     */
    public function getInvoiceEmails(): array;
}

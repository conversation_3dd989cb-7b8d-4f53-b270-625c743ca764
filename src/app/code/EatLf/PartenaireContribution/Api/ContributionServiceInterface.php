<?php

namespace EatLf\PartenaireContribution\Api;

use Magento\Quote\Api\Data\CartInterface;

interface ContributionServiceInterface
{
    /**
     * Set the contribution amount applicable for the specified cart.
     *
     * @param \Magento\Quote\Api\Data\CartInterface $cart
     * @param float $grandTotal
     *
     * @return void
     */
    public function applyContributionToCart(CartInterface $cart, float $grandTotal): void;
}

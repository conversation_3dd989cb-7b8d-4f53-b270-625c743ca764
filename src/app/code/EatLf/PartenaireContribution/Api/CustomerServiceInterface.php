<?php

namespace EatLf\PartenaireContribution\Api;

interface CustomerServiceInterface
{

    /**
     * Return the list of active contributions for which the customer is a registered employee.
     *
     * @param string $customerId
     *
     * @return \EatLf\PartenaireContribution\Api\Data\ContributionInterface[]
     */
    public function findCustomerContributions($customerId): array;

    /**
     * Format customer account phone number.
     *
     * Return the national part of the phone number (without the international prefix and leading 0) stored in the
     * customer account.
     *
     * @param string $customerId
     *
     * @return string
     */
    public function formatCustomerTelephone($customerId): string;

    /**
     * Remote the international prefix and leading 0 from the specified phone number.
     *
     * @param string $value
     *
     * @return string
     */
    public function formatTelephone($value): string;
}

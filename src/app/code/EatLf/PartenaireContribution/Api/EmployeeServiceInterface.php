<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Api;

interface EmployeeServiceInterface
{
    /**
     * Return the number of employees who are members of the specified contribution.
     *
     * @param string $contributionId
     *
     * @return int
     */
    public function countEmployees($contributionId): int;

    /**
     * Return a list of email / telephone of all the employees who are members of the specified contribution.
     *
     * @param string $contributionId
     *
     * @return array
     */
    public function getEmployees($contributionId): array;
}

<?php

namespace EatLf\PartenaireContribution\Api;

use EatLf\PartenaireContribution\Api\Data\ContributionInterface;
use EatLf\PartenaireContribution\Api\Data\ContributionSearchResultsInterface;
use Magento\Framework\Api\SearchCriteriaInterface;

interface ContributionRepositoryInterface
{
    /**
     * Retrieve contributions which match a specified criteria.
     *
     * @param \Magento\Framework\Api\SearchCriteriaInterface $criteria
     *
     * @return \EatLf\PartenaireContribution\Api\Data\ContributionSearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $criteria): ContributionSearchResultsInterface;

    /**
     * Load contribution by id
     *
     * @param string $id
     *
     * @return \EatLf\PartenaireContribution\Api\Data\ContributionInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException If contribution with specified ID does not exist
     */
    public function getById($id): ContributionInterface;

    /**
     * Persist contribution data
     *
     * @param \EatLf\PartenaireContribution\Api\Data\ContributionInterface $contribution
     *
     * @return \EatLf\PartenaireContribution\Api\Data\ContributionInterface
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function save(ContributionInterface $contribution): ContributionInterface;
}

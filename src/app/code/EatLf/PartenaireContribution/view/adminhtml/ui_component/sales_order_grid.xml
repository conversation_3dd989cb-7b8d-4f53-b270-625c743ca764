<?xml version="1.0" encoding="UTF-8"?>

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <columns name="sales_order_columns">
        <column name="invoice_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="componentDisabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </column>
        <column name="payment_date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="componentDisabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </column>
        <column name="contribution_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">N° contribution</item>
                    <item name="sortOrder" xsi:type="number">107</item>
                    <item name="filter" xsi:type="string">text</item>
                </item>
            </argument>
        </column>
    </columns>
</listing>

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
         class="EatLf\Partenaire\Rewrite\Partenairelisting">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">contributions_listing.contributions_listing_data_source</item>
            <item name="deps" xsi:type="string">contributions_listing.contributions_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">spinner_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
                <item name="name" xsi:type="string">add</item>
                <item name="label" xsi:type="string" translate="true">Add a contribution</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/create</item>
            </item>
        </item>
    </argument>
    <dataSource name="contributions_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <updateUrl path="mui/index/render"/>
            <storageConfig>
                <param name="indexField" xsi:type="string">contribution_id</param>
            </storageConfig>
        </settings>
        <aclResource>EatLf_Partenaire::listing</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="contributions_listing_data_source">
            <settings>
                <requestFieldName>contribution_id</requestFieldName>
                <primaryFieldName>contribution_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="sticky" xsi:type="boolean">true</item>
            </item>
        </argument>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <filterSelect name="partenaire_id" provider="${ $.parentName }">
                <settings>
                    <options class="EatLf\Partenaire\Model\Partenaire\Source\Options"/>
                    <label translate="true">Partenaire</label>
                    <caption translate="true">All partners</caption>
                    <dataScope>partenaire_id</dataScope>
                </settings>
            </filterSelect>
        </filters>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="spinner_columns">
        <column name="contribution_id" sortOrder="5">
            <settings>
                <label translate="true">Contribution number</label>
            </settings>
        </column>
        <column name="company" sortOrder="10">
            <settings>
                <label translate="true">Company</label>
            </settings>
        </column>
        <column name="enabled"
                class="EatLf\PartenaireContribution\Ui\Component\Contribution\Listing\Columns\Status"
                sortOrder="20">
            <settings>
                <label translate="true">Status</label>
                <filter>select</filter>
                <dataType>select</dataType>
                <options>
                    <option name="0" xsi:type="array">
                        <item name="value" xsi:type="number">1</item>
                        <item name="label" xsi:type="string" translate="true">Enabled</item>
                    </option>
                    <option name="1" xsi:type="array">
                        <item name="value" xsi:type="number">2</item>
                        <item name="label" xsi:type="string" translate="true">Disabled</item>
                    </option>
                </options>
            </settings>
        </column>
        <column name="amount"
                class="EatLf\PartenaireContribution\Ui\Component\Contribution\Listing\Columns\Amount"
                sortOrder="30">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Contribution amount</label>
            </settings>
        </column>
        <column name="min_paid_amount"
                class="EatLf\PartenaireContribution\Ui\Component\Contribution\Listing\Columns\Currency"
                sortOrder="40">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Minimum left to pay</label>
            </settings>
        </column>
        <column name="min_order_amount"
                class="EatLf\PartenaireContribution\Ui\Component\Contribution\Listing\Columns\Currency"
                sortOrder="50">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Minimum order amount</label>
            </settings>
        </column>
        <actionsColumn name="actions"
                       class="EatLf\PartenaireContribution\Ui\Component\Contribution\Listing\Columns\Actions"
                       sortOrder="100">
            <settings>
                <indexField>contribution_id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>

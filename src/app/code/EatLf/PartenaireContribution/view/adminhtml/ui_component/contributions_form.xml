<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">contributions_form.contributions_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">General Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="save" class="EatLf\PartenaireContribution\Ui\Component\Contribution\Form\Buttons\Save"/>
            <button name="back" class="EatLf\PartenaireContribution\Ui\Component\Contribution\Form\Buttons\Back"/>
        </buttons>
        <namespace>contributions_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>contributions_form.contributions_data_source</dep>
        </deps>
    </settings>
    <dataSource name="contributions_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="*/*/save"/>
        </settings>
        <dataProvider class="EatLf\PartenaireContribution\Ui\Component\Contribution\Form\DataProvider"
                      name="contributions_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>contribution_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <fieldset name="general">
        <settings>
            <label translate="true">Informations générales</label>
        </settings>
        <field name="contribution_id" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>false</visible>
            </settings>
        </field>
        <field name="partenaire_id" formElement="select">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Partner</label>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="EatLf\Partenaire\Model\Partenaire\Source\Options"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="enabled" formElement="select">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Status</label>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options>
                            <option name="0" xsi:type="array">
                                <item name="value" xsi:type="number">1</item>
                                <item name="label" xsi:type="string" translate="true">Enabled</item>
                            </option>
                            <option name="1" xsi:type="array">
                                <item name="value" xsi:type="number">0</item>
                                <item name="label" xsi:type="string" translate="true">Disabled</item>
                            </option>
                        </options>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="type" formElement="select">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Type</label>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options>
                            <option name="0" xsi:type="array">
                                <item name="value" xsi:type="number">1</item>
                                <item name="label" xsi:type="string">€</item>
                            </option>
                            <option name="1" xsi:type="array">
                                <item name="value" xsi:type="number">2</item>
                                <item name="label" xsi:type="string">%</item>
                            </option>
                        </options>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="amount" formElement="input">
            <settings>
                <dataType>number</dataType>
                <label translate="true">Amount</label>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-not-negative-number" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="min_paid_amount" formElement="input">
            <settings>
                <dataType>number</dataType>
                <label translate="true">Minimum left to pay</label>
                <notice translate="true">
                    Reste à charge minimum à payer par le salarié.
                </notice>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-not-negative-number" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="min_order_amount" formElement="input">
            <settings>
                <dataType>number</dataType>
                <label translate="true">Minimum order amount</label>
                <notice translate="true">
                    Montant minimum de commande pour bénéficier de la participation.
                </notice>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-not-negative-number" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="invoice_firstname" formElement="input">
            <settings>
                <dataType>number</dataType>
                <label translate="true">Invoice firstname</label>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="invoice_lastname" formElement="input">
            <settings>
                <dataType>number</dataType>
                <label translate="true">Invoice lastname</label>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="invoice_address" formElement="input">
            <settings>
                <dataType>number</dataType>
                <label translate="true">Invoice address</label>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="invoice_phone_number" formElement="input">
            <settings>
                <dataType>number</dataType>
                <label translate="true">Invoice phone number</label>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="invoice_emails" formElement="textarea">
            <settings>
                <dataType>number</dataType>
                <label translate="true">Invoice emails</label>
                <notice translate="true">
                    Liste des adresses emails, séparées par une virgule, qui recevront le bon à
                    payer envoyé au partenaire pour les commandes effectuées.
                </notice>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
    </fieldset>
    <fieldset name="members">
        <settings>
            <label translate="true">Employee management</label>
        </settings>
        <field name="employees_summary"
               component="EatLf_PartenaireContribution/js/contribution/form/employees"
               formElement="input">
            <settings>
                <elementTmpl>EatLf_PartenaireContribution/contribution/form/employees</elementTmpl>
                <label translate="true">Number of employees</label>
            </settings>
        </field>
        <field name="employees"
               component="EatLf_PartenaireContribution/js/contribution/form/list-uploader"
               formElement="fileUploader">
            <settings>
                <label translate="true">Update employee list</label>
                <notice translate="true">
                    Tous les employés existants sur cette participation seront remplacés par ceux présents dans le
                    fichier importé.
                </notice>
            </settings>
            <formElements>
                <fileUploader>
                    <settings>
                        <allowedExtensions>csv</allowedExtensions>
                        <maxFileSize>15728640</maxFileSize>
                        <uploaderConfig>
                            <param xsi:type="string" name="url">contribution/employee/upload</param>
                        </uploaderConfig>
                    </settings>
                </fileUploader>
            </formElements>
        </field>
    </fieldset>
</form>

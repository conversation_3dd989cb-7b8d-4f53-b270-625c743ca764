<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <referenceBlock name="checkout.cart.totals">
        <arguments>
            <argument name="jsLayout" xsi:type="array">
                <item name="components" xsi:type="array">
                    <item name="block-totals" xsi:type="array">
                        <item name="children" xsi:type="array">
                            <item name="partenaire-contribution" xsi:type="array">
                                <item name="component" xsi:type="string">EatLf_PartenaireContribution/js/view/summary/contribution-total</item>
                                <item name="config" xsi:type="array">
                                    <item name="contributionTitle" xsi:type="string" translate="true">Company contribution</item>
                                    <item name="totalTitle" xsi:type="string" translate="true">Amount to pay</item>
                                </item>
                            </item>
                        </item>
                    </item>
                </item>
            </argument>
        </arguments>
    </referenceBlock>
</page>
define([
    'Magento_Checkout/js/view/summary/abstract-total',
    'Magento_Checkout/js/model/quote'
], function (Component, quote) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'EatLf_PartenaireContribution/cart/contribution-total'
        },

        /**
         * @return {*}
         */
        isDisplayed: function () {
            var totals = quote.getTotals()();

            return totals && totals['contribution_amount'] > 0;
        },

        /**
         * Get raw contribution value.
         */
        getRawContribution: function () {
            var totals = quote.getTotals()();

            return totals['contribution_amount'] * -1;
        },

        /**
         * Get raw grand total value.
         */
        getRawGrandTotal: function () {
            var totals = quote.getTotals()();

            return totals['base_grand_total'];
        },

        /**
         * @return {*|String}
         */
        getContributionValue: function () {
            return this.getFormattedPrice(this.getRawContribution());
        },

        /**
         * @return {*|String}
         */
        getTotalValue: function () {
            return this.getFormattedPrice(this.getRawGrandTotal() + this.getRawContribution());
        }
    });
});

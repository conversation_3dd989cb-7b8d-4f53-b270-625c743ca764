define([
    'Magento_Checkout/js/model/totals'
], function (totals) {
    'use strict';

    return function (Wallet) {
        return Wallet.extend({
            getGrandTotal: function () {
                var price = 0;
                if (this.totals()) {
                    price = totals.getSegment('grand_total').value;

                    if (this.totals().extension_attributes && this.totals().extension_attributes.contribution_amount > 0) {
                        price -= this.totals().extension_attributes.contribution_amount;
                    }
                }

                return price;
            },
        });
    }
})

<?php
/**
 * @var $escaper   \Magento\Framework\Escaper
 * @var $block     \Magento\Framework\View\Element\Template
 * @var $viewModel \EatLf\PartenaireContribution\Block\Customer\Dashboard\Contribution
 */

use Magento\Cms\Block\BlockByIdentifier;

$viewModel = $block->getData('view_model');
$contributions = $viewModel->getContributions();
?>

<div class="box box-contribution">
    <div class="box-header">
        <strong><?= $escaper->escapeHtml(__('Company contribution')) ?></strong>
    </div>

    <?php if (count($contributions) > 0): ?>
        <h3 class="box-title">
            <span><?= $escaper->escapeHtml(__('Company contribution details')) ?></span>
        </h3>
        <div class="box-content">
            <?php if (count($contributions) > 1): ?>
                <div class="contribution-desc">
                    <?= $escaper->escapeHtml(__('You can benefit from multiple contributions')) ?></div>
            <?php else: ?>
                <div class="contribution-desc">
                    <?= $escaper->escapeHtml(__('Every day your company contributes to your lunch expenses')) ?>
                </div>
            <?php endif ?>

            <?php foreach ($contributions as $key => $contribution): ?>
                <?php if (count($contributions) > 1): ?>
                    <p class="contribution-head">
                        <?= $escaper->escapeHtml(
                            __('Company contribution details %1', $contribution->getData('partenaire_name'))
                        ) ?>
                    </p>
                <?php endif ?>

                <p class="contribution-item">
                    - <?= $escaper->escapeHtml(
                        __(
                            'You can benefit from a %1 per day',
                            $viewModel->formatAmount($contribution)
                        )
                    ) ?>
                </p>

                <?php if ($contribution->getMinOrderAmount() > 0): ?>
                    <p class="contribution-item">
                        - <?= $escaper->escapeHtml(
                            __(
                                'The minimum amount for your order is %1.',
                                $viewModel->formatPrice($contribution->getMinOrderAmount())
                            )
                        ) ?>
                    </p>
                <?php endif ?>

                <?php if ($contribution->getMinPaidAmount() > 0): ?>
                    <p class="contribution-item">
                        - <?= $escaper->escapeHtml(
                            __(
                                'The minimum amount to pay for your order is %1.',
                                $viewModel->formatPrice($contribution->getMinPaidAmount())
                            )
                        ) ?>
                    </p>
                <?php endif ?>
            <?php endforeach ?>
        </div>
    <?php else: ?>
        <?= $block->getLayout()
            ->createBlock(BlockByIdentifier::class)
            ->setData('identifier', $viewModel->getCmsBlockId())
            ->toHtml();
        ?>
    <?php endif ?>
</div>

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Block\Customer\Dashboard;

use EatLf\PartenaireContribution\Api\CustomerServiceInterface;
use EatLf\PartenaireContribution\Api\Data\ContributionInterface;
use <PERSON>gento\Customer\Model\Session;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class Contribution implements ArgumentInterface
{
    /**
     * @var \Magento\Customer\Model\Session
     */
    private Session $session;
    /**
     * @var \EatLf\PartenaireContribution\Api\CustomerServiceInterface
     */
    private CustomerServiceInterface $customerService;

    /**
     * @param \EatLf\PartenaireContribution\Api\CustomerServiceInterface $customerService
     * @param \Magento\Customer\Model\Session $session
     */
    public function __construct(
        CustomerServiceInterface $customerService,
        Session $session
    ) {
        $this->session = $session;
        $this->customerService = $customerService;
    }

    /**
     * Return the CMS block id to display when the customer is not eligible to company contribution.
     *
     * @return string
     */
    public function getCmsBlockId(): string
    {
        return 'abondement-dashboard-default';
    }

    /**
     * Return true if the customer belongs to at least one active contribution.
     *
     * @return \EatLf\PartenaireContribution\Api\Data\ContributionInterface[]
     */
    public function getContributions(): array
    {
        $customerId = $this->session->getCustomerId();

        return array_values($this->customerService->findCustomerContributions($customerId));
    }

    /**
     * Format the specified price for display.
     *
     * @param string $value
     *
     * @return string
     */
    public function formatPrice($value): string
    {
        return str_replace('.', ',', $value) . '€';
    }

    /**
     * Format the contribution amount based on its type.
     *
     * @param \EatLf\PartenaireContribution\Api\Data\ContributionInterface $contribution
     *
     * @return string
     */
    public function formatAmount(ContributionInterface $contribution): string
    {
        if ($contribution->getType() === ContributionInterface::TYPE_PERCENT) {
            return str_replace('.', ',', $contribution->getAmount()) . '%';
        }

        return $this->formatPrice($contribution->getAmount());
    }
}

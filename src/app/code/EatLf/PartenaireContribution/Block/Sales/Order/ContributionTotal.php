<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Block\Sales\Order;

use Magento\Framework\DataObject;
use Magento\Sales\Block\Order\Totals;

class ContributionTotal extends Totals
{
    /**
     * Add partenaire contribution total if applicable.
     *
     * @return $this
     */
    public function initTotals()
    {
        $parent = $this->getParentBlock();
        $order = $parent->getOrder();

        $contributionPayment = new DataObject(
            [
                'code' => 'partenaire_contribution',
                'strong' => true,
                'value' => $order->getData('contribution_amount') * -1,
                'label' => __('Company contribution'),
            ]
        );

        $totalPaid = new DataObject(
            [
                'code' => 'total_paid',
                'strong' => true,
                'value' => $order->getTotalPaid(),
                'label' => __('Total paid'),
            ]
        );

        if ($order->getData('contribution_amount') > 0) {
            $parent->addTotal($contributionPayment, 'last');
            $parent->addTotal($totalPaid, 'last');
        }

        return $this;
    }
}

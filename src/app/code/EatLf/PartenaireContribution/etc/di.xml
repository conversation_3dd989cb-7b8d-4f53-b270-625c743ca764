<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="EatLf\PartenaireContribution\Api\ContributionServiceInterface"
                type="EatLf\PartenaireContribution\Model\ContributionService"/>

    <preference for="EatLf\PartenaireContribution\Api\CustomerServiceInterface"
                type="EatLf\PartenaireContribution\Model\CustomerService"/>

    <preference for="Magento\Quote\Api\Data\CartInterface"
                type="EatLf\PartenaireContribution\Rewrite\Model\Quote"/>

    <preference for="EatLf\PartenaireContribution\Api\ContributionRepositoryInterface"
                type="EatLf\PartenaireContribution\Model\ResourceModel\ContributionRepository"/>

    <preference for="EatLf\PartenaireContribution\Api\Data\ContributionSearchResultsInterface"
                type="EatLf\PartenaireContribution\Model\ContributionSearchResults"/>

    <preference for="EatLf\PartenaireContribution\Api\Data\ContributionInterface"
                type="EatLf\PartenaireContribution\Model\Contribution"/>

    <preference for="EatLf\PartenaireContribution\Api\EmployeeServiceInterface"
                type="EatLf\PartenaireContribution\Model\EmployeeService"/>

    <preference for="EatLf\PartenaireContribution\Api\Data\InvoiceEventInterface"
                type="EatLf\PartenaireContribution\Model\Data\InvoiceEvent"/>

    <preference for="EatLf\PartenaireContribution\Api\Data\InvoiceEventDataInterface"
                type="EatLf\PartenaireContribution\Model\Data\InvoiceEventData"/>

    <preference for="EatLf\PartenaireContribution\Api\Data\TaxRateInterface"
                type="EatLf\PartenaireContribution\Model\Data\TaxRate"/>

    <virtualType name="EatLf\PartenaireContribution\Model\Api\SearchCriteria\ContributionCollectionProcessor"
                 type="Magento\Framework\Api\SearchCriteria\CollectionProcessor">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="joins" xsi:type="object">
                    EatLf\PartenaireContribution\Model\Api\SearchCriteria\CollectionProcessor\EmployeeJoinProcessor
                </item>
                <item name="filters" xsi:type="object">
                    EatLf\PartenaireContribution\Model\Api\SearchCriteria\CollectionProcessor\EmployeeFilterProcessor
                </item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType
            name="EatLf\PartenaireContribution\Model\Api\SearchCriteria\CollectionProcessor\EmployeeFilterProcessor"
            type="Magento\Framework\Api\SearchCriteria\CollectionProcessor\FilterProcessor">
        <arguments>
            <argument name="fieldMapping" xsi:type="array">
                <item name="email" xsi:type="string">epcm.email</item>
                <item name="telephone" xsi:type="string">epcm.telephone</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="EatLf\PartenaireContribution\Model\Api\SearchCriteria\CollectionProcessor\EmployeeJoinProcessor"
                 type="Magento\Framework\Api\SearchCriteria\CollectionProcessor\JoinProcessor">
        <arguments>
            <argument name="customJoins" xsi:type="array">
                <item name="emp.email" xsi:type="object">
                    EatLf\PartenaireContribution\Model\Api\SearchCriteria\JoinProcessor\Employee
                </item>
                <item name="emp.telephone" xsi:type="object">
                    EatLf\PartenaireContribution\Model\Api\SearchCriteria\JoinProcessor\Employee
                </item>
                <item name="franchise.id" xsi:type="object">
                    EatLf\PartenaireContribution\Model\Api\SearchCriteria\JoinProcessor\Franchise
                </item>
            </argument>
            <argument name="fieldMapping" xsi:type="array">
                <item name="email" xsi:type="string">emp.email</item>
                <item name="telephone" xsi:type="string">emp.telephone</item>
                <item name="franchise_id" xsi:type="string">franchise.id</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="Magento\Sales\Model\ResourceModel\Order\Invoice\Grid">
        <arguments>
            <argument name="columns" xsi:type="array">
                <item name="is_contribution" xsi:type="string">sales_invoice.is_contribution</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="Magento\Sales\Model\ResourceModel\Order\Grid">
        <arguments>
            <argument name="columns" xsi:type="array">
                <item name="contribution_id" xsi:type="string">sales_order.contribution_id</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Magento\Quote\Model\Quote\TotalsCollector">
        <plugin name="eatlf_calculate_contribution"
                type="EatLf\PartenaireContribution\Plugin\Quote\CalculateContribution"/>
    </type>

    <type name="Magento\Quote\Api\CartTotalRepositoryInterface">
        <plugin name="eatlf_contribution_amount"
                type="EatLf\PartenaireContribution\Plugin\Quote\TotalsRepoAddContribution"/>
    </type>

    <type name="Magento\Quote\Api\PaymentMethodManagementInterface">
        <plugin name="eatlf_deduct_contribution"
                type="EatLf\PartenaireContribution\Plugin\Sales\PaymentMethodContribution"/>
    </type>

    <type name="Magento\Sales\Model\Order\Validation\RefundInvoiceInterface">
        <plugin name="eatlf_contribution_refund" type="EatLf\PartenaireContribution\Plugin\Invoice\AllowInvoiceRefund"/>
    </type>

    <type name="Lf\Sales\Model\Order\Pdf\Invoice">
        <plugin name="eatlf_contribution_billing"
                type="EatLf\PartenaireContribution\Plugin\Invoice\InvoicePdfBillingAddresss"/>
    </type>

    <type name="Lf\Sales\Model\RefundOrder">
        <plugin name="eatlf_contribution_offline"
                type="EatLf\PartenaireContribution\Plugin\Creditmemo\AlwaysRefundOffline"/>
    </type>

    <type name="Lf\Sales\Model\Creditmemo\Refund">
        <plugin name="eatlf_contribution_reset_reglement"
                type="EatLf\PartenaireContribution\Plugin\Creditmemo\ResetCreditMemoReglement"/>
    </type>

    <type name="Magento\Sales\Api\OrderManagementInterface">
        <plugin name="eatlf_contribution_invoice_event"
                type="EatLf\PartenaireContribution\Plugin\Sales\AfterPlaceNewInvoiceEvent"/>
    </type>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="contributions_listing_data_source" xsi:type="string">
                    EatLf\PartenaireContribution\Model\ResourceModel\Contribution\Grid\Collection
                </item>
            </argument>
        </arguments>
    </type>

    <type name="EatLf\PartenaireContribution\Model\ResourceModel\ContributionRepository">
        <arguments>
            <argument name="collectionProcessor" xsi:type="object">
                EatLf\PartenaireContribution\Model\Api\SearchCriteria\ContributionCollectionProcessor
            </argument>
        </arguments>
    </type>

    <type name="Lf\Compta\Model\Feed\Payment">
        <plugin name="eatlf_contribution_compta_filter_invoice"
                type="EatLf\PartenaireContribution\Plugin\Compta\FilterContributionInvoice"/>
        <plugin name="eatlf_contribution_compta_payment"
                type="EatLf\PartenaireContribution\Plugin\Compta\PaymentFeedPartners"/>
    </type>

    <type name="Lf\Compta\Model\Feed\Invoice">
        <plugin name="eatlf_contribution_compta_invoice"
                type="EatLf\PartenaireContribution\Plugin\Compta\InvoiceFeedPartners"/>
    </type>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="eatlf_recompute_bap" xsi:type="object">
                    EatLf\PartenaireContribution\Command\RecomputeBap
                </item>
            </argument>
        </arguments>
    </type>
</config>

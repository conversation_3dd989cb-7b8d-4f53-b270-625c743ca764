<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="eatlf_partenaire_contribution" resource="default" engine="innodb"
           comment="Partenaire contribution definition Table">
        <column xsi:type="int" name="contribution_id" unsigned="true" nullable="false" identity="true"
                comment="Contribution ID"/>
        <column xsi:type="int" name="partenaire_id" unsigned="true" nullable="false" identity="false"
                comment="Partenaire ID"/>
        <column xsi:type="smallint" name="type" unsigned="true" nullable="false" identity="false" default="0"
                comment="Contribution type"/>
        <column xsi:type="boolean" name="enabled" default="0" comment="Contribution status"/>
        <column xsi:type="decimal" name="amount" unsigned="true" scale="2" nullable="false" default="0"
                comment="Contribution amount"/>
        <column xsi:type="decimal" name="min_paid_amount" unsigned="true" scale="2" nullable="false" default="0"
                comment="Minimum amount left to pay for the employee"/>
        <column xsi:type="decimal" name="min_order_amount" unsigned="true" scale="2" nullable="false" default="0"
                comment="Minimum cart grand total to be eligible to this contribution"/>
        <column xsi:type="varchar" name="invoice_firstname" nullable="false" comment="Invoice firstname"/>
        <column xsi:type="varchar" name="invoice_lastname" nullable="false" comment="Invoice lastname"/>
        <column xsi:type="varchar" name="invoice_address" nullable="false" comment="Invoice post address"/>
        <column xsi:type="varchar" name="invoice_phone_number" nullable="false" comment="Invoice phone number"/>
        <column xsi:type="varchar" name="invoice_emails" nullable="false" comment="Invoice recipient emails"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="contribution_id"/>
        </constraint>

        <constraint xsi:type="foreign" referenceId="EATLF_CONTRIBUTION_PARTENAIRE_ID"
                    table="eatlf_partenaire_contribution" column="partenaire_id" referenceTable="partenaire"
                    referenceColumn="id" onDelete="CASCADE"/>

        <index referenceId="EATLF_CONTRIBUTION_PARTENAIRE_ID" indexType="btree">
            <column name="partenaire_id"/>
        </index>
    </table>

    <table name="eatlf_partenaire_contribution_member" resource="default" engine="innodb"
           comment="Partenaire contribution members Table">
        <column xsi:type="int" name="member_id" unsigned="true" nullable="false" identity="true"
                comment="Member ID"/>
        <column xsi:type="int" name="contribution_id" unsigned="true" nullable="false" identity="false"
                comment="Contribution ID"/>
        <column xsi:type="varchar" name="email" nullable="false" comment="Member email"/>
        <column xsi:type="varchar" name="telephone" nullable="false" comment="Member telephone number"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="member_id"/>
        </constraint>

        <constraint xsi:type="foreign" referenceId="EATLF_CONTRIBUTION_PARTENAIRE_MEMBER_CONT_ID"
                    table="eatlf_partenaire_contribution_member" column="contribution_id"
                    referenceTable="eatlf_partenaire_contribution"
                    referenceColumn="contribution_id" onDelete="CASCADE"/>

        <constraint xsi:type="unique" referenceId="EATLF_CONTRIBUTION_EMAIL">
            <column name="contribution_id"/>
            <column name="email"/>
        </constraint>
    </table>

    <table name="quote">
        <column xsi:type="decimal" name="contribution_amount" scale="4" precision="20" unsigned="false" nullable="true"
                default="0" comment="Partenaire contribution amount"/>

        <column xsi:type="int" name="contribution_id" unsigned="true" nullable="true" identity="false"
                comment="Contribution ID"/>
    </table>

    <table name="sales_order">
        <column xsi:type="decimal" name="contribution_amount" scale="4" precision="20" unsigned="false" nullable="true"
                default="0" comment="Partenaire contribution amount"/>

        <column xsi:type="int" name="contribution_id" unsigned="true" nullable="true" identity="false"
                comment="Contribution ID"/>
    </table>

    <table name="sales_invoice">
        <column xsi:type="int" name="is_contribution" unsigned="true" nullable="false" identity="false"
                default="0" comment="Is it a contribution invoice?"/>
    </table>

    <table name="sales_invoice_grid">
        <column xsi:type="int" name="is_contribution" unsigned="true" nullable="false" identity="false"
                default="0" comment="Is it a contribution invoice?"/>
    </table>

    <table name="sales_order_grid">
        <column xsi:type="int" name="contribution_id" unsigned="true" nullable="true" identity="false"
                comment="Contribution ID"/>
    </table>
</schema>

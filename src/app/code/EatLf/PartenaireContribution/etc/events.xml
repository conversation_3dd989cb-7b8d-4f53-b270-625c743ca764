<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">

    <event name="sales_order_place_before">
        <observer name="eatlf_contribution_invoice"
                  instance="EatLf\PartenaireContribution\Observer\Sales\CreateContributionInvoice"/>
    </event>

    <event name="sales_order_place_after">
        <observer name="eatlf_contribution_invoice_flag_free"
                  instance="EatLf\PartenaireContribution\Observer\Sales\FlagContributionInvoice"/>
    </event>

    <event name="email_order_set_template_vars_before">
        <observer name="eatlf_contribution_order_variables" instance="EatLf\Referral\Observer\Sales\AddMailVariables"/>
    </event>

    <event name="lf_order_cancel_later">
        <observer name="eatlf_contribution_order_cancel"
                  instance="EatLf\PartenaireContribution\Observer\Sales\CancelInvoice"/>
    </event>
</config>

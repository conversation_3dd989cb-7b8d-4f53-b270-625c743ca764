<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Rewrite\Model;

class Quote extends \Magento\Quote\Model\Quote
{
    /**
     * Deduct contribution from grand total if necessary.
     *
     * @return float
     */
    public function getGrandTotal()
    {
        if ($this->getData('include_contribution')) {
            return parent::getGrandTotal() - $this->getData('contribution_amount');
        }

        return parent::getGrandTotal();
    }

    /**
     * Deduct contribution from base grand total if necessary.
     *
     * @return float
     */
    public function getBaseGrandTotal()
    {
        if ($this->getData('include_contribution')) {
            return parent::getBaseGrandTotal() - $this->getData('contribution_amount');
        }

        return parent::getBaseGrandTotal();
    }
}

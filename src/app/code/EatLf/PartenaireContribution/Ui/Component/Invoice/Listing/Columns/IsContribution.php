<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Ui\Component\Invoice\Listing\Columns;

use Magento\Ui\Component\Listing\Columns\Column;

class IsContribution extends Column
{
    /**
     * @inheritdoc
     */
    public function prepareDataSource(array $dataSource)
    {
        foreach ($dataSource['data']['items'] as &$item) {
            if ($item['is_contribution'] == 1) {
                $item['is_contribution'] = __('Yes');
            } else {
                $item['is_contribution'] = __('No');
            }
        }

        return $dataSource;
    }
}

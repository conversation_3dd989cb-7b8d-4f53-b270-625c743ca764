<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Ui\Component\Contribution\Listing\Columns;

use Magento\Ui\Component\Listing\Columns\Column;

class Status extends Column
{
    /**
     * @inheritdoc
     */
    public function prepareDataSource(array $dataSource)
    {
        foreach ($dataSource['data']['items'] as &$item) {
            if ($item['enabled'] == 1) {
                $item['enabled'] = __('Actif');
            } elseif ($item['enabled'] == 0) {
                $item['enabled'] = __('Inactif');
            }
        }

        return $dataSource;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Ui\Component\Contribution\Listing\Columns;

use Magento\Ui\Component\Listing\Columns\Column;

class Currency extends Column
{
    /**
     * @inheritdoc
     */
    public function prepareDataSource(array $dataSource)
    {
        foreach ($dataSource['data']['items'] as &$item) {
            $item[$this->getName()] = $item[$this->getName()] . ' €';
        }

        return $dataSource;
    }
}

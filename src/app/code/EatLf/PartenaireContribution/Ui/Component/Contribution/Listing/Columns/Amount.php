<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Ui\Component\Contribution\Listing\Columns;

use Magento\Ui\Component\Listing\Columns\Column;

class Amount extends Column
{
    /**
     * @inheritdoc
     */
    public function prepareDataSource(array $dataSource)
    {
        foreach ($dataSource['data']['items'] as &$item) {
            if ($item['type'] == 1) {
                $item['amount'] = $item['amount'] . ' €';
            } elseif ($item['type'] == 2) {
                $item['amount'] = $item['amount'] . ' %';
            }
        }

        return $dataSource;
    }
}

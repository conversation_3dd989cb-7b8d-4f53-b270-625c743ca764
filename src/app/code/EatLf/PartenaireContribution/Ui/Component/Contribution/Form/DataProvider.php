<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Ui\Component\Contribution\Form;

use EatLf\PartenaireContribution\Api\EmployeeServiceInterface;
use EatLf\PartenaireContribution\Model\ResourceModel\Contribution\CollectionFactory;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\UrlInterface;
use Magento\Ui\DataProvider\AbstractDataProvider;

class DataProvider extends AbstractDataProvider
{
    private const EXAMPLE_FILE_PATH = 'contribution/example.csv';

    /**
     * @var array|null
     */
    private ?array $loadedData = null;
    /**
     * @var \EatLf\PartenaireContribution\Api\EmployeeServiceInterface
     */
    private EmployeeServiceInterface $employeeService;
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    private RequestInterface $request;
    /**
     * @var \Magento\Framework\UrlInterface
     */
    private UrlInterface $url;

    /**
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \EatLf\PartenaireContribution\Api\EmployeeServiceInterface $employeeService
     * @param \EatLf\PartenaireContribution\Model\ResourceModel\Contribution\CollectionFactory $collectionFactory
     * @param \Magento\Framework\UrlInterface $url
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        RequestInterface $request,
        EmployeeServiceInterface $employeeService,
        CollectionFactory $collectionFactory,
        UrlInterface $url,
        $name,
        $primaryFieldName,
        $requestFieldName,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);

        $this->collection = $collectionFactory->create();
        $this->employeeService = $employeeService;
        $this->request = $request;
        $this->url = $url;
    }

    /**
     * @inheritDoc
     */
    public function getData()
    {
        if (!$this->loadedData === null) {
            return $this->loadedData;
        }

        $this->loadedData = [];

        if (!$this->getContributionId()) {
            return $this->loadedData;
        }

        $items = $this->collection->getItems();

        /** @var \EatLf\PartenaireContribution\Model\Contribution $contribution */
        foreach ($items as $contribution) {
            $this->loadedData[$contribution->getId()] = $contribution->getData();

            $this->loadedData[$contribution->getId()]['employees_summary'] =
                $this->employeeService->countEmployees($contribution->getId());
        }

        return $this->loadedData;
    }

    /**
     * @inheritDoc
     */
    public function getMeta()
    {
        $meta = parent::getMeta();

        if (!$this->getContributionId()) {
            $meta['members']['children']['employees_summary']['arguments']
            ['data']['config']['visible'] = false;
        }

        $meta['members']['children']['employees_summary']['arguments']
        ['data']['config']['employeesCount'] = $this->employeeService->countEmployees($this->getContributionId());

        $meta['members']['children']['employees_summary']['arguments']
        ['data']['config']['exportUrl'] = $this->url->getUrl(
            'contribution/employee/export',
            ['contribution_id' => $this->getContributionId()]
        );

        $meta['members']['children']['employees']['arguments']
        ['data']['config']['exampleFileUrl'] =
            $this->url->getBaseUrl(['_type' => UrlInterface::URL_TYPE_MEDIA]) . self::EXAMPLE_FILE_PATH;

        return $meta;
    }

    /**
     * Return current contribution id from request
     *
     * @return int
     */
    private function getContributionId(): int
    {
        return (int)$this->request->getParam($this->getRequestFieldName());
    }
}

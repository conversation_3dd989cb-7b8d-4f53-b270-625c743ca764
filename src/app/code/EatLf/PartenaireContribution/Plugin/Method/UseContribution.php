<?php

namespace EatLf\PartenaireContribution\Plugin\Method;

use Magento\Payment\Model\MethodInterface;
use Magento\Quote\Api\Data\CartInterface;

class UseContribution
{
    public function beforeIsAvailable(MethodInterface $subject, CartInterface $quote = null)
    {
        $quote->setData('include_contribution', true);

        return null;
    }

    public function afterIsAvailable(MethodInterface $subject, $result, CartInterface $quote = null)
    {
        $quote->setData('include_contribution', false);

        return $result;
    }
}

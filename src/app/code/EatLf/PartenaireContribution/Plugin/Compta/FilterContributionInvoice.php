<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Compta;

use Lf\Compta\Model\Feed\Payment;

class FilterContributionInvoice
{
    /**
     * Do not use contribution invoices for the regular payments export.
     *
     * @param \Lf\Compta\Model\Feed\Payment $payment
     * @param $result
     * @param \Magento\Sales\Model\Order $order
     *
     * @return \Magento\Framework\DataObject|mixed|void
     */
    public function afterGetInvoiceForOrderPayment(Payment $payment, $result, $order)
    {
        if ($order->getData('contribution_id') !== null) {
            foreach ($order->getInvoiceCollection()->getItems() as $invoice) {
                if ($invoice->getData('is_contribution') != 1) {
                    return $invoice;
                }
            }
        } else {
            return $result;
        }
    }
}

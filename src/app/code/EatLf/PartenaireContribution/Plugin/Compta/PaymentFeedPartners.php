<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Compta;

use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use Lf\Compta\Model\Feed\Payment;
use Magento\Sales\Model\Order\Creditmemo;
use Magento\Sales\Model\Order\Invoice;

class PaymentFeedPartners
{
    private const PARTNER_PREFIX = 'P';

    private ContributionRepositoryInterface $contributionRepository;

    private PartenaireRepositoryInterface $partenaireRepository;

    public function __construct(
        ContributionRepositoryInterface $contributionRepository,
        PartenaireRepositoryInterface $partenaireRepository
    ) {
        $this->contributionRepository = $contributionRepository;
        $this->partenaireRepository = $partenaireRepository;
    }

    public function afterGetCreditmemoAccountNumber(Payment $subject, $result, Creditmemo $creditmemo)
    {
        $invoice = $creditmemo->getInvoice();

        // Not a contribution invoice, return normal account number.
        if (!$invoice || $invoice->getData('is_contribution') != 1) {
            return $result;
        }

        $partenaire = $this->getPartenaireForInvoice($invoice);

        return self::PARTNER_PREFIX . $partenaire->getId();
    }

    public function afterGetCreditmemoLabel(Payment $subject, $result, Creditmemo $creditmemo)
    {
        $invoice = $creditmemo->getInvoice();

        // Not a contribution invoice, return normal account number.
        if (!$invoice || $invoice->getData('is_contribution') != 1) {
            return $result;
        }

        $partenaire = $this->getPartenaireForInvoice($invoice);

        return Payment::LIBELLE_AVOIR . $creditmemo->getIncrementId() . ' ' . $partenaire->getCompany();
    }

    private function getPartenaireForInvoice(Invoice $invoice)
    {
        $contributionId = $invoice->getOrder()->getData('contribution_id');

        $contribution = $this->contributionRepository->getById($contributionId);

        return $this->partenaireRepository->getById($contribution->getPartenaireId());
    }
}

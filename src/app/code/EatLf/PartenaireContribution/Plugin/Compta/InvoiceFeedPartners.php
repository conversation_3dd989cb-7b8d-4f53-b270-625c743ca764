<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Compta;

use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use Lf\Compta\Model\Feed\Invoice as InvoiceFeed;
use Magento\Sales\Model\Order\Creditmemo;
use Magento\Sales\Model\Order\Invoice;

class InvoiceFeedPartners
{
    private const PARTNER_PREFIX = 'P';

    private ContributionRepositoryInterface $contributionRepository;

    private PartenaireRepositoryInterface $partenaireRepository;

    public function __construct(
        ContributionRepositoryInterface $contributionRepository,
        PartenaireRepositoryInterface $partenaireRepository
    ) {
        $this->contributionRepository = $contributionRepository;
        $this->partenaireRepository = $partenaireRepository;
    }

    public function afterGetInvoiceAccountNumber(InvoiceFeed $subject, $result, Invoice $invoice)
    {
        // Not a contribution invoice, return normal account number.
        if ($invoice->getData('is_contribution') != 1) {
            return $result;
        }

        $partenaire = $this->getPartenaireForInvoice($invoice);

        return self::PARTNER_PREFIX . $partenaire->getId();
    }

    public function afterGetInvoiceLabel(InvoiceFeed $subject, $result, Invoice $invoice)
    {
        // Not a contribution invoice, return normal label.
        if ($invoice->getData('is_contribution') != 1) {
            return $result;
        }

        $partenaire = $this->getPartenaireForInvoice($invoice);

        return InvoiceFeed::LIBELLE_FACTURE . $invoice->getIncrementId() . ' ' . $partenaire->getCompany();
    }

    public function afterGetCreditmemoAccountNumber(InvoiceFeed $subject, $result, Creditmemo $creditmemo)
    {
        $invoice = $creditmemo->getInvoice();

        // Not a contribution invoice, return normal account number.
        if (!$invoice || $invoice->getData('is_contribution') != 1) {
            return $result;
        }

        $partenaire = $this->getPartenaireForInvoice($invoice);

        return self::PARTNER_PREFIX . $partenaire->getId();
    }

    public function afterGetCreditmemoLabel(InvoiceFeed $subject, $result, Creditmemo $creditmemo)
    {
        $invoice = $creditmemo->getInvoice();

        // Not a contribution invoice, return normal account number.
        if (!$invoice || $invoice->getData('is_contribution') != 1) {
            return $result;
        }

        $partenaire = $this->getPartenaireForInvoice($invoice);

        return InvoiceFeed::LIBELLE_AVOIR . $creditmemo->getIncrementId() . ' ' . $partenaire->getCompany();
    }

    private function getPartenaireForInvoice(Invoice $invoice)
    {
        $contributionId = $invoice->getOrder()->getData('contribution_id');

        $contribution = $this->contributionRepository->getById($contributionId);

        return $this->partenaireRepository->getById($contribution->getPartenaireId());
    }
}

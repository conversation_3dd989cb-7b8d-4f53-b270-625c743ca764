<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Quote;

use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\CartTotalRepositoryInterface;
use Magento\Quote\Api\Data\TotalsInterface;

class TotalsRepoAddContribution
{
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;

    /**
     * @param \Magento\Quote\Api\CartRepositoryInterface $cartRepository
     */
    public function __construct(CartRepositoryInterface $cartRepository)
    {
        $this->cartRepository = $cartRepository;
    }

    /**
     * Add the contribution amount to the cart totals.
     *
     * @param \Magento\Quote\Api\CartTotalRepositoryInterface $subject
     * @param \Magento\Quote\Api\Data\TotalsInterface $result
     * @param string $cartId
     *
     * @return \Magento\Quote\Api\Data\TotalsInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function afterGet(
        CartTotalRepositoryInterface $subject,
        TotalsInterface $result,
        $cartId
    ): TotalsInterface {
        $cart = $this->cartRepository->get($cartId);
        $contribution = $cart->getData('contribution_amount');

        $result
            ->getExtensionAttributes()
            ->setContributionAmount($contribution);

        return $result;
    }
}

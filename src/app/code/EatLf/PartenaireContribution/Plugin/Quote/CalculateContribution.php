<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Quote;

use EatLf\PartenaireContribution\Api\ContributionServiceInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Address\Total;
use Magento\Quote\Model\Quote\TotalsCollector;

class CalculateContribution
{
    /**
     * @var \EatLf\PartenaireContribution\Api\ContributionServiceInterface
     */
    private ContributionServiceInterface $contributionService;

    /**
     * @param \EatLf\PartenaireContribution\Api\ContributionServiceInterface $contributionService
     */
    public function __construct(
        ContributionServiceInterface $contributionService
    ) {
        $this->contributionService = $contributionService;
    }

    /**
     * Calculate the contribution amount applicable to the cart.
     *
     * @param \Magento\Quote\Model\Quote\TotalsCollector $subject
     * @param \Magento\Quote\Model\Quote\Address\Total $result
     * @param \Magento\Quote\Model\Quote $quote
     *
     * @return Total
     */
    public function afterCollect(TotalsCollector $subject, Total $result, Quote $quote)
    {
        $this->contributionService->applyContributionToCart(
            $quote,
            (float)$result->getData('grand_total')
        );

        return $result;
    }
}

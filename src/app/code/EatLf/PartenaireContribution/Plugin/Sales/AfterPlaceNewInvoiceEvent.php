<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Sales;

use EatLf\PartenaireContribution\Model\GetContributionInvoice;
use EatLf\PartenaireContribution\Model\InvoiceEventService;
use EatLf\PartenaireContribution\Model\SendInvoiceEvent;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderManagementInterface;

class AfterPlaceNewInvoiceEvent
{
    private InvoiceEventService $invoiceEventService;

    private GetContributionInvoice $getContributionInvoice;

    private SendInvoiceEvent $sendInvoiceEvent;

    public function __construct(
        InvoiceEventService $invoiceEventService,
        GetContributionInvoice $getContributionInvoice,
        SendInvoiceEvent $sendInvoiceEvent
    ) {
        $this->invoiceEventService = $invoiceEventService;
        $this->getContributionInvoice = $getContributionInvoice;
        $this->sendInvoiceEvent = $sendInvoiceEvent;
    }

    /**
     * Send a new invoice event if a contribution invoice exists on the new order.
     *
     * @param \Magento\Sales\Api\OrderManagementInterface $orderManagement
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     *
     * @return \Magento\Sales\Api\Data\OrderInterface
     */
    public function afterPlace(OrderManagementInterface $orderManagement, OrderInterface $order)
    {
        $invoice = $this->getContributionInvoice->execute($order);

        if (!$invoice) {
            return $order;
        }

        $event = $this->invoiceEventService->createAddInvoiceEvent($invoice);

        $this->sendInvoiceEvent->execute($event);

        return $order;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Sales;

use Magento\Sales\Block\Order\Totals;
use Magento\Sales\Model\Order;

class AdminCanceledOrderTotals
{
    private $called = false;

    public function afterGetTotals(Totals $totals, $result, $area = null)
    {
        $order = $totals->getOrder();

        if ($this->called
            || $order->getData('contribution_id') === null
            || !isset($result['refunded'])
        ) {
            return $result;
        }

        $contributionAmount = $order->getData('contribution_amount');

        if ($order->getTotalDue() > 0 && $order->getStatus() === Order::STATE_CANCELED) {
            $result['due']['label'] = __('Total annulé');

            $result['refunded']['value'] -= $contributionAmount;
            $result['refunded']['base_value'] -= $contributionAmount;
        }

        $this->called = true;

        return $result;
    }
}

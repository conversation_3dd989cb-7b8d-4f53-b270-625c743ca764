<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Sales;

use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\PaymentInterface;
use Magento\Quote\Api\PaymentMethodManagementInterface;

class PaymentMethodContribution
{
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;

    /**
     * @param \Magento\Quote\Api\CartRepositoryInterface $cartRepository
     */
    public function __construct(CartRepositoryInterface $cartRepository)
    {
        $this->cartRepository = $cartRepository;
    }

    /**
     * Activate contribution deduction on quote grand total.
     *
     * @param \Magento\Quote\Api\PaymentMethodManagementInterface $subject
     * @param string $cartId
     * @param \Magento\Quote\Api\Data\PaymentInterface $method
     *
     * @return null
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function beforeSet(PaymentMethodManagementInterface $subject, $cartId, PaymentInterface $method)
    {
        $cart = $this->cartRepository->get($cartId);
        $cart->setData('include_contribution', true);

        return null;
    }

    /**
     * Activate contribution deduction on quote grand total.
     *
     * @param \Magento\Quote\Api\PaymentMethodManagementInterface $subject
     * @param string $cartId
     *
     * @return null
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function beforeGetList(PaymentMethodManagementInterface $subject, $cartId)
    {
        $cart = $this->cartRepository->get($cartId);
        $cart->setData('include_contribution', true);

        return null;
    }
}

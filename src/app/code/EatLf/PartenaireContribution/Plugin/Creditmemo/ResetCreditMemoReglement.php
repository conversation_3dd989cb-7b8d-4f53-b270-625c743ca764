<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Creditmemo;

use Lf\Sales\Model\Creditmemo\Refund;
use Magento\Sales\Api\CreditmemoRepositoryInterface;
use Magento\Sales\Api\InvoiceRepositoryInterface;
use Magento\Sales\Model\Order\Invoice;

class ResetCreditMemoReglement
{
    /**
     * @var \Magento\Sales\Api\CreditmemoRepositoryInterface
     */
    private CreditmemoRepositoryInterface $creditmemoRepository;
    /**
     * @var \Magento\Sales\Api\InvoiceRepositoryInterface
     */
    private InvoiceRepositoryInterface $invoiceRepository;

    /**
     * @param \Magento\Sales\Api\CreditmemoRepositoryInterface $creditmemoRepository
     * @param \Magento\Sales\Api\InvoiceRepositoryInterface $invoiceRepository
     */
    public function __construct(
        CreditmemoRepositoryInterface $creditmemoRepository,
        InvoiceRepositoryInterface $invoiceRepository
    ) {
        $this->creditmemoRepository = $creditmemoRepository;
        $this->invoiceRepository = $invoiceRepository;
    }

    /**
     * For creditmemo on paid invoice: use same payment date and use invoice payment method.
     * For creditmemo on unpaid invoice: set payment date and method to null.
     *
     * @param \Lf\Sales\Model\Creditmemo\Refund $subject
     * @param mixed $result
     * @param string $creditmemoId
     * @param string $dateReglement
     * @param string $modeReglement
     */
    public function afterRefund(Refund $subject, $result, $creditmemoId, $dateReglement, $modeReglement)
    {
        $creditMemo = $this->creditmemoRepository->get($creditmemoId);
        $invoice = $this->invoiceRepository->get($creditMemo->getInvoiceId());

        if ($invoice->getData('is_contribution') == 1) {
            if ($invoice->getState() != Invoice::STATE_PAID) {
                $creditMemo->setDateReglement(null);
            }

            $creditMemo->setModeReglement($invoice->getData('payment_method'));

            $this->creditmemoRepository->save($creditMemo);
        }
    }
}

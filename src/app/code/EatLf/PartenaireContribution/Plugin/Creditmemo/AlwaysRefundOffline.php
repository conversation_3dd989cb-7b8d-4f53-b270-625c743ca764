<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Creditmemo;

use Lf\Sales\Model\RefundOrder;
use Magento\Sales\Model\Order\Invoice;

class AlwaysRefundOffline
{
    /**
     * Contribution invoices must always be refunded offline.
     *
     * @param \Lf\Sales\Model\RefundOrder $subject
     * @param bool $result
     * @param Invoice $invoice
     *
     * @return bool
     */
    public function afterIsOnlineRefund(RefundOrder $subject, bool $result, Invoice $invoice): bool
    {
        if ($invoice->getData('is_contribution') == 1) {
            return false;
        }

        return $result;
    }
}

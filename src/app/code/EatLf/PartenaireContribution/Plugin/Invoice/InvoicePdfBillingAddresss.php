<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Invoice;

use EatLf\PartenaireContribution\Api\ContributionRepositoryInterface;
use Lf\Sales\Model\Order\Pdf\Invoice;
use Magento\Framework\Exception\NoSuchEntityException;

class InvoicePdfBillingAddresss
{
    /**
     * @var \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface
     */
    private ContributionRepositoryInterface $contributionRepository;

    /**
     * @param \EatLf\PartenaireContribution\Api\ContributionRepositoryInterface $contributionRepository
     */
    public function __construct(
        ContributionRepositoryInterface $contributionRepository
    ) {
        $this->contributionRepository = $contributionRepository;
    }

    /**
     * Show the contribution billing address for contribution invoices.
     *
     * @param \Lf\Sales\Model\Order\Pdf\Invoice $subject
     * @param string $result
     * @param \Magento\Sales\Model\Order\Invoice $invoice
     * @param \Magento\Sales\Model\Order $order
     *
     * @return string
     */
    public function afterGetBillingAddressText(Invoice $subject, $result, $invoice, $order)
    {
        if ($invoice->getData('is_contribution') != 1) {
            return $result;
        }

        try {
            $contribution = $this->contributionRepository->getById($order->getData('contribution_id'));
        } catch (NoSuchEntityException $e) {
            return $result;
        }

        return $contribution->getInvoiceFirstname() . ' ' . $contribution->getInvoiceLastname(
        ) . ' / ' . $contribution->getInvoiceAddress();
    }
}

<?php
declare(strict_types=1);

namespace EatLf\PartenaireContribution\Plugin\Invoice;

use Magento\Sales\Api\Data\CreditmemoInterface;
use Magento\Sales\Api\Data\InvoiceInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Model\Order\Validation\RefundInvoiceInterface;
use Magento\Sales\Model\ValidatorResultInterfaceFactory;

class AllowInvoiceRefund
{
    /**
     * @var \Magento\Sales\Model\ValidatorResultInterfaceFactory
     */
    private ValidatorResultInterfaceFactory $resultInterfaceFactory;

    /**
     * @param \Magento\Sales\Model\ValidatorResultInterfaceFactory $resultInterfaceFactory
     */
    public function __construct(
        ValidatorResultInterfaceFactory $resultInterfaceFactory
    ) {
        $this->resultInterfaceFactory = $resultInterfaceFactory;
    }

    /**
     * Always allow refund for invoices on an order with a contribution applied to it.
     *
     * @param \Magento\Sales\Model\Order\Validation\RefundInvoiceInterface $subject
     * @param \Magento\Sales\Model\ValidatorResultInterface $result
     * @param \Magento\Sales\Model\Order\Invoice $invoice
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param \Magento\Sales\Api\Data\CreditmemoInterface $creditmemo
     * @param array $items
     * @param bool $isOnline
     * @param bool $notify
     * @param bool $appendComment
     * @param \Magento\Sales\Api\Data\CreditmemoCommentCreationInterface|null $comment
     * @param \Magento\Sales\Api\Data\CreditmemoCreationArgumentsInterface|null $arguments
     *
     * @return \Magento\Sales\Model\ValidatorResultInterface
     */
    public function afterValidate(
        RefundInvoiceInterface $subject,
        $result,
        InvoiceInterface $invoice,
        OrderInterface $order,
        CreditmemoInterface $creditmemo,
        array $items = [],
        $isOnline = false,
        $notify = false,
        $appendComment = false,
        \Magento\Sales\Api\Data\CreditmemoCommentCreationInterface $comment = null,
        \Magento\Sales\Api\Data\CreditmemoCreationArgumentsInterface $arguments = null
    ) {
        if ($order->getData('contribution_id')) {
            return $this->resultInterfaceFactory->create();
        }

        return $result;
    }
}

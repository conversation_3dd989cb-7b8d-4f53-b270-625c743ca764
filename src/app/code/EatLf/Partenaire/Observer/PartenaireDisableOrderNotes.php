<?php
declare(strict_types=1);

namespace EatLf\Partenaire\Observer;

use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;
use EatLf\Partenaire\Model\Partenaire;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class PartenaireDisableOrderNotes implements ObserverInterface
{
    /**
     * @var OrderCollectionFactory
     */
    private $orderCollectionFactory;

    /**
     * @var OrderRepositoryInterface
     */
    private $orderRepository;

    public function __construct(
        OrderCollectionFactory   $orderCollectionFactory,
        OrderRepositoryInterface $orderRepository
    )
    {
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->orderRepository = $orderRepository;
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        /** @var Partenaire $partenaire */
        $partenaire = $observer->getDataObject();

        if (!$partenaire->getIsActive() && $partenaire->getOrigData('is_active')) {
            $orderCollection = $this->orderCollectionFactory->create();

            $orderCollection
                ->addFieldToFilter('main_table.partenaire_id', ['eq' => $partenaire->getId()]);

            $customerIds = [];
            foreach ($orderCollection->getItems() as $item) {
                $customerIds[$item['customer_id']] = $item;
            }

            foreach ($customerIds as $customerId => $order) {
                $order->setCustomerNote($order->getCustomerNote() . ' ');
                $this->orderRepository->save($order);
            }
        }
    }
}

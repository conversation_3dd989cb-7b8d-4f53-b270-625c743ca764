<?php
declare(strict_types=1);

namespace EatLf\Partenaire\Observer;

use EatLf\Partenaire\Api\Data\PartenaireInterface;
use EatLf\Partenaire\Model\Partenaire;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class PartenaireFulltextIndex implements ObserverInterface
{

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        /** @var Partenaire $partenaire */
        $partenaire = $observer->getDataObject();

        $fullText = join(
            ';',
            [
                $partenaire->getCompany(),
                $partenaire->getAddress(),
                $partenaire->getParentCompany()
            ]
        );

        $noSpaceFullText = str_replace(' ', '', $fullText);

        $partenaire->setData(PartenaireInterface::FULLTEXT_SEARCH, $noSpaceFullText);
    }
}
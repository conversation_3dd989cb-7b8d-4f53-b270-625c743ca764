<?php

namespace EatLf\Partenaire\Model;

use EatLf\Partenaire\Api\Data\LocalityInterface;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Model\AbstractModel;

class Locality extends AbstractModel implements LocalityInterface
{

    protected function _construct()
    {
        $this->_init(ResourceModel\Locality::class);
    }

    /**
     * {@inheritDoc}
     */
    public function getName()
    {
        return $this->getData(LocalityInterface::NAME);
    }

    /**
     * {@inheritDoc}
     */
    public function setName(string $name)
    {
        if (strlen($name) > LocalityInterface::NAME_MAXLENGTH) {
            throw new InputException(__('Locality name is too long'));
        }

        return $this->setData(LocalityInterface::NAME, $name);
    }
}
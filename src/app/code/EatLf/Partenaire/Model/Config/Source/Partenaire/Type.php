<?php

namespace EatLf\Partenaire\Model\Config\Source\Partenaire;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Magento\Framework\View\Element\Template\Context;

/**
 * Class Type
 * @package EatLf\Partenaire\Model\Config\Source\Partenaire
 */
class Type extends AbstractSource
{
    /**
     *  Technical ID for type COMPANY
     */
    const COMPANY = 1;

    /**
     * Technical ID for type RESTAURANT
     */
    const RESTAURANT = 2;
    /**
     * @var Context
     */
    private $context;

    private $scopeConfig;

    /**
     * Type constructor.
     * @param Context $context
     * @param array   $data
     */
    public function __construct(
        Context $context,
        array $data = []
    ) {
        $this->context = $context;
        $this->scopeConfig = $context->getScopeConfig();

    }

    /**
     * Get options as array
     *
     * @return array
     * @codeCoverageIgnore
     */
    public function toOptionArray()
    {
        return $this->getAllOptions();
    }

    /**
     * Retrieve All as simple array
     *
     * @return array
     */
    public function asArray(): array
    {
        return [
            self::COMPANY => $this->getShippingBarPartenaireMsg(),
            self::RESTAURANT => $this->getShippingBarRestaurantMsg(),
        ];
    }

    /**
     * Retrieve All options
     *
     * @return array
     */
    public function getAllOptions()
    {
        if (!$this->_options) {
            $this->_options = [
                ['label' => $this->getShippingBarPartenaireMsg(), 'value' => self::COMPANY],
                ['label' => $this->getShippingBarRestaurantMsg(), 'value' => self::RESTAURANT],
            ];
        }
        return $this->_options;
    }

    public function getShippingBarPartenaireMsg()
    {
        return $this->scopeConfig->getValue('web/shipping/shipping_bar_partenaire_delivery');
    }

    public function getShippingBarRestaurantMsg()
    {
        return $this->scopeConfig->getValue('web/shipping/shipping_bar_restaurant_delivery');
    }
}

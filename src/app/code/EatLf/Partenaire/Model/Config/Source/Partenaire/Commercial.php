<?php

namespace EatLf\Partenaire\Model\Config\Source\Partenaire;

use EatLf\Partenaire\Model\ResourceModel\Partenaire\CollectionFactory;
use Magento\Authorization\Model\Acl\Role\UserFactory;
use Magento\Authorization\Model\ResourceModel\Role\CollectionFactory as RoleCollectionFactory;
use Magento\Authorization\Model\UserContextInterface;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\Data\OptionSourceInterface;

class Commercial implements OptionSourceInterface
{
    const EVICTROLES = ['app-livreur', 'app-producteur', 'app-preparateur'];
    private Session $authSession;
    private CollectionFactory $partenaireCollectionFactory;
    private UserContextInterface $userContext;
    private \Magento\User\Model\ResourceModel\User\CollectionFactory $userCollectionFactory;
    private RoleCollectionFactory $roleCollectionFactory;
    private UserFactory $userFactory;

    public function __construct(
        Session                                                  $authSession,
        CollectionFactory                                        $partenaireCollectionFactory,
        UserContextInterface                                     $userContext,
        \Magento\User\Model\ResourceModel\User\CollectionFactory $userCollectionFactory,
        RoleCollectionFactory                                    $roleCollectionFactory,
        UserFactory                                              $userFactory

    ) {
        $this->authSession = $authSession;
        $this->partenaireCollectionFactory = $partenaireCollectionFactory;
        $this->userContext = $userContext;
        $this->userCollectionFactory = $userCollectionFactory;
        $this->roleCollectionFactory = $roleCollectionFactory;
        $this->userFactory = $userFactory;
    }

    /**
     * @return array
     */
    public function toOptionArray(): array
    {
        return $this->getAdminUsers();
    }

    private function getAdminUsers()
    {
        $adminUsers = [];

        $adminUsers[] = ['label' => 'Sans Commercial', 'value' => ' '];
        $userFranchiseId = $this->authSession->getUser()->getFranchiseId();
        foreach ($this->userCollectionFactory->create() as $user) {
            $roleName = $user->getRole()->getRoleName();
            $roleFound = array_search($roleName, SELF::EVICTROLES) !== false;

            if ($user->getFranchiseId() === $userFranchiseId && !$roleFound) {
                $adminUsers[] = array(
                    'label' => ucfirst(strtolower($user->getFirstName() . " " . $user->getLastName())),
                    'value' => ucfirst(strtolower($user->getFirstName() . " " . $user->getLastName()))
                );
            }
        }

        return $adminUsers;
    }
}


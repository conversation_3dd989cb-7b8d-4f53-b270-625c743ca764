<?php

namespace EatLf\Partenaire\Model\Config\Source;

/**
 * @api
 * @since 100.0.2
 */
class Actifs implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * Options getter
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [['value' => 1, 'label' => __('Actif')], ['value' => 0, 'label' => __('Inactif')]];
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function toArray()
    {
        return [0 => __('Inactif'), 1 => __('Actif')];
    }
}

<?php

namespace EatLf\Partenaire\Model\Config\Source;

use EatLf\Partenaire\Api\Data\PartenaireInterface;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\Data\OptionSourceInterface;
use EatLf\Partenaire\Model\ResourceModel\Partenaire\CollectionFactory;


class Partenaire implements OptionSourceInterface
{
    /**
     * @var Session $authSession
     */
    private Session $authSession;
    /**
     * @var CollectionFactory
     */
    private CollectionFactory $partenaireCollectionFactory;

    /**
     * @param Session $authSession
     * @param CollectionFactory $partenaireCollectionFactory
     */
    public function __construct(
        Session $authSession,
        CollectionFactory $partenaireCollectionFactory
    ) {
        $this->authSession = $authSession;
        $this->partenaireCollectionFactory = $partenaireCollectionFactory;
    }

    /**
     * @return array
     */
    public function toOptionArray(): array
    {
        $currentUser = $this->authSession->getUser();

        $partenaires = $this->partenaireCollectionFactory->create();
        if ($currentUser->getFranchiseId() != "") {
            $partenaires->addFieldToFilter(PartenaireInterface::FRANCHISE_ID, $currentUser->getFranchiseId());
        }
        $partenaires->getSelect()->order('company ASC');

        $result = [['value' => null, 'label' => " "]];

        foreach ($partenaires as $item) {
            $result[] = [
                'value' => $item->getCompany().'&&'.$item->getId(),
                'label' => $item->getCompany()
            ];
        }
        return $result;
    }
}

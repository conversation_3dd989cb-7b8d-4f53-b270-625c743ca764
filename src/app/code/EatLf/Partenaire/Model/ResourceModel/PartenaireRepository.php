<?php

namespace EatLf\Partenaire\Model\ResourceModel;

use EatLf\Partenaire\Api\Data\PartenaireSearchResultsInterfaceFactory;
use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\Partenaire\Model\PartenaireFactory;
use EatLf\Partenaire\Model\ResourceModel\Partenaire\CollectionFactory;
use Lf\Geocode\Api\GeocoderInterface;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\NoSuchEntityException;

class PartenaireRepository implements PartenaireRepositoryInterface
{
    /**
     * @var Partenaire
     */
    private $partenaireResource;
    /**
     * @var PartenaireFactory
     */
    private $partenaireFactory;
    /**
     * @var PartenaireSearchResultsInterfaceFactory
     */
    private $searchResultsFactory;
    /**
     * @var CollectionProcessorInterface
     */
    private $collectionProcessor;
    /**
     * @var CollectionFactory
     */
    private $collectionFactory;
    /**
     * @var array
     */
    private $cache;
    /**
     * @var GeocoderInterface
     */
    private $geocoder;

    public function __construct(
        Partenaire $partenaireResource,
        PartenaireFactory $partenaireFactory,
        PartenaireSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor,
        CollectionFactory $collectionFactory,
        GeocoderInterface $geocoder
    )
    {
        $this->partenaireResource = $partenaireResource;
        $this->partenaireFactory = $partenaireFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
        $this->collectionFactory = $collectionFactory;
        $this->geocoder = $geocoder;
    }

    /**
     * {@inheritDoc}
     */
    public function getById($partenaireId)
    {
        if (isset($this->cache[$partenaireId])) {
            return $this->cache[$partenaireId];
        }

        $partenaire = $this->partenaireFactory->create();

        $this->partenaireResource->load($partenaire, $partenaireId, 'id');

        if (!$partenaire->getId()) {
            throw new NoSuchEntityException(__('Partner company not found for the specified id'));
        }

        $this->cache[$partenaire->getId()] = $partenaire;

        return $partenaire;
    }

    /**
     * {@inheritDoc}
     */
    public function save($partenaire)
    {
        if (isset($this->cache[$partenaire->getId()])) {
             unset($this->cache[$partenaire->getId()]);
        }

        $coords = $this->geocoder->geocode(
            $partenaire->getAddress(),
            $partenaire->getPostcode(),
            $partenaire->getCity()
        );

        $partenaire
            ->setLatitude($coords['latitude'])
            ->setLongitude($coords['longitude']);

        $this->partenaireResource->save($partenaire);

        return $partenaire;
    }

    /**
     * {@inheritDoc}
     */
    public function delete($partenaireId)
    {
        $this->partenaireResource->delete(
            $this->getById($partenaireId)
        );
    }

    /**
     * {@inheritDoc}
     */
    public function getList(SearchCriteriaInterface $criteria)
    {
        $collection = $this->collectionFactory->create();

        $this->collectionProcessor->process($criteria, $collection);

        return $this->searchResultsFactory->create()
            ->setItems($collection->getItems());
    }


    /**
     * @param $quoteId
     * @return Partenaire|mixed
     * @throws NoSuchEntityException
     */
    public function getForQuote($quoteId)
    {
        if (!isset($this->partenaireByQuoteId[$quoteId])) {
            $partenaire = $this->loadPartenaire('loadByQuote', 'quoteId', $quoteId);

            $this->partenaireById[$partenaire->getId()] = $partenaire;
            $this->partenaireByQuoteId[$quoteId] = $partenaire;
        }
        return $this->partenaireByQuoteId[$quoteId];
    }

    /**
     * @param $loadMethod
     * @param $loadField
     * @param $identifier
     * @return Partenaire
     * @throws NoSuchEntityException
     */
    protected function loadPartenaire($loadMethod, $loadField, $identifier)
    {
        /** @var Partenaire $partenaire */
        $partenaire = $this->partenaireFactory->create();
        $partenaire->$loadMethod($identifier);
        if (!$partenaire->getId()) {
            throw NoSuchEntityException::singleField($loadField, $identifier);
        }
        return $partenaire;
    }
}

<?php

namespace EatLf\Partenaire\Model\ResourceModel\Partenaire\Grid;

use EatLf\Partenaire\Model\Config\Source\Partenaire\Type;
use EatLf\Partenaire\Model\ResourceModel\Partenaire;
use Magento\Framework\Data\Collection\Db\FetchStrategyInterface as FetchStrategy;
use Magento\Framework\Data\Collection\EntityFactoryInterface as EntityFactory;
use Magento\Framework\Event\ManagerInterface as EventManager;
use Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult;
use Psr\Log\LoggerInterface as Logger;

class Collection extends SearchResult
{
    public function __construct(
        Type $partenaireType,
        EntityFactory $entityFactory,
        Logger $logger,
        FetchStrategy $fetchStrategy,
        EventManager $eventManager,
        $identifierName = null,
        $connectionName = null
    )
    {
        parent::__construct(
            $entityFactory,
            $logger,
            $fetchStrategy,
            $eventManager,
            'partenaire',
            Partenaire::class,
            $identifierName,
            $connectionName
        );

        $typeLibelleExpr = "CASE ";
        foreach($partenaireType->getAllOptions() as $partenaireType)
        {
            $typeLibelleExpr.="WHEN main_table.type=".$partenaireType['value']." THEN '".$partenaireType['label']."' ";
        }
        $typeLibelleExpr .= " END";


        $this->getSelect()->columns(['type_libelle' => new \Zend_Db_Expr($typeLibelleExpr)]);
        $this->addFilterToMap('type_libelle', new \Zend_Db_Expr($typeLibelleExpr));

        $this->join(
            ['franchise' => 'franchise'],
            'franchise.franchise_id = main_table.franchise_id',
            ['franchise_name' => 'name']
        )->join(
            ['printer' => 'printer'],
            'printer.id = main_table.printer_id',
            ['printer_name' => 'name']
        )->join(
            ['partenaire_locality' => 'partenaire_locality'],
            'partenaire_locality.id = main_table.locality_id',
            ['locality_name' => 'name']
        );
    }
}
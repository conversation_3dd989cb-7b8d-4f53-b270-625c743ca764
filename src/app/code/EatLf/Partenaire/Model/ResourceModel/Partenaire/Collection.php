<?php

namespace EatLf\Partenaire\Model\ResourceModel\Partenaire;

use EatLf\Partenaire\Model\Config\Source\Partenaire\Type;
use EatLf\Partenaire\Model\ResourceModel\Locality as LocalityRessource;
use EatLf\Partenaire\Model\ResourceModel\Partenaire;
use Magento\Framework\Data\Collection\Db\FetchStrategyInterface;
use Magento\Framework\Data\Collection\EntityFactoryInterface;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Psr\Log\LoggerInterface;

class Collection extends AbstractCollection
{
    /**
     * @var LocalityRessource
     */
    private $localityRessource;
    /**
     * @var Type
     */
    private $partenaireType;

    public function __construct(
        Type $partenaireType,
        EntityFactoryInterface $entityFactory,
        LoggerInterface $logger,
        FetchStrategyInterface $fetchStrategy,
        ManagerInterface $eventManager,
        Partenaire $resource,
        LocalityRessource $localityRessource,
        AdapterInterface $connection = null
    )
    {
        $this->_init(\EatLf\Partenaire\Model\Partenaire::class, Partenaire::class);
        parent::__construct($entityFactory, $logger, $fetchStrategy, $eventManager, $connection, $resource);
        $this->localityRessource = $localityRessource;
        $this->partenaireType = $partenaireType;
    }

    /**
     * @return array
     */
    public function getListWithLocalities()
    {
        $typeLibelleExpr = "CASE ";
        foreach($this->partenaireType->getAllOptions() as $type)
        {
            $typeLibelleExpr.="WHEN partenaire.type=".$type['value']." THEN '".$type['label']."' ";
        }
        $typeLibelleExpr .= " END";

        $connection = $this->getConnection();
        $select = $connection->select()
            ->from($this->getResource()->getMainTable())
            ->join(['pl' => $this->localityRessource->getMainTable()], 'partenaire.locality_id = pl.id')
            ->reset(\Zend_Db_Select::COLUMNS)
            ->columns(['partenaire.type as type_id', 'type_name' => new \Zend_Db_Expr($typeLibelleExpr), 'locality_id', 'pl.name as locality_name'])
            ->order(['partenaire.type ASC', 'pl.name ASC'])
            ->where('is_active = 1')
            ->distinct(true);
        ;

        return $connection->query($select)->fetchAll();
    }
}
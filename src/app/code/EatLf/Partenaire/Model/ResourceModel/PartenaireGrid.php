<?php

namespace EatLf\Partenaire\Model\ResourceModel;

use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\DB\Select;
use Magento\Framework\Model\ResourceModel\Db\Context;
use Zend_Db_Statement_Interface;

/**
 * Class Grid
 */
class PartenaireGrid extends AbstractGrid implements PartenaireGridInterface
{

    /**
     * @var string $mainTableName
     */
    private $mainTableName;
    /**
     * @var string $gridTableName
     */
    protected $gridTableName;
    /**
     * @var string $partenaireIdField
     */
    private $partenaireIdField;
    /**
     * @var array
     */
    private $joins;
    /**
     * @var array
     */
    private $columns;

    public function __construct(
        Context                        $context,
                                       $mainTableName,
                                       $gridTableName,
                                       $partenaireIdField,
        array                          $joins = [],
        array                          $columns = [],
                                       $connectionName = null
    ) {
        $this->mainTableName = $mainTableName;
        $this->gridTableName = $gridTableName;
        $this->partenaireIdField = $partenaireIdField;
        $this->joins = $joins;
        $this->columns = $columns;
        parent::__construct($context, $connectionName);
    }

    /**
     * @return Select
     */
    protected function getGridOriginSelect()
    {
        $select = $this->getConnection()->select()
            ->from([$this->mainTableName => $this->getTable($this->mainTableName)], []);
        foreach ($this->joins as $joinName => $data) {
            $select->joinLeft(
                [$joinName => $this->getTable($data['table'])],
                sprintf(
                    '%s.%s = %s.%s',
                    $this->mainTableName,
                    $data['origin_column'],
                    $joinName,
                    $data['target_column']
                ),
                []
            );
        }
        $columns = [];
        foreach ($this->columns as $key => $value) {
            if ($value instanceof \Zend_Db_Expr) {
                $columns[$key] = $value;
            } else {
                $columns[$key] = new \Zend_Db_Expr($value);
            }
        }
        $select->columns($columns);
        return $select;
    }

    /**
     * @param int|string $value
     * @param null $field
     * @return Zend_Db_Statement_Interface
     */
    public function refresh($value, $field = null)
    {
        $select = $this->getGridOriginSelect()
            ->where(($field ?: $this->mainTableName . '.id') . ' = ?', $value);
        $sql = $this->getConnection()
            ->insertFromSelect(
                $select,
                $this->getTable($this->gridTableName),
                array_keys($this->columns),
                AdapterInterface::INSERT_ON_DUPLICATE
            );

        $this->addCommitCallback(function () use ($sql) {
            $this->getConnection()->query($sql);
        });

        return $this->getConnection()->query($sql);
    }

    public function deletePartenaire($partenaireId)
    {
        $connection = $this->getConnection();
        $tableName = $connection->getTableName($this->gridTableName);
        $where = ['partenaire_id = ?' => $partenaireId];
        $connection->delete($tableName, $where);
    }

    public function updateFranchise($id, $value)
    {
        $connection = $this->getConnection();
        $sql = ["franchise_name" => $value];
        $where = ['franchise_id = ?' => $id];
        $tableName = $connection->getTableName($this->gridTableName);
        return $connection->update($tableName, $sql, $where);
    }

    public function deleteFranchise($value)
    {
        $connection = $this->getConnection();
        $tableName = $connection->getTableName($this->gridTableName);
        $where = ['franchise_id = ?' => $value];
        $connection->delete($tableName, $where);
    }

    public function updatePrinter($id, $value)
    {
        $connection = $this->getConnection();
        $sql = ["printer_name" => $value];
        $where = ['printer_id = ?' => $id];
        $tableName = $connection->getTableName($this->gridTableName);
        return $connection->update($tableName, $sql, $where);
    }

    public function deletePrinter($value)
    {
        $connection = $this->getConnection();
        $tableName = $connection->getTableName($this->gridTableName);
        $where = ['printer_id = ?' => $value];
        $connection->delete($tableName, $where);
    }
}

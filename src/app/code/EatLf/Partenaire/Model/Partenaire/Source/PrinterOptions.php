<?php

namespace EatLf\Partenaire\Model\Partenaire\Source;

use Lf\Printer\Api\PrinterRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Backend\Model\Auth\Session;
use Magento\Security\Model\Plugin\AuthSession;

class PrinterOptions implements OptionSourceInterface
{
    /**
     * @var PrinterRepositoryInterface
     */
    private $printerRepository;
    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;
    private  $authSession;

    public function __construct(
        PrinterRepositoryInterface $printerRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        Session $authSession
    )
    {
        $this->printerRepository = $printerRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->authSession = $authSession;
    }

    public function toOptionArray()
    {
        $userFranchiseId = $this->authSession->getUser()->getFranchiseId();
        $result = [];
        $this->searchCriteriaBuilder->addFilter('franchise_id',$userFranchiseId, 'in');
        $criteria = $this->searchCriteriaBuilder->create();

        foreach ($this->printerRepository->getList($criteria)->getItems() as $item) {
            $result[] = [
                'value' => $item->getId(),
                'label' => $item->getName(),
                'franchise_id' => $item->getFranchiseId()
            ];
        }

        return $result;
    }
}

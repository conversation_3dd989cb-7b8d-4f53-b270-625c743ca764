<?php

namespace EatLf\Partenaire\Ui\Component\Listing\Column;

use EatLf\Partenaire\Model\Partenaire\Access;
use Magento\Framework\AuthorizationInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class PartenaireActions extends Column
{
    /**
     * @var AuthorizationInterface
     */
    private $authorization;
    /**
     * @var UrlInterface
     */
    private $url;

    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        AuthorizationInterface $authorization,
        UrlInterface $url,
        array $components = [],
        array $data = []
    )
    {
        parent::__construct($context, $uiComponentFactory, $components, $data);
        $this->authorization = $authorization;
        $this->url = $url;
    }

    public function prepareDataSource(array $dataSource)
    {
        if (!$this->authorization->isAllowed(Access::ACCESS_EDIT)) {
            return $dataSource;
        }

        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as & $item) {
                if (isset($item['id'])) {
                    $item[$this->getData('name')] = [
                        'edit' => [
                            'href' => $this->url->getUrl(
                                'partenaires/form/edit',
                                [ 'id' => $item['id'] ]
                            ),
                            'label' => __('Edit')
                        ]
                    ];
                }
            }
        }

        return $dataSource;
    }
}
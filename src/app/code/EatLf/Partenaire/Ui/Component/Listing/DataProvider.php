<?php

namespace EatLf\Partenaire\Ui\Component\Listing;

use Magento\Backend\Model\Auth\Session;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\ReportingInterface;
use Magento\Framework\Api\Search\SearchCriteriaBuilder;
use Magento\Framework\App\RequestInterface;

class DataProvider extends \Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider
{
    /**
     * @var Session
     */
    private $authSession;

    /**
     * DataProvider constructor.
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param Session $authSession
     * @param ReportingInterface $reporting
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param RequestInterface $request
     * @param FilterBuilder $filterBuilder
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        Session $authSession,
        ReportingInterface $reporting,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        RequestInterface $request,
        FilterBuilder $filterBuilder,
        array $meta = [],
        array $data = []
    )
    {
        parent::__construct($name,
            $primaryFieldName,
            $requestFieldName,
            $reporting,
            $searchCriteriaBuilder,
            $request,
            $filterBuilder,
            $meta,
            $data);
        $this->authSession = $authSession;
    }

    public function addFilter(\Magento\Framework\Api\Filter $filter)
    {
        $filter->setField('main_table.' . $filter->getField());

        return parent::addFilter($filter);
    }

    public function getSearchCriteria()
    {
        $currentUser = $this->authSession->getUser();

        if($currentUser->getFranchiseId()!="")
        {
            $franchiseFilter = $this->filterBuilder->setField('franchise_id')
                ->setValue($currentUser->getFranchiseId())
                ->setConditionType('eq')
                ->create();

            $this->addFilter($franchiseFilter);
        }

        return parent::getSearchCriteria();
    }
}
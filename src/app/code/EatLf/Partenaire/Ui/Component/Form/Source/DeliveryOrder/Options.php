<?php
declare(strict_types=1);

namespace EatLf\Partenaire\Ui\Component\Form\Source\DeliveryOrder;

use Magento\Framework\Data\OptionSourceInterface;

class Options implements OptionSourceInterface
{

    /**
     * @inheritDoc
     */
    public function toOptionArray()
    {
        $values = [
            ['label' => ' ', 'value' => null]
        ];

        for ($i = 1; $i <= 100; $i++) {
            $values[] = [
                'label' => "{$i}",
                'value' => "{$i}"
            ];
        }

        return $values;
    }
}
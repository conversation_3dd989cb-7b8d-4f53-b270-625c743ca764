<?php

namespace EatLf\Partenaire\Api\Data;

interface PartenaireInterface
{
    const FRANCHISE_ID = 'franchise_id';

    const PRINTER_ID = 'printer_id';

    const TYPE = 'type';

    const LOCALITY_ID = 'locality_id';

    const IS_ACTIVE = 'is_active';

    const COMPANY = 'company';

    const PARENT_COMPANY = 'parent_company';

    const ADDRESS = 'address';

    const CITY = 'city';

    const POSTCODE = 'postcode';

    const LOGO = 'logo';

    const SHIPPING_SLOT = 'shipping_slot';

    const SHIPPING_COMMENT = 'shipping_comment';

    const LAST_ORDER_TIME = 'last_order_time';

    const COUNTRY_ID = 'country_id';

    const LATITUDE = 'latitude';

    const LONGITUDE = 'longitude';

    const FULLTEXT_SEARCH = 'fulltext_search';

    const EFFECTIF = 'effectif';

    const MONTANT_TICKET_RESTO = 'montant_ticket_resto';

    const URNE = 'urne';

    const ETAGERE = 'etagere';

    const FRIGIDAIRE = 'frigidaire';

    const CUSTOM_MESSAGE_ENABLED = 'custom_message_enabled';

    const CUSTOM_MESSAGE = 'custom_message';

    /**
     * @return int
     */
    public function getId();

    /**
     * @return int
     */
    public function getFranchiseId();

    /**
     * @return int
     */
    public function getType();

    /**
     * @return int
     */
    public function getLocalityId();


    /**
     * @return int
     */
    public function getPrinterId();

    /**
     * @return bool
     */
    public function getIsActive();

    /**
     * @return string
     */
    public function getCompany();

    /**
     * @return string
     */
    public function getParentCompany();

    /**
     * @return string
     */
    public function getAddress();

    /**
     * @return string
     */
    public function getCity();

    /**
     * @return string
     */
    public function getPostcode();

    /**
     * @return string
     */
    public function getLogo();

    /**
     * @return string
     */
    public function getShippingSlot();

    /**
     * @return string
     */
    public function getShippingComment();

    /**
     * @return int
     */
    public function getLastOrderTime();

    /**
     * @return string
     */
    public function getCountryId();

    /**
     * @return string
     */
    public function getLatitude();

    /**
     * @return string
     */
    public function getLongitude();

    /**
     * @param int $value
     * @return self
     */
    public function setId($value);

    /**
     * @param int $franchiseId
     * @return self
     */
    public function setFranchiseId($franchiseId);

    /**
     * @param int $type
     * @return self
     */
    public function setType($type);

    /**
     * @param int $localityId
     * @return self
     */
    public function setLocalityId($localityId);

    /**
     * @param int $printerId
     * @return self
     */
    public function setPrinterId($printerId);

    /**
     * @param bool $isActive
     * @return self
     */
    public function setIsActive(bool $isActive);

    /**
     * @param string $company
     * @return self
     */
    public function setCompany(string $company);

    /**
     * @param string $parentCompany
     * @return self
     */
    public function setParentCompany(string $parentCompany);

    /**
     * @param string $address
     * @return self
     */
    public function setAddress(string $address);

    /**
     * @param string $city
     * @return self
     */
    public function setCity(string $city);

    /**
     * @param string $postcode
     * @return self
     */
    public function setPostcode(string $postcode);

    /**
     * @param string $logo
     * @return self
     */
    public function setLogo(string $logo);

    /**
     * @param string $shippingSlot
     * @return self
     */
    public function setShippingSlot(string $shippingSlot);

    /**
     * @param string $shippingComment
     * @return self
     */
    public function setShippingComment(string $shippingComment);

    /**
     * @param int $lastOrderTime
     * @return self
     */
    public function setLastOrderTime(int $lastOrderTime);

    /**
     * @param string $countryId
     * @return self
     */
    public function setCountryId(string $countryId);

    /**
     * @param string $latitude
     * @return self
     */
    public function setLatitude(string $latitude);

    /**
     * @param string $longitude
     * @return self
     */
    public function setLongitude(string $longitude);

    /**
     * @return int
     */
    public function getEffectif();

    /**
     * @param int $effectif
     * @return self
     */
    public function setEffectif(int $effectif);

    /**
     * @return string
     */
    public function getMontantTicketResto();

    /**
     * @param double $montant_ticket_resto
     * @return self
     */
    public function setMontantTicketResto(string $montant_ticket_resto);

    /**
     * @return bool
     */
    public function getUrne();

    /**
     * @return bool
     */
    public function getEtagere();

    /**
     * @return bool
     */
    public function getFrigidaire();

    /**
     * @param bool $urne
     * @return self
     */
    public function setUrne(bool $urne);

    /**
     * @param bool $etagere
     * @return self
     */
    public function setEtagere(bool $etagere);

    /**
     * @param bool $frigidaire
     * @return self
     */
    public function setFrigidaire(bool $frigidaire);

    /**
     * @return bool
     */
    public function getCustomMessageEnabled();

    /**
     * @param bool $custom_message_enabled
     * @return self
     */
    public function setCustomMessageEnabled(bool $custom_message_enabled);

    /**
     * @return string
     */
    public function getCustomMessage();

    /**
     * @param string $custom_message
     * @return self
     */
    public function setCustomMessage(string $custom_message);

}

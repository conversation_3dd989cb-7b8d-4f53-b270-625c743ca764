<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="EatLf_Partenaire::partenaires"
             title="Entreprises EatLf"
             translate="title"
             module="EatLf_Partenaire"
             parent="Lf_Franchise::franchise_header"
             sortOrder="60"
             dependsOnModule="Lf_Franchise"
             action="partenaires/listing"
             resource="EatLf_Partenaire::listing"/>
        <add id="EatLf_Partenaire::localities"
             title="Localités"
             translate="title"
             module="EatLf_Partenaire"
             parent="Lf_Franchise::franchise_header"
             sortOrder="70"
             dependsOnModule="Lf_Franchise"
             action="partenaires/locality/listing"
             resource="EatLf_Partenaire::locality_view"/>
    </menu>
</config>
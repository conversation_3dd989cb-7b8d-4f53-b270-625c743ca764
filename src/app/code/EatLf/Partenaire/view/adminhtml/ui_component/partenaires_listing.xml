<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd" class="EatLf\Partenaire\Rewrite\Partenairelisting">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">partenaires_listing.partenaires_listing_data_source</item>
            <item name="deps" xsi:type="string">partenaires_listing.partenaires_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">spinner_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
                <item name="name" xsi:type="string">add</item>
                <item name="label" xsi:type="string" translate="true">Add a partner</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/form/create</item>
            </item>
        </item>
    </argument>
    <dataSource name="partenaires_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <updateUrl path="mui/index/render"/>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
            </storageConfig>
        </settings>
        <aclResource>EatLf_Partenaire::listing</aclResource>
        <dataProvider class="EatLf\Partenaire\Ui\Component\Listing\DataProvider" name="partenaires_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="sticky" xsi:type="boolean">true</item>
            </item>
        </argument>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <filterSelect name="franchise_id" provider="${ $.parentName }">
                <settings>
                    <options class="Lf\Franchise\Model\Franchise\Source\Options" />
                    <label translate="true">Franchise</label>
                    <caption translate="true">All franchises</caption>
                    <dataScope>franchise_id</dataScope>
                </settings>
            </filterSelect>
            <filterSelect name="type" provider="${ $.parentName }">
                <settings>
                    <options class="EatLf\Partenaire\Model\Config\Source\Partenaire\Type" />
                    <label translate="true">Type</label>
                    <caption translate="true">All Type</caption>
                    <dataScope>type</dataScope>
                </settings>
            </filterSelect>
            <filterSelect name="printer_id" provider="${ $.parentName }">
                <settings>
                    <options class="Lf\Printer\Model\Printer\Source\Options" />
                    <label translate="true">Printer</label>
                    <caption translate="true">All printers</caption>
                    <dataScope>printer_id</dataScope>
                </settings>
            </filterSelect>
            <filterSelect name="urne" provider="${ $.parentName }">
                <settings>
                    <options class="EatLf\Partenaire\Model\Config\Source\Yesnomaybe" />
                    <label translate="true">Urne</label>
                    <dataScope>urne</dataScope>
                </settings>
            </filterSelect>
            <filterSelect name="etagere" provider="${ $.parentName }">
                <settings>
                    <options class="EatLf\Partenaire\Model\Config\Source\Yesnomaybe" />
                    <label translate="true">Étagère</label>
                    <dataScope>etagere</dataScope>
                </settings>
            </filterSelect>
            <filterSelect name="frigidaire" provider="${ $.parentName }">
                <settings>
                    <options class="EatLf\Partenaire\Model\Config\Source\Yesnomaybe" />
                    <label translate="true">Frigidaire</label>
                    <dataScope>frigidaire</dataScope>
                </settings>
            </filterSelect>
            <filterSelect name="locality_id" provider="${ $.parentName }">
                <settings>
                    <options class="EatLf\Partenaire\Model\Locality\Source\Options" />
                    <label translate="true">Locality</label>
                    <caption translate="true">All localities</caption>
                    <dataScope>locality_id</dataScope>
                </settings>
            </filterSelect>
            <filterRange name="last_order_time" provider="${ $.parentName }">
                <settings>
                    <label translate="true">Last order time (1215 for 12h15)</label>
                    <rangeType>text</rangeType>
                    <dataScope>last_order_time</dataScope>
                </settings>
            </filterRange>
            <filterRange name="daily_print_start" provider="${ $.parentName }">
                <settings>
                    <label translate="true">Daily print start (0815 for 08h15)</label>
                    <rangeType>text</rangeType>
                    <dataScope>daily_print_start</dataScope>
                </settings>
            </filterRange>
            <filterRange name="daily_print_end" provider="${ $.parentName }">
                <settings>
                    <label translate="true">Daily print end (1300 for 13h00)</label>
                    <rangeType>text</rangeType>
                    <dataScope>daily_print_end</dataScope>
                </settings>
            </filterRange>
        </filters>
        <paging name="listing_paging"/>
        <exportButton name="export_button"/>
    </listingToolbar>
    <columns name="spinner_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="partenaires/listing/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">partenaires_listing.partenaires_listing.spinner_columns.ids
                </param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">partenaires_listing.partenaires_listing.spinner_columns_editor
                    </item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>id</indexField>
            </settings>
        </selectionsColumn>
        <column name="company" sortOrder="1">
            <settings>
                <filter>text</filter>
                <label translate="true">Company</label>
            </settings>
        </column>
        <column name="type_libelle" sortOrder="2">
            <settings>
                <filter>text</filter>
                <label translate="true">Type</label>
            </settings>
        </column>
        <column name="locality_name" sortOrder="3">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Locality</label>
            </settings>
        </column>
        <column name="parent_company" sortOrder="4">
            <settings>
                <filter>text</filter>
                <label translate="true">Parent company</label>
            </settings>
        </column>
        <column name="address" sortOrder="5">
            <settings>
                <label translate="true">Address</label>
            </settings>
        </column>
        <column name="city" sortOrder="6">
            <settings>
                <label translate="true">City</label>
            </settings>
        </column>
        <column name="postcode" sortOrder="7">
            <settings>
                <label translate="true">Postcode</label>
            </settings>
        </column>
        <column name="franchise_name" sortOrder="8">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Franchise</label>
            </settings>
        </column>
        <column name="printer_id" sortOrder="9">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">EatLf\Partenaire\Model\Partenaire\Source\PrinterOptions</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="sortOrder" xsi:type="number">9</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Printer</item>
                    <item name="editor" xsi:type="string">select</item>
                </item>
            </argument>
        </column>
        <column name="delivery_order" sortOrder="10">
            <settings>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">false</rule>
                        <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>text</editorType>
                </editor>
                <filter>text</filter>
                <dataType>text</dataType>
                <label translate="true">Delivery order</label>
            </settings>
        </column>
        <column name="shipping_slot" sortOrder="11">
            <settings>
                <label translate="true">Shipping timeslot</label>
            </settings>
        </column>
        <column name="shipping_comment" sortOrder="12">
            <settings>
                <label translate="true">Shipping info</label>
            </settings>
        </column>
        <column name="last_order_time" class="EatLf\Partenaire\Ui\Component\Listing\Column\Time" sortOrder="13">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Last order time</label>
            </settings>
        </column>
        <column name="daily_print_start" class="EatLf\Partenaire\Ui\Component\Listing\Column\Time" sortOrder="14">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Daily print start</label>
            </settings>
        </column>
        <column name="daily_print_end" class="EatLf\Partenaire\Ui\Component\Listing\Column\Time" sortOrder="15">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Daily print end</label>
            </settings>
        </column>
        <column name="custom_message_enabled" sortOrder="16">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">EatLf\Partenaire\Model\Config\Source\Yesnomaybe</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="bodyTmpl" xsi:type="string">ui/grid/cells/html</item>
                    <item name="sortable" xsi:type="boolean">true</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="label" xsi:type="string" translate="true">Custom message</item>
                </item>
            </argument>
        </column>
        <column name="is_active" sortOrder="17">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">EatLf\Partenaire\Model\Config\Source\Actifs</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="bodyTmpl" xsi:type="string">ui/grid/cells/html</item>
                    <item name="sortable" xsi:type="boolean">true</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="label" xsi:type="string" translate="true">Actif/Inactif</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="actions" class="EatLf\Partenaire\Ui\Component\Listing\Column\PartenaireActions">
            <settings>
                <indexField>id</indexField>
            </settings>
        </actionsColumn>
        <column name="commercial_in_charge" sortOrder="12">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                </item>
            </argument>
            <settings>
                <label translate="true">Commercial en charge</label>
            </settings>
        </column>
    </columns>
</listing>


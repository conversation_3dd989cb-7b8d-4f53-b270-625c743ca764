<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">locality_form_edit.localities_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">General Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="save" class="EatLf\Partenaire\Block\Adminhtml\Locality\Buttons\SaveButton"/>
            <button name="back" class="EatLf\Partenaire\Block\Adminhtml\Locality\Buttons\BackButton"/>
        </buttons>
        <namespace>locality_form_edit</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>locality_form_edit.localities_data_source</dep>
        </deps>
    </settings>
    <dataSource name="localities_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="*/*/save"/>
        </settings>
        <dataProvider class="EatLf\Partenaire\Ui\Component\Locality\Form\DataProvider" name="localities_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <fieldset name="general">
        <settings>
            <label translate="true">General Information</label>
        </settings>
        <field name="id" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>false</visible>
            </settings>
        </field>
        <field name="name" formElement="input">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Locality name</label>
                <notice translate="true">Maximum 55 characters</notice>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="maxlength" xsi:type="string">10</rule>
                </validation>
            </settings>
        </field>
    </fieldset>
</form>

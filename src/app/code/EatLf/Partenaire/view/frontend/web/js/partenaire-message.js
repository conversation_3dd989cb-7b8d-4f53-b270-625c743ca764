define([
    'jquery',
    'ko',
    'uiComponent',
    'EatLf_Shipping/js/model/shipping'
], function (
    $,
    ko,
    Component,
    shippingModel
) {
    'use strict';
    var SESSION_KEY = 'partenaire-message-hidden';

    return Component.extend({
        initialize: function () {
            this._super();

            var askToHide = sessionStorage.getItem(SESSION_KEY);
            if (askToHide === 'true') {
                $('#message-partenaire').hide();
                return;
            }

            $('.message-partenaire-close').on('click', function () {
                sessionStorage.setItem(SESSION_KEY, 'true');
                $('#message-partenaire').hide();
            });

            ko.computed(this.onShippingDataChange.bind(this));
        },

        onShippingDataChange: function () {
            var askToHide = sessionStorage.getItem(SESSION_KEY);
            if (askToHide === 'true') {
                return true;
            }

            var shippingData = shippingModel.data();
            if (shippingData.selectedPartenaireMessage) {
                $('#message-partenaire-txt').html(shippingData.selectedPartenaireMessage);
                $('#message-partenaire').show();
            }
        },
    });
});

<?php

namespace EatLf\Partenaire\Controller\Adminhtml\Locality;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;

class Edit extends Action
{
    const ADMIN_RESOURCE = Access::ACCESS_EDIT;

    /**
     * @var PageFactory
     */
    private $pageFactory;

    public function __construct(
        Context $context,
        PageFactory $pageFactory
    )
    {
        parent::__construct($context);
        $this->pageFactory = $pageFactory;
    }

    public function execute()
    {
        $resultPage = $this->pageFactory->create();

        $resultPage->getConfig()->getTitle()->prepend((__('Edit a locality')));

        return $resultPage;
    }
}
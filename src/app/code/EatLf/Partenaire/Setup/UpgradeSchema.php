<?php

namespace EatLf\Partenaire\Setup;

use Magento\Framework\DB\Ddl\Table;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\UpgradeSchemaInterface;

class UpgradeSchema implements UpgradeSchemaInterface
{
    /**
     * Upgrades DB schema for a module
     *
     * @param SchemaSetupInterface $setup
     * @param ModuleContextInterface $context
     * @return void
     */
    public function upgrade(
        SchemaSetupInterface $setup,
        ModuleContextInterface $context
    )
    {
        $setup->startSetup();

        if (version_compare($context->getVersion(),
            '0.0.2',
            '<')) {
            $setup
                ->getConnection()
                ->modifyColumn(
                    $setup->getTable('partenaire'),
                    'shipping_slot',
                    [
                        'type' => Table::TYPE_TEXT,
                        'nullable' => false
                    ]
                );
        }

        if (version_compare($context->getVersion(),
            '0.0.3',
            '<')) {
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'country_id',
                    [
                        'type' => Table::TYPE_TEXT,
                        'nullable' => false,
                        'comment' => 'Pays du partenaire'
                    ]
                );
        }

        if (version_compare($context->getVersion(),
            '0.0.4',
            '<')) {
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'longitude',
                    [
                        'type' => Table::TYPE_TEXT,
                        'nullable' => false,
                        'comment' => 'Longitude du partenaire'
                    ]
                );

            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'latitude',
                    [
                        'type' => Table::TYPE_TEXT,
                        'nullable' => false,
                        'comment' => 'Latitude du partenaire'
                    ]
                );
        }

        if (version_compare($context->getVersion(),
            '0.0.5',
            '<')) {
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'fulltext_search',
                    [
                        'type' => Table::TYPE_TEXT,
                        'nullable' => true,
                        'comment' => 'Fulltext search column'
                    ]
                );
        }

        if (version_compare($context->getVersion(),
            '0.0.6',
            '<')) {
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'daily_print_start',
                    [
                        'type' => Table::TYPE_INTEGER,
                        'nullable' => false,
                        'comment' => 'Heure de début des impressions'
                    ]
                );
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'daily_print_end',
                    [
                        'type' => Table::TYPE_INTEGER,
                        'nullable' => false,
                        'comment' => 'Heure de fin des impressions'
                    ]
                );
        }

        if (version_compare($context->getVersion(),
            '0.0.7',
            '<')) {
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'contact_name',
                    [
                        'type' => Table::TYPE_TEXT,
                        'nullable' => true,
                        'comment' => 'Nom du contact'
                    ]
                );
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'contact_email',
                    [
                        'type' => Table::TYPE_TEXT,
                        'nullable' => true,
                        'comment' => 'Email du contact'
                    ]
                );
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'contact_phone',
                    [
                        'type' => Table::TYPE_TEXT,
                        'nullable' => true,
                        'comment' => 'Téléphone du contact'
                    ]
                );
        }

        if (version_compare($context->getVersion(),
            '0.0.8',
            '<')) {
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'delivery_order',
                    [
                        'type' => Table::TYPE_INTEGER,
                        'nullable' => true,
                        'comment' => 'Ordre de tournée'
                    ]
                );
        }

        if (version_compare($context->getVersion(),
            '0.0.9',
            '<')) {
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'type',
                    [
                        'type' => Table::TYPE_INTEGER,
                        'nullable' => false,
                        'comment' => 'Type de partenaire',
                        'default' => '1',
                        'after' => 'is_active'
                    ]
                );
        }

        if (version_compare($context->getVersion(),
            '0.0.10',
            '<')) {
            $installer = $setup;
            $installer->startSetup();

            $table = $installer->getConnection()->newTable(
                $installer->getTable('partenaire_locality')
            )
                ->addColumn(
                    'id',
                    Table::TYPE_INTEGER,
                    null,
                    ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
                    'Id'
                )
                ->addColumn(
                    'name',
                    Table::TYPE_TEXT,
                    55,
                    ['nullable' => false],
                    'Locality Name'
                )
                ->setComment(
                    'Partenaire Locality table'
                );

            $installer->getConnection()->createTable($table);

            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'locality_id',
                    [
                        'type' => Table::TYPE_INTEGER,
                        'nullable' => false,
                        'unsigned' => true,
                        'size' => '10',
                        'comment' => 'Localité du partenaire',
                        'default' => '1',
                        'after' => 'type'
                    ]
                );

            $setup
                ->getConnection()
                ->addForeignKey(
                    $setup->getFkName('partenaire',
                        'locality_id',
                        'partenaire_locality',
                        'id'),
                    'partenaire',
                    'locality_id',
                    'partenaire_locality',
                    'id',
                    Table::ACTION_CASCADE
                );

            $setup->endSetup();
        }

        if (version_compare($context->getVersion(),
            '0.0.12',
            '<')) {
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'effectif',
                    [
                        'type' => Table::TYPE_INTEGER,
                        'nullable' => true,
                        'comment' => 'Effectif',
                        'after' => 'delivery_order'
                    ]
                );

            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'montant_ticket_resto',
                    [
                        'type' => Table::TYPE_DECIMAL,
                        'nullable' => true,
                        'comment' => 'Montant ticket restaurant',
                        'after' => 'effectif',
                        'length' => '12,4',
                    ]
                );
        }


        if (version_compare($context->getVersion(),
            '0.0.13',
            '<')) {
            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'urne',
                    [
                        'type' => Table::TYPE_INTEGER,
                        'nullable' => false,
                        'comment' => 'Urne',
                        'default' => '0',
                        'after' => 'montant_ticket_resto'
                    ]
                );

            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'etagere',
                    [
                        'type' => Table::TYPE_INTEGER,
                        'nullable' => false,
                        'comment' => 'Etagere',
                        'default' => '0',
                        'after' => 'urne'
                    ]
                );

            $setup
                ->getConnection()
                ->addColumn(
                    $setup->getTable('partenaire'),
                    'frigidaire',
                    [
                        'type' => Table::TYPE_INTEGER,
                        'nullable' => false,
                        'comment' => 'Frigidaire',
                        'default' => '0',
                        'after' => 'etagere'
                    ]
                );
        }
    }
}

<?php

namespace EatLf\Partenaire\Setup;

use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

class UpgradeData implements UpgradeDataInterface
{
    protected $localities = [
        [
            'name' => 'Lille Métropole'
        ], [
            'name' => 'Paris Nord - Roissy'
        ], [
            'name' => 'Paris - La Défense'
        ],

    ];

    /**
     * {@inheritdoc}
     */
    public function upgrade(
        ModuleDataSetupInterface $setup,
        ModuleContextInterface $context
    )
    {
        $setup->startSetup();
        $connection = $setup->getConnection();

        if (version_compare($context->getVersion(),
            '0.0.11',
            '<')) {
            foreach ($this->localities as $locality) {
                $connection->insertForce(
                    $setup->getTable('partenaire_locality'),
                    $locality
                );
            }
            $setup->endSetup();
        }
    }
}
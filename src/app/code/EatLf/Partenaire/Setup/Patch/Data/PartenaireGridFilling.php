<?php
declare (strict_types=1);

namespace EatLf\Partenaire\Setup\Patch\Data;

use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use EatLf\Partenaire\Model\ResourceModel\Partenaire\CollectionFactory as PartenaireCollectionFactory;
use EatLf\Partenaire\Model\ResourceModel\PartenaireGridInterface;
use EatLf\Partenaire\Model\Partenaire;

class PartenaireGridFilling implements DataPatchInterface
{
    /**
     * @var PartenaireCollectionFactory $partenaireCollection
     */
    private $partenaireCollection;
    /**
     * @var PartenaireGridInterface $entityGrid
     */
    private $entityGrid;
    /**
     * @var State $state
     */
    private $state;

    /**
     * @param PartenaireCollectionFactory $partenaireCollection
     * @param PartenaireGridInterface $entityGrid
     * @param State $state
     */
    public function __construct(
        PartenaireCollectionFactory $partenaireCollection,
        PartenaireGridInterface     $entityGrid,
        State $state
    ) {
        $this->partenaireCollection = $partenaireCollection;
        $this->entityGrid = $entityGrid;
        $this->state = $state;
    }

    /**
     * @return PartenaireGridFilling|void
     * @throws LocalizedException
     */
    public function apply()
    {
        $this->state->setAreaCode(Area::AREA_ADMINHTML);

        /** @var Partenaire $partenaire */
        foreach ($this->partenaireCollection->create() as $partenaire) {
            $this->entityGrid->refresh($partenaire->getId());
        }
    }

    /**
     * {@inheritdoc}
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public function getAliases(): array
    {
        return [];
    }
}

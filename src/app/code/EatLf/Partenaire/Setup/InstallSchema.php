<?php

namespace EatLf\Partenaire\Setup;

use Magento\Framework\DB\Ddl\Table;
use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

class InstallSchema implements InstallSchemaInterface
{

    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        $table = $setup->getConnection()
            ->newTable('partenaire')
            ->addColumn(
                'id',
                Table::TYPE_INTEGER,
                null,
                ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
                'Id'
                )
            ->addColumn(
                'franchise_id',
                Table::TYPE_INTEGER,
                null,
                ['unsigned' => true, 'nullable' => false],
                'Id du franchisé gérant le partenaire'
            )
            ->addColumn(
                'printer_id',
                Table::TYPE_INTEGER,
                null,
                ['unsigned' => true, 'nullable' => false],
                "Id de l'imprimante liée"
            )
            ->addColumn(
                'is_active',
                Table::TYPE_SMALLINT,
                null,
                ['unsigned' => true, 'nullable' => false, 'default' => 0],
                'Activation du partenaire'
            )
            ->addColumn(
                'company',
                Table::TYPE_TEXT,
                null,
                ['nullable' => false],
                'Nom du partenaire'
            )
            ->addColumn(
                'parent_company',
                Table::TYPE_TEXT,
                null,
                ['nullable' => false],
                'Nom de la société mère'
            )
            ->addColumn(
                'address',
                Table::TYPE_TEXT,
                null,
                ['nullable' => false],
                'Adresse du partenaire'
            )
            ->addColumn(
                'city',
                Table::TYPE_TEXT,
                null,
                ['nullable' => false],
                'Ville du partenaire'
            )
            ->addColumn(
                'postcode',
                Table::TYPE_INTEGER,
                null,
                ['nullable' => false],
                'Code postal du partenaire'
            )
            ->addColumn(
                'shipping_slot',
                Table::TYPE_TEXT,
                null,
                [],
                'Créneau de livraison du partenaire'
            )
            ->addColumn(
                'last_order_time',
                Table::TYPE_INTEGER, // Magento ne connait pas le type Mysql TIME
                null,
                ['nullable' => false],
                'Heure de fin de commande'
            )
            ->addColumn(
                'shipping_comment',
                Table::TYPE_TEXT,
                null,
                [],
                'Indication de livraison'
            )
            ->addColumn(
                'logo',
                Table::TYPE_TEXT,
                null,
                [],
                'Logo du partenaire'
            )
            ->addForeignKey(
                $setup->getFkName('partenaire', 'franchise_id', 'franchise', 'franchise_id'),
                'franchise_id',
                $setup->getTable('franchise'),
                'franchise_id',
                Table::ACTION_NO_ACTION
            )
            ->addForeignKey(
                $setup->getFkName('partenaire', 'printer_id', 'printer', 'id'),
                'printer_id',
                $setup->getTable('printer'),
                'id',
                Table::ACTION_NO_ACTION
            );

        $setup->getConnection()->createTable($table);

        $setup->endSetup();
    }
}
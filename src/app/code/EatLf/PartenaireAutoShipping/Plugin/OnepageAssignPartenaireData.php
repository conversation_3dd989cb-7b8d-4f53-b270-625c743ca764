<?php

namespace EatLf\PartenaireAutoShipping\Plugin;

use EatLf\PartenaireAutoShipping\Model\CartManagement;
use Magento\Checkout\Model\Type\Onepage;
use Magento\Quote\Api\CartRepositoryInterface;

class OnepageAssignPartenaireData
{
    /**
     * @var CartManagement
     */
    private $cartManagement;
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private CartRepositoryInterface $cartRepository;

    public function __construct(
        CartManagement $cartManagement,
        CartRepositoryInterface $cartRepository
    )
    {
        $this->cartManagement = $cartManagement;
        $this->cartRepository = $cartRepository;
    }

    public function afterInitCheckout(Onepage $subject, Onepage $result)
    {
        $quote = $subject->getQuote();

        $this->cartManagement->populateShippingAddress(
            $quote
        );

        $this->cartRepository->save($quote);

        return $result;
    }
}
<?php

namespace EatLf\PartenaireAutoShipping\Model;

use EatLf\Carrier\Model\Delivery;
use EatLf\Partenaire\Api\PartenaireRepositoryInterface;
use EatLf\Sales\Model\CustomerSales;
use EatLf\Shipping\Helper\Shipping;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\Data\CartInterface;

class CartManagement
{
    /**
     * @var Shipping
     */
    private $shippingHelper;
    /**
     * @var PartenaireRepositoryInterface
     */
    private $partenaireRepository;
    /**
     * @var \EatLf\Sales\Model\CustomerSales
     */
    private CustomerSales $customerSales;

    public function __construct(
        Shipping $shippingHelper,
        PartenaireRepositoryInterface $partenaireRepository,
        CustomerSales $customerSales
    ) {
        $this->shippingHelper = $shippingHelper;
        $this->partenaireRepository = $partenaireRepository;
        $this->customerSales = $customerSales;
    }

    /**
     * Copies the information from the selected partenaire to the shipping address
     *
     * @param CartInterface $cart
     *
     * @return CartManagement
     */
    public function populateShippingAddress(CartInterface $cart): CartManagement
    {
        $partenaireId = $this->shippingHelper
            ->getShippingDatas($cart)
            ->getExtensionAttributes()
            ->getPartenaireId();

        try {
            $partenaire = $this->partenaireRepository->getById($partenaireId);
        } catch (NoSuchEntityException $e) {
            return $this;
        }

        $shippingAddress = $cart->getShippingAddress();

        $shippingAddress
            ->setCompany($partenaire->getCompany())
            ->setStreet($partenaire->getAddress())
            ->setPostcode($partenaire->getPostcode())
            ->setCity($partenaire->getCity())
            ->setCountryId($partenaire->getCountryId())
            ->setShippingMethod(Delivery::CODE . '_' . Delivery::CODE)
            ->setCollectShippingRates(true)
            ->setCustomerAddressId(null)
            ->setSaveInAddressBook('0');

        $customer = $cart->getCustomer();

        if ($customer->getId()) {
            $shippingAddress
                ->setTelephone($customer->getCustomAttribute('telephone')->getValue())
                ->setFirstname($customer->getFirstname())
                ->setLastname($customer->getLastname());
        }

        return $this;
    }

    public function populateBillingAddressFromShipping(CartInterface $cart): CartManagement
    {
        $shippingAddress = $cart->getShippingAddress();
        $billingAddress = $cart->getBillingAddress();

        $billingAddress
            ->setFirstname($shippingAddress->getFirstname())
            ->setLastname($shippingAddress->getLastname())
            ->setTelephone($shippingAddress->getTelephone())
            ->setCompany($shippingAddress->getCompany())
            ->setStreet($shippingAddress->getStreet())
            ->setPostcode($shippingAddress->getPostcode())
            ->setCity($shippingAddress->getCity())
            ->setCountryId($shippingAddress->getCountryId());

        return $this;
    }
}

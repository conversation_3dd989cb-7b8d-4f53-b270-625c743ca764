<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Address\Book $block */
?>
<div class="block block-addresses-default">
    <div class="block-content">
        <?php if ($_pAddsses = $block->getDefaultBilling()): ?>
            <div class="box box-address-billing">
                <h3 class="box-title">
                    <span><?= $block->escapeHtml(__('Default Billing Address')) ?></span>
                </h3>
                <div class="box-content">
                    <address>
                        <?= $block->getAddressHtml($block->getAddressById($_pAddsses)) ?>
                    </address>
                </div>
                <div class="box-actions">
                    <a class="action edit" href="<?= $block->escapeUrl($block->getAddressEditUrl($_pAddsses)) ?>">
                        <span><?= $block->escapeHtml(__('Change Billing Address')) ?></span>
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="box box-billing-address">
                <h3 class="box-title"><span><?= $block->escapeHtml(__('Default Billing Address')) ?></span></h3>
                <div class="box-content">
                    <p><?= $block->escapeHtml(__('You have no default billing address in your address book.')) ?></p>
                </div>
            </div>
        <?php endif ?>
    </div>
</div>

<div class="block block-addresses-list">
   
    <div class="block-content">
        <?php if ($_pAddsses = $block->getAdditionalAddresses()): ?>
            <ol class="items addresses">
                <?php foreach ($_pAddsses as $_address): ?>
                    <li class="item">
                        <address>
                            <?= $block->getAddressHtml($_address) ?><br />
                        </address>
                        <div class="item actions">
                            <a class="action edit" href="<?= $block->escapeUrl($block->getUrl('customer/address/edit', ['id' => $_address->getId()])) ?>"><span><?= $block->escapeHtml(__('Edit Address')) ?></span></a>
                            <a class="action delete" href="#" role="delete-address" data-address="<?= $block->escapeHtmlAttr($_address->getId()) ?>"><span><?= $block->escapeHtml(__('Delete Address')) ?></span></a>
                        </div>
                    </li>
                <?php endforeach; ?>
            </ol>
        <?php else: ?>
            <p class="empty"><?= $block->escapeHtml(__('You have no other address entries in your address book.')) ?></p>
        <?php endif ?>
    </div>
</div>

<div class="actions-toolbar">
    <div class="primary">
        <button type="button" role="add-address" title="<?= $block->escapeHtmlAttr(__('Add New Address')) ?>" class="btn btn-8h action primary add"><span><?= $block->escapeHtml(__('Add New Address')) ?></span></button>
    </div> 
</div>
<script type="text/x-magento-init">
    {
        ".page-main": {
            "address": {
                "deleteAddress": "li.item a[role='delete-address']",
                "deleteUrlPrefix": "<?= $block->escapeJs($block->escapeUrl($block->getDeleteUrl())) ?>id/",
                "addAddress": "button[role='add-address']",
                "addAddressLocation": "<?= $block->escapeJs($block->escapeUrl($block->getAddAddressUrl())) ?>"
            }
        }
    }
</script>

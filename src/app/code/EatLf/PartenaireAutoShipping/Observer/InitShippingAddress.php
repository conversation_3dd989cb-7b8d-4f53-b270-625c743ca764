<?php

namespace EatLf\PartenaireAutoShipping\Observer;

use EatLf\PartenaireAutoShipping\Model\CartManagement;
use EatLf\Partenaire\Api\Data\PartenaireInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Quote\Api\Data\CartInterface;

class InitShippingAddress implements ObserverInterface
{
    /**
     * @var CartManagement
     */
    private $cartManagement;

    public function __construct(CartManagement $cartManagement)
    {
        $this->cartManagement = $cartManagement;
    }

    /**
     * Automatically sets the shipping method
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        /** @var CartInterface $quote */
        $quote = $observer->getData('quote');

        $this->cartManagement->populateShippingAddress($quote);
    }
}
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="web" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1"
                 showInStore="1">
            <group id="shipping" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Libellés shipping bar</label>
                <field id="shipping_bar_title" translate="label" type="text" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Step 0 - Titre</label>
                </field>
                <field id="shipping_bar_restaurant_delivery" translate="label" type="text" sortOrder="20"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Step 0 - livraison restaurants</label>
                </field>
                <field id="shipping_bar_partenaire_delivery" translate="label" type="text" sortOrder="30"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Step 0 - livraison partenaires</label>
                </field>
                <field id="shipping_bar_partenaire_delivery_step1_title" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Step 1 - livraison partenaire - titre</label>
                </field>
                <field id="shipping_bar_partenaire_delivery_step1_placeholder" translate="label" type="text" sortOrder="51" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Step 1 - livraison partenaire - placeholder</label>
                </field>
                <field id="shipping_bar_partenaire_delivery_step2_title" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Step 2 - livraison partenaire - titre</label>
                </field>
            </group>
            <group id="shipping_bar_param" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Paramètres shipping bar</label>
                <field id="locality_enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Activer la sélection de la localité</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>


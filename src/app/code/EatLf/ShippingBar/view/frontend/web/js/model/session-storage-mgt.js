define([
    'knockout'
], function (
    ko
) {
    'use strict';

    var TYPE_STORAGE_KEY= 'eatlf-type-id';
    var LOCALITY_STORAGE_KEY= 'eatlf-locality-id';
    var typeObservable =  ko.observable(parseInt(sessionStorage.getItem(TYPE_STORAGE_KEY)));
    var localityObservable =  ko.observable(parseInt(sessionStorage.getItem(LOCALITY_STORAGE_KEY)));

    var shippingBarSessionStorage = {
        type : typeObservable,
        locality : localityObservable,
        TYPE_STORAGE_KEY: TYPE_STORAGE_KEY,
        LOCALITY_STORAGE_KEY: LOCALITY_STORAGE_KEY,

        setType: function (type_id) {
            sessionStorage.setItem(this.TYPE_STORAGE_KEY, type_id);
            this.type(type_id);
        },

        setLocality: function (locality_id) {
            sessionStorage.setItem(this.LOCALITY_STORAGE_KEY, locality_id);
            this.locality(locality_id);
        },

        clear: function () {
            sessionStorage.removeItem(this.LOCALITY_STORAGE_KEY);
            sessionStorage.removeItem(this.TYPE_STORAGE_KEY);
        }
    };

    return shippingBarSessionStorage;

});
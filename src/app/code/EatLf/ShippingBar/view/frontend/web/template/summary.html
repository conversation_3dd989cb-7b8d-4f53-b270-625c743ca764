<div class="step3 ss2">
    <div class="shippingbar-tooltips" data-bind="event: {mouseover: showTooltip, mouseout: hideTooltip, click: showTooltip }"><i class="fa fa-question-circle"></i></div>
    <div id="shippingbarTooltipPopin" class="resetPopin shippingPopin" style="display: none;" data-bind="visible: tooltipVisible">
        <span data-bind="text: tooltiptext"></span>
        <button class="close action-close" data-bind="click: hideTooltip" data-role="closeBtn" type="button"></button>
    </div>

    <div class="smallcart_wrapper" data-bind="afterRender: moveMinicart"></div>

    <div class="shipping_summary" >
        <div class="shipping_summary_left">
            <div class="split">
                <span class="text" data-bind="text: selectedCompany"></span>
                <span id="_place" class="place_after" data-bind="click: showLeftPopin"></span>
            </div>
            <div class="split">
                <span class="text" data-bind="text: selectedDate"></span>
                <span id="_place" class="place_after" data-bind="click: showLeftPopin"></span>
            </div>
        </div>
        <div class="shipping_summary_right">
            <div class="split">
            <span class="text text-uppercase"  data-bind="i18n: 'Order before'"></span> <span class="summary_date" data-bind="text: maxOrderTime"></span>
                </div>
            <div class="split">
                <span class="text text-uppercase margin-10" data-bind="i18n: 'Delivery between'" ></span> <span class="summary_date" data-bind="text: shippingSlot"></span>
            </div>
        </div>
    </div>

    <div id="resetPopin"
         class="resetPopin"
         style="display: none;"
         data-bind="visible: popinVisible, css: popinClass">

        <button class="close action-close"
                data-bind="click: hideResetPopin"
                data-role="closeBtn"
                type="button"></button>

        <span data-bind="i18n:'Attention, si vous souhaitez modifier votre date ou adresse de livraison, cela supprimera totalement votre panier'"></span>

        <button class="btn"
                data-bind="i18n:'Modify shipping',
                           click:onResetShipping"></button>
    </div>
</div>

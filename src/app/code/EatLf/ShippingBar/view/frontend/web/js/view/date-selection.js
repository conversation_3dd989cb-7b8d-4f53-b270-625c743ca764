define([
    'jquery',
    'ko',
    'EatLf_ShippingBar/js/model/session-storage-mgt',
    'moment',
    'uiComponent',
    'mage/translate',
    'EatLf_Shipping/js/model/shipping-service',
    'EatLf_Shipping/js/model/shipping'
], function (
    $,
    ko,
    shippingBarSessionStorage,
    moment,
    Component,
    $t,
    shippingService,
    shippingModel
) {
   'use strict';

   return Component.extend({
       defaults: {
           template: 'EatLf_ShippingBar/date-selection',
           isTodayAvailable: false,
           selectedDate: null,
           todayValue: moment(new Date()).format('YYYY-MM-DD'),
           otherDates: [],
           adviceText: '',
           title: '',
           loaderVisible: true,
           cssClass: '',
           links: {
               loaderVisible: 'shippingBar:loaderVisible'
           },
           listens: {
               selectedDate: 'onSelectDate'
           }
       },

       initialize: function () {
           this._super();

           ko.computed(this.onShippingDataChange.bind(this));

           return this;
       },

       initObservable: function () {
           this._super()
               .observe([
                   'isTodayAvailable',
                   'otherDates',
                   'loaderVisible',
                   'cssClass',
                   'selectedDate',
                   'adviceText'
               ]);

           this.title =
               ko.pureComputed(function () {
                   switch (shippingBarSessionStorage.type()) {
                       case 1 :
                           return SbPartenaireDeliveryS2Title;
                           break;
                       case 2 :
                           return $t('Restaurant');
                           break;
                   }
                   return $t('Point of delivery type');
               }.bind(this));

           return this;
       },

       dateFormat: function (date) {
           return moment(date).format('ddd DD MMMM');
       },

       onShippingDataChange: function () {
           var shippingData = shippingModel.data();
           var formattedDate = moment(shippingData.maxOrderTime, 'Hmm').format('HH[H]mm');

           this.isTodayAvailable(shippingData.isTodayAvailable);
           this.otherDates(shippingData.otherDates);

           this.adviceText(
               $.mage.__('Commandez avant <span class="hilite">%1</span> pour une livraison entre %2.')
                   .replace('%1', formattedDate)
                   .replace('%2', shippingData.shippingSlot)
           );

           if (shippingData.selectedDate) {
               this.cssClass('OK');
               $(function () {
                   $('.smallcart_wrapper').replaceWith($('.smallcart'));
               });
               return;
           }

           if (!shippingData.selectedDate && shippingData.selectedPartenaireId) {
               this.cssClass('');
               this.loaderVisible(false);
           }

           if (
               shippingData.selectedPartenaireId !== null &&
               shippingData.selectedDate === null &&
               shippingData.isTodayAvailable === false &&
               shippingData.otherDates.length === 0
           ) {
               this.cssClass('KO');
           }
       },

       onSelectDate: function (selectedValue) {
           if (selectedValue === null) {
               return;
           }

           // Small timeout to show the selection css animation
           setTimeout(function () {
               shippingService.selectDate(selectedValue);
               this.loaderVisible(true);
               this.selectedDate(null);
               $(document).trigger( "timeslotSelected" );
           }.bind(this), 800);
       }
   });
});

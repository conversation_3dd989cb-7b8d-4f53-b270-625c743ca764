define([
    'knockout',
    'uiComponent',
    'EatLf_Shipping/js/model/shipping',
    'EatLf_Shipping/js/model/shipping-service',
    'mage/translate',
    'EatLf_ShippingBar/js/model/session-storage-mgt'
], function (
    ko,
    Component,
    shippingModel,
    shippingService,
    $t,
    shippingBarSessionStorage
) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'EatLf_ShippingBar/coming-soon',
            loaderVisible: true,
            title: '',
            isVisible: false,
            cssClass: '',
            links: {
                loaderVisible: 'shippingBar:loaderVisible'
            },
        },

        initialize: function () {
            this._super();

            ko.computed(this.onShippingDataChange.bind(this));

            return this;
        },

        initObservable: function () {
            this._super()
                .observe(['isVisible', 'loaderVisible', 'cssClass']);

            this.title =
                ko.pureComputed(function () {
                    switch (shippingBarSessionStorage.type()) {
                        case 1 :
                            return $t('Company');
                            break;
                        case 2 :
                            return $t('Restaurant');
                            break;
                    }
                    return $t('Point of delivery type');
                }.bind(this));

            return this;
        },

        onResetClicked: function () {
            sessionStorage.removeItem(shippingBarSessionStorage.TYPE_STORAGE_KEY);
            sessionStorage.removeItem(shippingBarSessionStorage.LOCALITY_STORAGE_KEY);
            shippingService.resetSelection();
        },

        onShippingDataChange: function () {
            var shippingData = shippingModel.data();

            if (
                shippingData.selectedPartenaireId !== null &&
                shippingData.selectedDate === null &&
                shippingData.isTodayAvailable === false &&
                shippingData.otherDates.length === 0
            ) {
                this.isVisible(true);
                return;
            }

            this.isVisible(false);
        }
    });
});
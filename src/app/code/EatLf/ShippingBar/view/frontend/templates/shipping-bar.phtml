<?php
/** @var \EatLf\ShippingBar\Block\ShippingNav $block */
?>
<div id="shippingBar" data-bind="scope: 'shippingBar'" data-block="shippingNav" class="delivery">
    <div class="loader" data-bind="visible: loaderVisible">
        <div><div></div><div></div><div></div><div></div></div>
    </div>

    <script>
        window.SbTitle = '<?php echo $block->getShippingBarTitle(); ?>';
        window.SbPartenaireDeliveryS1Title = '<?php echo $block->getShippingBarPartenaireDeliveyStep1Title(); ?>';
        window.SbPartenaireDeliveryS2Title = '<?php echo $block->getShippingBarPartenaireDeliveyStep2Title(); ?>';
        window.SbPartenaireDeliveryS1Placeholder = '<?php echo $block->getShippingBarPartenaireDeliveyStep1Placeholder(); ?>';
        window.sbLocalityEnabled = '<?php echo $block->isLocalityEnabled(); ?>';
        window.sbToolTipText = '<?php echo $block->getShippingBarToolTipText(); ?>';
        window.currentRoute = '<?php echo $block->getRequest()->getFullActionName(); ?>';
    </script>

    <!-- ko foreach: getRegion('shipping-bar-content') -->
    <!-- ko template: getTemplate() --><!-- /ko -->
    <!--/ko-->
</div>

<script type="text/x-magento-init">
    {
        "#shippingBar": {
            "Magento_Ui/js/core/app": <?= /* @escapeNotVerified */
    $block->getJsLayout() ?>
        }
    }


</script>

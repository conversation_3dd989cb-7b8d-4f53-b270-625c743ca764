<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="page_calendar"/>

    <body>
        <referenceBlock name="header.container.custom">
            <block class="EatLf\ShippingBar\Block\ShippingNav"
                   name="shippingnav"
                   after="-"
                   template="EatLf_ShippingBar::shipping-bar.phtml">
                <arguments>
                    <argument name="jsLayout" xsi:type="array">
                        <item name="components" xsi:type="array">
                            <item name="shippingBar" xsi:type="array">
                                <item name="component" xsi:type="string">EatLf_ShippingBar/js/view/shipping-bar</item>
                                <item name="children" xsi:type="array">
                                    <item name="typeSelection" xsi:type="array">
                                        <item name="component" xsi:type="string">EatLf_ShippingBar/js/view/type-selection</item>
                                        <item name="displayArea" xsi:type="string">shipping-bar-content</item>
                                    </item>
                                    <item name="partenaireSelection" xsi:type="array">
                                        <item name="component" xsi:type="string">EatLf_ShippingBar/js/view/partenaire-selection</item>
                                        <item name="displayArea" xsi:type="string">shipping-bar-content</item>
                                    </item>
                                    <item name="dateSelection" xsi:type="array">
                                        <item name="component" xsi:type="string">EatLf_ShippingBar/js/view/date-selection</item>
                                        <item name="displayArea" xsi:type="string">shipping-bar-content</item>
                                    </item>
                                    <item name="summary" xsi:type="array">
                                        <item name="component" xsi:type="string">EatLf_ShippingBar/js/view/summary</item>
                                        <item name="displayArea" xsi:type="string">shipping-bar-content</item>
                                    </item>
                                    <item name="comingSoon" xsi:type="array">
                                        <item name="component" xsi:type="string">EatLf_ShippingBar/js/view/coming-soon</item>
                                        <item name="displayArea" xsi:type="string">shipping-bar-content</item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>

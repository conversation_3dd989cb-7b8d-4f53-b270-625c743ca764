<?php
declare(strict_types=1);

namespace EatLf\BapAdminUi\Ui\Component\Bap\Listing\Columns;

use EatLf\Bap\Api\Data\BapStatusInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class Actions extends Column
{
    private UrlInterface $url;

    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        UrlInterface $url,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);

        $this->url = $url;
    }

    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as & $item) {
                if ($this->canShowDownloadAction($item)) {
                    $item[$this->getData('name')] = [
                        'download_pdf' => [
                            'href' => $this->url->getUrl(
                                'bap/bap/report',
                                ['bap_id' => $item['bap_id']]
                            ),
                            'label' => __('Download'),
                        ],
                    ];
                }
            }
        }

        return $dataSource;
    }

    private function canShowDownloadAction($item): bool
    {
        return isset($item['bap_id']) &&
            ($item['orig_status'] === BapStatusInterface::STATUS_WAITING_FOR_PAYMENT ||
                $item['orig_status'] === BapStatusInterface::STATUS_PAID);
    }
}

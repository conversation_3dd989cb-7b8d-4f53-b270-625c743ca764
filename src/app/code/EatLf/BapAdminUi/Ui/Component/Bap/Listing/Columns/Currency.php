<?php
declare(strict_types=1);

namespace EatLf\BapAdminUi\Ui\Component\Bap\Listing\Columns;

use Magento\Framework\Pricing\Helper\Data;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class Currency extends Column
{
    private Data $price;

    public function __construct(
        Data $price,
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);

        $this->price = $price;
    }


    public function prepareDataSource(array $dataSource): array
    {
        foreach ($dataSource['data']['items'] as &$item) {
            $item['orig_' . $this->getName()] = $item[$this->getName()];
            $item[$this->getName()] = $this->price->currency($item[$this->getName()], true, false);
        }

        return $dataSource;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\BapAdminUi\Ui\Component\Bap\Listing\Filter;

use Magento\Framework\Data\OptionSourceInterface;

class Month implements OptionSourceInterface
{
    public function toOptionArray(): array
    {
        $result = [];
        $months = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];

        foreach ($months as $month) {
            $result[] = [
                'value' => $month,
                'label' => str_pad($month, 2, '0', STR_PAD_LEFT),
            ];
        }

        return $result;
    }
}

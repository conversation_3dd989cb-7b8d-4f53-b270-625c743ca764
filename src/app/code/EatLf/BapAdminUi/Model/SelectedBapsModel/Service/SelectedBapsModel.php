<?php
declare(strict_types=1);

namespace EatLf\BapAdminUi\Model\SelectedBapsModel\Service;

class SelectedBapsModel
{
    private array $bapIncrementIds;

    private array $bapIds;

    private array $modeReglement;

    private float $totalTtc;

    private float $totalHt;

    public function __construct(
        array $bapIncrementIds,
        array $bapIds,
        array $modeReglement,
        float $totalTtc,
        float $totalHt
    ) {
        $this->bapIncrementIds = $bapIncrementIds;
        $this->bapIds = $bapIds;
        $this->modeReglement = $modeReglement;
        $this->totalTtc = $totalTtc;
        $this->totalHt = $totalHt;
    }

    public function getBapIncrementIds(): array
    {
        return $this->bapIncrementIds;
    }

    public function getBapIds(): array
    {
        return $this->bapIds;
    }

    public function getModesReglement(): array
    {
        return $this->modeReglement;
    }

    public function getTotalTtc(): float
    {
        return $this->totalTtc;
    }

    public function getTotalHt(): float
    {
        return $this->totalHt;
    }
}

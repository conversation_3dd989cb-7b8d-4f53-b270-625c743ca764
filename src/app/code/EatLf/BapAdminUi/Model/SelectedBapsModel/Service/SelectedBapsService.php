<?php
declare(strict_types=1);

namespace EatLf\BapAdminUi\Model\SelectedBapsModel\Service;

use EatLf\Bap\Api\Data\BapPaymentMethodInterface;

class SelectedBapsService implements SelectedBapsServiceInterface
{
    private BapDataGatewayInterface $bapDataGateway;

    public function __construct(BapDataGatewayInterface $bapDataGateway)
    {
        $this->bapDataGateway = $bapDataGateway;
    }

    public function getSelectedBapsModel(array $bapIdList): SelectedBapsModel
    {
        [$incrementIds, $totalHt, $totalTtc] = $this->prepareBapsData($bapIdList);

        return new SelectedBapsModel(
            $incrementIds,
            $bapIdList,
            BapPaymentMethodInterface::BAP_PAYMENT_METHODS,
            $totalTtc,
            $totalHt
        );
    }

    private function prepareBapsData(array $bapIdList): array
    {
        $bapList = $this->fetchBapsData($bapIdList);

        $incrementIds = [];
        $totalHt = 0;
        $totalTtc = 0;

        foreach ($bapList as $bap) {
            $incrementIds[] = $bap->getIncrementId();
            $totalHt += $bap->getTotalExclTax();
            $totalTtc += $bap->getTotalInclTax();
        }

        return [$incrementIds, $totalHt, $totalTtc];
    }

    private function fetchBapsData(array $bapIdList): array
    {
        return $this->bapDataGateway->getBapsData($bapIdList);
    }
}

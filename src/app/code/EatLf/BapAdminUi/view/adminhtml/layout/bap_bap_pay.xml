<?xml version="1.0" encoding="UTF-8"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="editor"/>
    <body>
        <referenceContainer name="content">
            <block class="EatLf\BapAdminUi\Block\Adminhtml\Bap\Pay"
                   name="eatlfbap_pay"
                   template="EatLf_BapAdminUi::selected-baps/pay.phtml">
                <arguments>
                    <argument xsi:type="object" name="view_model">
                        EatLf\BapAdminUi\ViewModel\PaySelectedBaps
                    </argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>

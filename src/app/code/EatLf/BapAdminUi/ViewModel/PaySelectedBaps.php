<?php
declare(strict_types=1);

namespace EatLf\BapAdminUi\ViewModel;

use EatLf\BapAdminUi\Model\SelectedBapsModel\Service\SelectedBapsModel;
use EatLf\BapAdminUi\Model\SelectedBapsModel\Service\SelectedBapsServiceInterface;
use Magento\Framework\Pricing\Helper\Data;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;

/**
 * View model for the "Pay selected baps" block.
 *
 * Fetches data for the baps the user selected on the admin grid and formats them.
 */
class PaySelectedBaps implements ArgumentInterface
{
    private const SAVE_ACTION = 'bap/bap/payPost';

    public const FIELD_BAP_IDS = 'bap_ids';

    public const FIELD_PAYMENT_METHOD = 'payment-method';

    public const FIELD_PAYMENT_DATE = 'payment-date';

    public const FIELD_PAYMENT_NUMBER = 'payment-number';

    private SelectedBapsServiceInterface $selectedBapsService;

    private SelectedBapsIdsProvider $selectedBapsIdsProvider;

    private Data $priceHelper;

    private UrlInterface $url;

    // Lazily initialized selected baps model data.
    private $selectedBapsModel;

    public function __construct(
        SelectedBapsServiceInterface $selectedBapsService,
        SelectedBapsIdsProvider $selectedBapsIdsProvider,
        Data $priceHelper,
        UrlInterface $url
    ) {
        $this->selectedBapsService = $selectedBapsService;
        $this->selectedBapsIdsProvider = $selectedBapsIdsProvider;
        $this->priceHelper = $priceHelper;
        $this->url = $url;
    }

    public function getBapIncrementIds(): string
    {
        $incrementIds = $this->getSelectedBapsModel()->getBapIncrementIds();

        asort($incrementIds);

        return join(' | ', $incrementIds);
    }

    public function getBapIds(): array
    {
        return $this->getSelectedBapsModel()->getBapIds();
    }

    public function getTotalInclTax(): string
    {
        $priceInclTax = $this->getSelectedBapsModel()->getTotalTtc();

        return $this->formatCurrency($priceInclTax);
    }

    public function getTotalExclTax(): string
    {
        $priceExclTax = $this->getSelectedBapsModel()->getTotalHt();

        return $this->formatCurrency($priceExclTax);
    }

    public function getModesReglement(): array
    {
        return $this->getSelectedBapsModel()->getModesReglement();
    }

    public function getFormActionUrl(): string
    {
        return $this->url->getUrl(self::SAVE_ACTION);
    }

    private function formatCurrency(float $amount): string
    {
        return $this->priceHelper->currency($amount, true, false);
    }

    private function getSelectedBapsModel(): SelectedBapsModel
    {
        if ($this->selectedBapsModel === null) {
            $this->setSelectedBapsModel();
        }

        return $this->selectedBapsModel;
    }

    private function setSelectedBapsModel(): void
    {
        $bapIdList = $this->selectedBapsIdsProvider->getSelectedBapsIds();

        $this->selectedBapsModel = $this->selectedBapsService->getSelectedBapsModel($bapIdList);
    }
}

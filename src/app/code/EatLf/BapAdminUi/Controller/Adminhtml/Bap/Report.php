<?php
declare(strict_types=1);

namespace EatLf\BapAdminUi\Controller\Adminhtml\Bap;

use EatLf\BapAdminUi\Model\BapAccessFilter\Service\BapAccessFilter;
use EatLf\BapReport\Api\GenerateReportsInterface;
use EatLf\Partenaire\Model\Partenaire\Access;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\App\ResponseInterface;

class Report extends Action
{
    private const BAP_ID_PARAM = 'bap_id';

    public const ADMIN_RESOURCE = Access::ACCESS_LISTING;

    private FileFactory $fileFactory;

    private GenerateReportsInterface $generateReports;

    private RequestInterface $request;

    private BapAccessFilter $bapAccessFilter;

    public function __construct(
        Context $context,
        FileFactory $fileFactory,
        GenerateReportsInterface $generateReports,
        RequestInterface $request,
        BapAccessFilter $bapAccessFilter
    ) {
        parent::__construct($context);

        $this->fileFactory = $fileFactory;
        $this->generateReports = $generateReports;
        $this->request = $request;
        $this->bapAccessFilter = $bapAccessFilter;
    }

    public function execute()
    {
        $bapId = $this->getBapId();

        $this->bapAccessFilter->checkBapAccess($bapId);

        $zipFilePath = $this->generateReports->execute((int)$bapId);

        return $this->prepareReportDownloadResult($zipFilePath);
    }

    private function getBapId(): string
    {
        return $this->request->getParam(self::BAP_ID_PARAM);
    }

    private function prepareReportDownloadResult(string $reportPath): ResponseInterface
    {
        $content = [
            'type' => 'filename',
            'value' => $reportPath,
            'rm' => 1,
        ];

        // phpcs:ignore Magento2.Functions.DiscouragedFunction
        return $this->fileFactory->create(basename($reportPath), $content);
    }
}

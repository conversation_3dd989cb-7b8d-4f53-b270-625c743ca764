<?php

namespace EatLf\SyncLfDatas\Model\Ws\Product;

use Laminas\Http\Request;
use Magento\Payment\Model\Method\Logger;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\Json\Helper\Data;
use EatLf\SyncLfDatas\Model\Config;
use \DateInterval;

/**
 * Model for ws call
 */
class Client
{
    public const PAGE_SIZE = 1800;

    public const ALL_PAGE_SIZE = 10000;

    private TimezoneInterface $timezone;

    protected Logger $logger;

    protected Config $config;

    protected Data $jsonHelper;

    protected $clientConfig = [
        'maxredirects' => 5,
        'timeout'      => 30
    ];

    protected $tokenId;

    protected $endPoints = [
        'token'              => '/rest/V1/integration/admin/token',
        'products'           => '/rest/V1/products',
        'franchise_products' => '/rest/V1/franchise/products',
        'gallery'            => '/rest/V1/products/%sku%/media',
    ];

    public function __construct(
        TimezoneInterface $timezone,
        Logger            $logger,
        Config            $config,
        Data              $jsonHelper
    )
    {
        $this->timezone = $timezone;
        $this->logger = $logger;
        $this->config = $config;
        $this->jsonHelper = $jsonHelper;
    }

    protected function getEndpoint($endPointName): string
    {
        if (!array_key_exists($endPointName, $this->endPoints)) {
            throw new \Exception("EndPoint doesn't exist : " . $endPointName);
        }

        return $this->config->hostname() . $this->endPoints[$endPointName];
    }

    protected function getTokenId(): string
    {
        if ($this->tokenId == null) {
            $client = new \Laminas\Http\Client();

            $client->setUri($this->getEndpoint('token'));
            $client->setOptions($this->clientConfig);
            $client->setMethod(Request::METHOD_POST);
            $client->setParameterPost([
                'username' => $this->config->username(),
                'password' => $this->config->password()
            ]);
            $client->setHeaders(['Accept' => 'application/json',]);

            $client->setEncType(\Laminas\Http\Client::ENC_FORMDATA);

            try {
                $response = $client->send();

                if (!$response->isSuccess()) {
                    throw new \Exception('Invalid response received');
                }

                $this->tokenId = trim($response->getBody(), '"');
            } finally {
                $this->logger->debug(
                    [
                        'result' => $this->tokenId
                    ]
                );
            }

        }

        return $this->tokenId;
    }

    public function getUpdatedProductsDatas(int $nbHour)
    {
        $extractDatetime = $this->timezone->date();
        $extractDatetime->sub(new DateInterval("PT{$nbHour}H"));

        $client = new \Laminas\Http\Client();
        $client->setUri($this->getEndpoint('products'));
        $client->setOptions($this->clientConfig);
        $client->setMethod(Request::METHOD_GET);
        $client->setHeaders(
            [
                'Accept'        => 'application/json',
                'Content-Type'  => 'application/json',
                'Authorization' => 'Bearer ' . $this->getTokenId()
            ]
        );

        $client->setEncType(\Laminas\Http\Client::ENC_FORMDATA);

        $parameters = [
            'searchCriteria' => [
                'filterGroups' => [
                    [
                        'filters' => [
                            [
                                'field'         => 'updated_at',
                                'value'         => $extractDatetime->format('Y-m-d H:i:s'),
                                'conditionType' => 'gt',
                            ],
                        ],
                    ],
                    [
                        'filters' => [
                            [
                                'field'         => 'type_id',
                                'value'         => 'simple',
                                'conditionType' => 'eq',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $httpParameters = ['searchCriteria[pageSize]' => urlencode(self::PAGE_SIZE)];

        $filterGroupCpt = 0;
        foreach ($parameters['searchCriteria'] as $searchCriteriaLabel => $filterGroups) {
            foreach ($filterGroups as $filters) {
                $filterCpt = 0;
                foreach ($filters as $filter) {
                    foreach ($filter as $filterContent) {
                        foreach ($filterContent as $fieldName => $value) {
                            $httpParameters["searchCriteria[{$searchCriteriaLabel}][{$filterGroupCpt}][filters][{$filterCpt}][{$fieldName}]"] = urlencode($value);
                        }
                        $filterCpt++;
                    }
                }

                $filterGroupCpt++;
            }
        }

        $client->setParameterGet($httpParameters);

        try {
            $response = $client->send();

            if (!$response->isSuccess()) {
                throw new \Exception('Invalid response received');
            }

            $images = $this->jsonHelper->jsonDecode($response->getBody());

        } finally {
            $this->logger->debug(['result' => $this->getTokenId()]);
        }

        return $images;
    }

    public function getProductGallery($sku)
    {
        $client = new \Laminas\Http\Client();
        $client->setUri(str_replace('%sku%', $sku, $this->getEndpoint('gallery')));
        $client->setOptions($this->clientConfig);
        $client->setMethod(Request::METHOD_GET);
        $client->setHeaders(
            [
                'Accept'        => 'application/json',
                'Authorization' => 'Bearer ' . $this->getTokenId()
            ]
        );

        $client->setEncType(\Laminas\Http\Client::ENC_FORMDATA);

        try {
            $response = $client->send();
            $products = $this->jsonHelper->jsonDecode($response->getBody());

            if (!$response->isSuccess()) {
                throw new \Exception('Invalid response received');
            }

        } finally {
            $this->logger->debug(
                [
                    'result' => $this->getTokenId()
                ]
            );
        }

        return $products;
    }

    public function getAllProductIds()
    {
        $client = new \Laminas\Http\Client();

        $client->setUri($this->getEndpoint('products'));
        $client->setOptions($this->clientConfig);
        $client->setMethod(Request::METHOD_GET);
        $client->setHeaders(
            [
                'Accept'        => 'application/json',
                'Authorization' => 'Bearer ' . $this->getTokenId()
            ]
        );

        $client->setEncType(\Laminas\Http\Client::ENC_FORMDATA);

        $client->setParameterGet([
            'searchCriteria[pageSize]' => urlencode(self::PAGE_SIZE),
            'fields' => 'items[id]'
        ]);

        try {
            $response = $client->send();

            if (!$response->isSuccess()) {
                throw new \Exception('Invalid response received');
            }

            $products = $this->jsonHelper->jsonDecode($response->getBody());
        } finally {
            $this->logger->debug(
                [
                    'result' => $this->getTokenId()
                ]
            );
        }

        return $products;
    }

    public function getUpdatedFranchiseProductsDatas(int $nbHour): array
    {
        $extractDatetime = $this->timezone->date();
        $extractDatetime->sub(new DateInterval("PT{$nbHour}H"));

        $client = new \Laminas\Http\Client();
        $client->setUri($this->getEndpoint('franchise_products'));
        $client->setOptions($this->clientConfig);
        $client->setMethod(Request::METHOD_GET);
        $client->setHeaders(
            [
                'Accept'        => 'application/json',
                'Authorization' => 'Bearer ' . $this->getTokenId()
            ]
        );

        $client->setEncType(\Laminas\Http\Client::ENC_FORMDATA);

        $parameters = [
            'searchCriteria' => [
                'filterGroups' => [
                    [
                        'filters' => [
                            [
                                'field'         => 'updated_at',
                                'value'         => $extractDatetime->format('Y-m-d H:i:s'),
                                'conditionType' => 'gt',
                            ],
                        ],
                    ]
                ],
            ],
        ];

        $httpParameters = ['searchCriteria[pageSize]' => urlencode(self::ALL_PAGE_SIZE)];

        $filterGroupCpt = 0;
        foreach ($parameters['searchCriteria'] as $searchCriteriaLabel => $filterGroups) {
            foreach ($filterGroups as $filters) {
                $filterCpt = 0;
                foreach ($filters as $filter) {
                    foreach ($filter as $filterContent) {
                        foreach ($filterContent as $fieldName => $value) {
                            $parameters["searchCriteria[{$searchCriteriaLabel}][{$filterGroupCpt}][filters][{$filterCpt}][{$fieldName}]"] = urlencode($value);
                        }
                        $filterCpt++;
                    }
                }

                $filterGroupCpt++;
            }
        }

        $client->setParameterGet(array_merge($httpParameters, $parameters));

        try {
            $response = $client->send();

            if (!$response->isSuccess()) {
                throw new \Exception('Invalid response received');
            }

            $images = $this->jsonHelper->jsonDecode($response->getBody());

        } finally {
            $this->logger->debug(
                [
                    'result' => $this->getTokenId()
                ]
            );
        }

        return $images;
    }
}

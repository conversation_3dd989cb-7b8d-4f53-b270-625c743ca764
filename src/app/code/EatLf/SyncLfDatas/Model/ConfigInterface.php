<?php
namespace EatLf\SyncLfDatas\Model;

/**
 * SyncLfDatas module interface
 *
 */
interface ConfigInterface
{

    /**
     * Enabled config path
     */
    const XML_PATH_ENABLED = 'syncproducts/syncproducts/enabled';

    /**
     * host config path
     */
    const XML_PATH_HOST = 'syncproducts/syncproducts/host';

    /**
     * Username config path
     */
    const XML_PATH_USERNAME = 'syncproducts/syncproducts/username';

    /**
     * Username config path
     */
    const XML_PATH_PASSWORD = 'syncproducts/syncproducts/password';

    /**
     * Check if synchronization is enable
     *
     */
    public function isEnabled();

    /**
     * Return hostname
     *
     * @return string
     * @since 100.2.0
     */
    public function hostname();

    /**
     * Return username
     *
     * @return string
     * @since 100.2.0
     */
    public function username();

    /**
     * Return password
     *
     * @return string
     * @since 100.2.0
     */
    public function password();

}

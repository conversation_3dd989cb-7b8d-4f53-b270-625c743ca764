<?php

namespace EatLf\SyncLfDatas\Model\Services\Product\Media;

use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\ProductRepository;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use EatLf\SyncLfDatas\Model\Ws\Product as ProductWs;
use Magento\Catalog\Api\Data\ProductAttributeMediaGalleryEntryInterface;
use Magento\CatalogImportExport\Model\Import\Proxy\Product\ResourceModelFactory;
use Magento\CatalogImportExport\Model\Import\Product\SkuProcessor;
use Magento\CatalogImportExport\Model\Import\Product\MediaGalleryProcessor;
use Magento\MediaStorage\Service\ImageResize;


/**
 * Class Update
 *
 * @package EatLf\SyncLfDatas\Model\Services\Product
 */
class Update
{

    /**
     * @var SkuProcessor
     */
    private $skuProcessor;

    /**
     * Media gallery attribute code.
     */
    public const MEDIA_GALLERY_ATTRIBUTE_CODE = 'media_gallery';

    /**
     * Attribute id for product images storage.
     *
     * @var array
     */
    protected $_mediaGalleryAttributeId = null;

    /**
     * @var ProductCollectionFactory
     */
    protected $productCollectionFactory;

    /**
     * @var ResourceModelFactory
     */
    protected $_resourceFactory;

    /**
     * @var ProductAttributeMediaGalleryEntryInterface
     */
    protected $productAttributeMediaGalleryEntry;

    /**
     * @var ProductRepository
     */
    protected $productRepository;

    /**
     * @var ProductWs
     */
    protected $productWs;

    /**
     * @var MediaGalleryProcessor
     */
    protected $mediaGalleryProcessor;
    /**
     * @var \Magento\MediaStorage\Service\ImageResize
     */
    private ImageResize $imageResize;

    /**
     * Update constructor.
     */
    public function __construct(
        ProductCollectionFactory $productCollectionFactory,
        ProductWs $productWs,
        ProductRepository $productRepository,
        ResourceModelFactory $resourceFactory,
        MediaGalleryProcessor $mediaGalleryProcessor,
        SkuProcessor $skuProcessor,
        ImageResize $imageResize
    ) {
        $this->skuProcessor = $skuProcessor;
        $this->mediaGalleryProcessor = $mediaGalleryProcessor;
        $this->_resourceFactory = $resourceFactory;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->productRepository = $productRepository;
        $this->productWs = $productWs;
        $this->imageResize = $imageResize;
    }


    /**
     * @param array $data
     */
    public function process(array $data)
    {
        $mediaGalleryDatas = [];
        foreach ($data as $row) {
            $sku = $row['sku'];

            /** @var Product $product */
            $product = $this->productRepository->get($sku);
            $productImages = $this->productWs->getProductMediaGallery($sku);

            $this->deleteOldImages($productImages, $product);

            /**
             * Store new media datas in mediaGalleryDatas array
             */
            foreach ($productImages as $productImage) {
                $mediaGalleryDatas[1][$sku][] = [
                    'media_type' => $productImage['media_type'],
                    'value' => $productImage['file'],
                    'position' => $productImage['position'],
                    'label' => $productImage['label'],
                    'disabled' => $productImage['disabled'],
                    'types' => $productImage['types'],
                    'attribute_id' => $this->getMediaGalleryAttributeId(),
                ];

                $this->imageResize->resizeFromImageName($productImage['file']);
            }
            $this->skuProcessor->addNewSku($sku, ['entity_id' => $product->getId()]);
        }

        /**
         * Save all new skus
         */
        if (!empty($mediaGalleryDatas)) {
            $this->mediaGalleryProcessor->saveMediaGallery($mediaGalleryDatas);
        }
    }

    /**
     * @return array|mixed
     */
    public function getMediaGalleryAttributeId()
    {
        if (!$this->_mediaGalleryAttributeId) {
            /** @var $resource \Magento\CatalogImportExport\Model\Import\Proxy\Product\ResourceModel */
            $resource = $this->_resourceFactory->create();
            $this->_mediaGalleryAttributeId = $resource->getAttribute(self::MEDIA_GALLERY_ATTRIBUTE_CODE)->getId();
        }
        return $this->_mediaGalleryAttributeId;
    }

    /**
     * @param $productImages
     * @param \Magento\Catalog\Model\Product $product
     *
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     */
    public function deleteOldImages($productImages, Product $product)
    {
        $oldImages = $product->getMediaGalleryEntries();
        $hasDeletedImage = false;

        foreach ($oldImages as $key => $oldImage) {
            $found = false;

            foreach ($productImages as $productImage) {
                if ($productImage['file'] === $oldImage->getFile()) {
                    $found = true;
                }
            }

            if (!$found) {
                $hasDeletedImage = true;
                unset($oldImages[$key]);
            }
        }

        if ($hasDeletedImage) {
            $product->setMediaGalleryEntries($oldImages);
            $this->productRepository->save($product);
        }
    }
}

<?php

namespace EatLf\SyncLfDatas\Model\Services\Product;

use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\Registry;

class Delete
{
    /**
     * @var CollectionFactory
     */
    private $productCollectionFactory;

    /**
     * @var
     */
    private $registry;

    /**
     * Delete constructor.
     */
    public function __construct(
        CollectionFactory $productCollectionFactory,
        Registry $registry
    )
    {
        $this->registry = $registry;
        $this->productCollectionFactory = $productCollectionFactory;
    }

    public function process(array $productIds)
    {
        $this->registry->register('isSecureArea', true);
        $productCollection = $this->productCollectionFactory->create();
        $productCollection->addFieldToFilter('entity_id', array('in' => $productIds));
        $productCollection->delete();
    }
}

<?php

namespace EatLf\SyncLfDatas\Model\Services\FranchiseProduct;

use Lf\Franchise\Api\FranchiseProductRepositoryInterface;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Lf\Franchise\Model\FranchiseProduct;
use Lf\Franchise\Model\FranchiseProductFactory;
use Magento\Framework\Api\Search\SearchCriteriaBuilder;
use Psr\Log\LoggerInterface;

/**
 * Class Update
 */
class Update
{
    /**
     * @var FranchiseProductRepositoryInterface
     */
    private FranchiseProductRepositoryInterface $franchiseProductRepository;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var FranchiseProduct
     */
    private FranchiseProduct $franchiseProduct;

    /**
     * @var array
     */
    private array                   $existingFranchiseIds;
    private FranchiseProductFactory $franchiseProductFactory;

    /**
     * Update constructor.
     */
    public function __construct(
        FranchiseProductFactory             $franchiseProductFactory,
        FranchiseProductRepositoryInterface $franchiseProductRepository,
        FranchiseRepositoryInterface        $franchiseRepository,
        SearchCriteriaBuilder               $searchCriteriaBuilder,
        LoggerInterface                     $logger
    )
    {
        $this->franchiseProductRepository = $franchiseProductRepository;
        $this->logger = $logger;

        $searchCriteria = $searchCriteriaBuilder->create();
        foreach ($franchiseRepository->getList($searchCriteria)->getItems() as $franchise) {
            $this->existingFranchiseIds[] = $franchise->getId();
        }
        $this->franchiseProductFactory = $franchiseProductFactory;
    }


    /**
     * @param array $franchiseProductDatas
     */
    public function process(array $franchiseProductDatas): void
    {
        foreach ($franchiseProductDatas as $franchiseProductData) {
            try {
                if (in_array($franchiseProductData['franchise_id'], $this->existingFranchiseIds)) {
                    $franchiseProduct = $this->franchiseProductFactory->create();
                    $franchiseProduct = $franchiseProduct->getByProductAndFranchise(
                        $franchiseProductData['product_id'],
                        $franchiseProductData['franchise_id']
                    );

                    if ($franchiseProduct->getId()) {
                        $franchiseProduct->changeTaxClassId($franchiseProductData['tax_class_id']);
                        $franchiseProduct->setCodeCompta($franchiseProductData['code_compta']);
                        $this->franchiseProductRepository->save($franchiseProduct);
                    }
                    unset($franchiseProduct);
                }
            } catch (\Exception $e) {
                $this->logger->error(__('Erreur lors du traitement du franchise/product ID %1/%2 : %3',
                    $franchiseProductData['franchise_id'], $franchiseProductData['product_id'], $e->getMessage()));
            }
        }
    }
}

<?php

namespace EatLf\SyncLfDatas\Cron;

use EatLf\SyncLfDatas\Model\Services\Product as ProductSync;
use Magento\Framework\App\Cache\TypeListInterface;
use Magento\Indexer\Model\IndexerFactory;

/**
 * Class SyncLfDatas
 * @package EatLf\SyncLfDatas\Cron
 */
class SyncLfDatas
{
    /**
     * @var ProductSync
     */
    protected $productSync;

    protected $cacheToFlush = [
        'full_page',
    ];

    protected $indexesToBuild = [
        'cataloginventory_stock',
    ];

    /**
     * @var TypeListInterface
     */
    private $cacheTypeList;

    /**
     * @var IndexerFactory
     */
    private $indexerFactory;

    /**
     * SyncLfDatas constructor.
     * @param ProductSync $productSync
     */
    public function __construct(
        ProductSync $productSync,
        TypeListInterface $cacheTypeList,
        IndexerFactory $indexerFactory
    )
    {
        $this->productSync = $productSync;
        $this->cacheTypeList = $cacheTypeList;
        $this->indexerFactory = $indexerFactory;
    }


    /**
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        try {
            $this->productSync->execute(30);

            foreach ($this->indexesToBuild as $indexerId) {
                $indexer = $this->indexerFactory->create();
                $indexer->load($indexerId);
                $indexer->reindexAll();
            }

            foreach ($this->cacheToFlush as $cache) {
                $this->cacheTypeList->cleanType($cache);
            }
        } catch (\Exception $e) {
            throw new \Magento\Framework\Exception\LocalizedException(__('Unable to synchronize : ' . $e->getMessage()));
        }
    }

}
<?php
declare(strict_types=1);

namespace EatLf\FranchiseInventory\Setup\Patch\Data;

use Exception;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Lf\FranchiseInventory\Model\ResourceModel\DailyStock\CollectionFactory as DailyStockCollectionFactory;
use Lf\FranchiseInventory\Model\ResourceModel\DailyStock as DailyStockRessource;


class RemoveAccessoriesProductsFromDailyStock implements DataPatchInterface
{

    /**
     * @var ModuleDataSetupInterface $moduleDataSetup
     */
    private ModuleDataSetupInterface $moduleDataSetup;
    /**
     * @var CollectionFactory $productCollectionFactory
     */
    private CollectionFactory $productCollectionFactory;
    /**
     * @var DailyStockCollectionFactory $dailyStockCollectionFactory
     */
    private DailyStockCollectionFactory $dailyStockCollectionFactory;
    /**
     * @var DailyStockRessource $dailyStockRessource
     */
    private DailyStockRessource $dailyStockRessource;


    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        CollectionFactory $productCollectionFactory,
        DailyStockCollectionFactory $dailyStockCollectionFactory,
        DailyStockRessource $dailyStockRessource
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->dailyStockCollectionFactory = $dailyStockCollectionFactory;
        $this->dailyStockRessource = $dailyStockRessource;
    }

    /**
     * @return $this
     * @throws Exception
     */
    public function apply(): RemoveAccessoriesProductsFromDailyStock
    {
        $this->moduleDataSetup->startSetup();

        $productCollection = $this->productCollectionFactory->create();
        $productCollection->addAttributeToFilter('is_accessory', 1);

        $ids = [];
        foreach ($productCollection->getItems() as $item) {
            $ids[] = $item->getEntityId();
        }

        $dailyStocks = $this->dailyStockCollectionFactory->create();
        $dailyStocks->addFieldToFilter('product_id', ['in' => $ids]);
        foreach( $dailyStocks->getItems() as $item) {
            $this->dailyStockRessource->delete($item);
        }
        return $this;
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}

<?php
declare(strict_types=1);

namespace EatLf\FranchiseInventory\Model;

use DateTime;
use Lf\FranchiseInventory\Api\DailyStockRepositoryInterface;
use EatLf\FranchiseInventory\Api\InventoryManagementInterface;
use EatLf\FranchiseInventory\Block\Stock\Manage;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Lf\Franchise\Model\Franchise;
use Lf\FranchiseInventory\Api\StockManagementInterface;
use Lf\FranchiseInventory\Model\DailyStock;
use Lf\FranchiseInventory\Model\ResourceModel\DailyStock as DailyStockResourceModel;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\NoSuchEntityException;

class InventoryManagement implements InventoryManagementInterface, StockManagementInterface
{
    /**
     * @var ResourceModel\DailyStock
     */
    private $dailyStockResource;

    private $dates = [];
    /**
     * @var DailyStockRepositoryInterface
     */
    private $dailyStockRepository;
    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;
    /**
     * @var FilterBuilder
     */
    private $filterBuilder;
    /**
     * @var FranchiseRepositoryInterface
     */
    private $franchiseRepository;

    public function __construct(
        DailyStockResourceModel $dailyStockResource,
        DailyStockRepositoryInterface $dailyStockRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        FranchiseRepositoryInterface $franchiseRepository
    ) {
        $this->dailyStockResource = $dailyStockResource;
        $this->dailyStockRepository = $dailyStockRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->franchiseRepository = $franchiseRepository;
    }

    /**
     * {@inheritDoc}
     */
    public function franchiseHasStock($franchiseId, DateTime $date): bool
    {
        foreach ($this->getDatesInStock($franchiseId) as $stockDate) {
            if ($stockDate === $date->format(DailyStock::DATE_FORMAT)) {
                return true;
            }
        }

        return false;
    }

    /**
     * {@inheritDoc}
     */
    public function getNextAvailableDates($franchiseId, DateTime $startDate, $number = 1): array
    {
        $result = [];

        try {
            /** @var Franchise $franchise */
            $franchise = $this->franchiseRepository->getById($franchiseId);
        } catch (NoSuchEntityException $e) {
            return $result;
        }

        // Stock can only be defined NUMBER_OF_DAYS (worked days) in the future
        $nextWorkedDays = $franchise->getNextWorkedDays(Manage::NUMBER_OF_DAYS);

        foreach ($nextWorkedDays as $nextWorkedDay) {
            if ($number <= 0) {
                break;
            }

            if ($nextWorkedDay->format(DailyStock::DATE_FORMAT) <=
                $startDate->format(DailyStock::DATE_FORMAT)) {
                continue;
            }

            if ($this->franchiseHasStock($franchiseId, $nextWorkedDay)) {
                $result[] = $nextWorkedDay;
                $number--;
            }
        }

        return $result;
    }

    protected function getDatesInStock($franchiseId)
    {
        if (isset($this->dates[$franchiseId])) {
            return $this->dates[$franchiseId];
        }

        $this->dates[$franchiseId] = $this->dailyStockResource->getDatesWithProductInStock($franchiseId);

        return $this->dates[$franchiseId];
    }

    public function isInStock($productId, $franchiseId, \DateTime $date): bool
    {
        $franchiseStock = $this->dailyStockRepository->getForFranchise($franchiseId, $date);

        // A product must have a stock greater than 0 for the specified date.
        return $franchiseStock
                ->getStockForProduct($productId)
                ->getStock() > 0;
    }
}
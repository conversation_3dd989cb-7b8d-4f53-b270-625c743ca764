<?php
/**
 * @category  <todo>
 * @package   <todo>
 * <AUTHOR> <<EMAIL>>
 * @Copyright 2020 Adin
 * @license   Apache License Version 2.0
 */

namespace EatLf\FranchiseInventory\Model\Rewrite;

use Lf\FranchiseInventory\Api\Data\DailyStockInterface;
use Lf\FranchiseInventory\Model\DailyStockFactory;

class FranchiseDailyStock extends \Lf\FranchiseInventory\Model\FranchiseDailyStock
{

    /**
     * set stock to 0 and not null for EatLf
     * @param int $productId
     * @return DailyStockInterface
     */
    public function getStockForProduct($productId): DailyStockInterface
    {
        if (!isset($this->dailyStocks[$productId])) {
            $this->dailyStocks[$productId] = $this->dailyStockFactory->create()
                ->setFranchiseId($this->franchiseId)
                ->setDate($this->date)
                ->setProductId($productId)
                ->setStock(0)
                ->setSales(0);
        }

        return $this->dailyStocks[$productId];
    }

}
<?php
declare(strict_types=1);

namespace EatLf\FranchiseInventory\Model\ResourceModel\DailyStock;

use EatLf\FranchiseInventory\Helper\ProductName;
use Lf\FranchiseInventory\Model\ResourceModel\DailyStock;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Zend_Db_Select;

class Grid
{
    /**
     * @var CollectionFactory
     */
    private $productCollectionFactory;
    /**
     * @var DailyStock
     */
    private $dailyStockResource;
    /**
     * @var ProductName
     */
    private $productName;
    /**
     * @var AttributeRepositoryInterface
     */
    private $attributeRepository;

    public function __construct(
        CollectionFactory $productCollectionFactory,
        DailyStock $dailyStockResource,
        ProductName $productName,
        AttributeRepositoryInterface $attributeRepository
    )
    {
        $this->productCollectionFactory = $productCollectionFactory;
        $this->dailyStockResource = $dailyStockResource;
        $this->productName = $productName;
        $this->attributeRepository = $attributeRepository;
    }

    /**
     * Returns an array with one row per product containing stock and sales values for all the dates passed as parameter
     *
     * @param array $dates
     * @param $franchiseId
     * @param string|null $categoryId
     * @return array
     */
    public function getGridData(array $dates, $franchiseId, string $categoryId = null): array
    {
        $productCollection = $this->productCollectionFactory->create();

        try {
            $catAttributeId = $this->attributeRepository
                ->get('catalog_category', 'name')
                ->getAttributeId();
        } catch (NoSuchEntityException $e) {
            return [];
        }

        $productCollection
            ->addAttributeToSelect(Product::NAME)
            ->addAttributeToSelect('is_active_eatlf')
            ->addAttributeToFilter([
                ['attribute' => 'is_accessory', 'eq' => '0'],
                ['attribute' => 'is_accessory', 'null' => true ]
            ], '', 'left')
            ->addFieldToFilter(Product::STATUS, Status::STATUS_ENABLED);

        if($categoryId !== null) {
            $productCollection->addCategoriesFilter([
                'eq' => $categoryId
            ]);
        }

        $select = $productCollection
            ->getSelect()
            ->reset(Zend_Db_Select::COLUMNS)
            ->join(
                'franchise_product',
                "franchise_product.product_id = e.entity_id",
                ['is_active' => 'is_active_fo']
            )
            ->joinLeft(
                ['ccp' => 'catalog_category_product'],
                "ccp.product_id = e.entity_id",
                []
            )
            ->joinLeft(
                ['ccev' => 'catalog_category_entity_varchar'],
                "ccev.entity_id = ccp.category_id and ccev.attribute_id = {$catAttributeId} and ccev.store_id = 0",
                ['category' => 'value']
            )
            ->columns(['e.entity_id', 'e.sku'])
            ->where('franchise_product.franchise_id = ?', $franchiseId);

        foreach ($dates as $date) {
            $stockSelect = $this->dailyStockResource
                ->getConnection()
                ->select()
                ->from(
                   $this->dailyStockResource->getMainTableSecure(),
                    ['stock', 'sales', 'product_id']
                )
                ->where('franchise_id = ?', $franchiseId)
                ->where('date = ?', $date);

            $select
                ->joinLeft(
                    [$date => new \Zend_Db_Expr("($stockSelect)")],
                    new \Zend_Db_Expr("`{$date}`.product_id = e.entity_id"),
                    [
                        "{$date}_stock_init" => new \Zend_Db_Expr("COALESCE(`{$date}`.stock,0) + COALESCE(`{$date}`.sales,0)"),
                        "{$date}_stock" => new \Zend_Db_Expr("COALESCE(`{$date}`.stock,0)"),
                        "{$date}_sales" => new \Zend_Db_Expr("COALESCE(`{$date}`.sales,0)"),
                    ]
                );
        }

        $result = [];

        // Remove inactive products that don't have any sale for the grid dates
        /** @var Product $item */
        foreach ($productCollection->getItems() as $item) {
            $item->setName($this->productName->normalize($item->getName()));

            if($item->getIsActive() && $item->getIsActiveEatlf() == Status::STATUS_ENABLED) {
                $result[] = $item->toArray();
                continue;
            }

            $removeLine = true;

            foreach ($item->getData() as $key => $value) {
                if (strpos($key, 'sales') && $value != 0) {
                    $removeLine = false;
                    break;
                }
            }

            if (!$removeLine) {
                $result[] = $item->toArray();
            }
        }

        return $result;
    }
}

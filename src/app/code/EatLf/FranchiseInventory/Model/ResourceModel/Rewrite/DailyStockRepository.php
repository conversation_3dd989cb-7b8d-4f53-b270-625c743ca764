<?php
/**
 * @category  EatLf
 * @package   EatLf_FranchiseInventory
 * <AUTHOR> <<EMAIL>>
 * @Copyright 2020 Adin
 * @license   Apache License Version 2.0
 */

namespace EatLf\FranchiseInventory\Model\ResourceModel\Rewrite;

use Lf\FranchiseInventory\Api\Data\DailyStockInterface;
use Lf\FranchiseInventory\Model\DailyStock as DailyStockModel;
use DateTime;

class DailyStockRepository extends \Lf\FranchiseInventory\Model\ResourceModel\DailyStockRepository
{
    /**
     * {@inheritDoc}
     */
    public function getForProduct($franchiseId, DateTime $date, $productId): DailyStockInterface
    {
        $criteria = $this->searchCriteriaBuilder
            ->addFilter(DailyStockModel::FRANCHISE_ID, $franchiseId)
            ->addFilter(DailyStockModel::PRODUCT_ID, $productId)
            ->addFilter(DailyStockModel::DATE, $date->format(DailyStockModel::DATE_FORMAT))
            ->create();

        $stocks = $this->getList($criteria)->getItems();

        if(count($stocks) !== 1) { // stock data not found in db so initialize it at 0
            return $this->dailyStockFactory->create()
                ->setFranchiseId($franchiseId)
                ->setProductId($productId)
                ->setDate($date)
                ->setStock(0)
                ->setSales(0);
        }

        $stock = array_shift($stocks);

        return $stock;
    }

}
<?php
/**
 * @category  <todo>
 * @package   <todo>
 * <AUTHOR> <<EMAIL>>
 * @Copyright 2020 Adin
 * @license   Apache License Version 2.0
 */

namespace EatLf\FranchiseInventory\Model\ResourceModel\Rewrite;

use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Framework\Exception\LocalizedException;

class DailyStock extends \Lf\FranchiseInventory\Model\ResourceModel\DailyStock
{

    /**
     * Returns the list of available dates with at least one product in stock for the specified franchise
     *
     * @param $franchiseId
     * @return array
     */
    public function getDatesWithProductInStock($franchiseId): array
    {
        $productCollection = $this->productCollectionFactory->create();

        try {
            $productCollection
                ->addAttributeToFilter('status', Status::STATUS_ENABLED)
                ->addAttributeToFilter('is_active_eatlf', Status::STATUS_ENABLED)
                ->getSelect()
                ->reset(\Zend_Db_Select::COLUMNS)
                ->columns(new \Zend_Db_Expr('distinct(stocks.date)'))
                ->join(
                    ['stocks' => $this->getMainTableSecure()],
                    'e.entity_id = stocks.product_id',
                    []
                )
                ->join(
                    ['fp' => 'franchise_product'],
                    'fp.product_id = stocks.product_id and fp.franchise_id = stocks.franchise_id',
                    []
                )
                ->where('stocks.franchise_id = ?', $franchiseId)
                ->where('fp.is_active_fo = 1')
                ->where('stocks.stock > 0')
                ->order('date asc');

            $dates = $productCollection->getItems();

            return array_map(function ($elem) {
                return $elem['date'];
            }, $dates);
        } catch (LocalizedException $e) {
            $this->logger->error('An error occured when trying to check stocks', ['exception' => $e]);
            return [];
        }
    }
}
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Catalog::catalog">
                    <resource id="Magento_Catalog::inventory" title="Inventory">
                        <resource id="EatLf_FranchiseInventory::stocks" title="Produits Eat Stocks" translate="title" sortOrder="10"  />
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>

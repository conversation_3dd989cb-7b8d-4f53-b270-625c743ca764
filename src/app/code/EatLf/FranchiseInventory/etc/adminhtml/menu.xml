<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="EatLf_FranchiseInventory::stocks" title="Produits Eat Stocks" translate="title" module="EatLf_FranchiseInventory"
             parent="Magento_Catalog::inventory" sortOrder="15" dependsOnModule="EatLf_FranchiseInventory"
             action="franchiseinventory/stock/manage" resource="EatLf_FranchiseInventory::stocks"/>
    </menu>
</config>
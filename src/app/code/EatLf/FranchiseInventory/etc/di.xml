<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="EatLf\FranchiseInventory\Api\InventoryManagementInterface"
                type="EatLf\FranchiseInventory\Model\InventoryManagement" />

    <preference for="Lf\FranchiseInventory\Model\FranchiseDailyStock"
                type="EatLf\FranchiseInventory\Model\Rewrite\FranchiseDailyStock"/>

    <preference for="Lf\FranchiseInventory\Model\ResourceModel\DailyStock"
                type="EatLf\FranchiseInventory\Model\ResourceModel\Rewrite\DailyStock"/>

    <preference for="Lf\FranchiseInventory\Model\ResourceModel\DailyStockRepository"
                type="EatLf\FranchiseInventory\Model\ResourceModel\Rewrite\DailyStockRepository"/>

    <preference for="Lf\FranchiseInventory\Api\StockManagementInterface"
                type="EatLf\FranchiseInventory\Model\InventoryManagement"/>
</config>
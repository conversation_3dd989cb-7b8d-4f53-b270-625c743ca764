<?php

namespace EatLf\FranchiseInventory\Api;

use DateTime;

interface InventoryManagementInterface
{
    /**
     * Verifies that at least one product is in stock for the specified date / franchise id
     *
     * @param $franchiseId
     * @param \DateTime $date
     * @return bool
     */
    public function franchiseHasStock($franchiseId, \DateTime $date): bool;

    /**
     * Returns the next available working days based on stock data for the specified franchise
     *
     * @param $franchiseId
     * @param DateTime $startDate
     * @param int $number
     * @return DateTime[]
     */
    public function getNextAvailableDates($franchiseId, DateTime $startDate, $number = 1): array;
}
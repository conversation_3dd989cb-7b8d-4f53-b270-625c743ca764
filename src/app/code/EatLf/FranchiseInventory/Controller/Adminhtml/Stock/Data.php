<?php
declare(strict_types=1);

namespace EatLf\FranchiseInventory\Controller\Adminhtml\Stock;

use EatLf\FranchiseInventory\Block\Stock\Manage;
use EatLf\FranchiseInventory\Model\ResourceModel\DailyStock\Grid;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\NoSuchEntityException;

class Data extends Action
{
    /**
     * @var JsonFactory
     */
    private $jsonFactory;
    /**
     * @var Grid
     */
    private $gridResource;
    /**
     * @var FranchiseRepositoryInterface
     */
    private $franchiseRepository;

    public function __construct(
        Action\Context $context,
        JsonFactory $jsonFactory,
        Grid $gridResource,
        FranchiseRepositoryInterface $franchiseRepository
    )
    {
        parent::__construct($context);
        $this->jsonFactory = $jsonFactory;
        $this->gridResource = $gridResource;
        $this->franchiseRepository = $franchiseRepository;
    }

    public function execute()
    {
        $result = $this->jsonFactory->create();

        try {
            $franchiseId = $this->franchiseRepository->getFranchiseActif()->getFranchiseId();
        } catch (NoSuchEntityException $e) {
            return $result->setData([
                'success' => false,
                'errors' => [
                    'No franchise available in current context'
                ]
            ]);
        }

        $paramDates = $this->getRequest()->getParam('dates', null);

        if ($paramDates === null) {
            return $result->setData([
                'success' => false,
                'errors' => [
                    'The dates parameter is mandatory'
                ]
            ]);
        }

        $dates = explode(Manage::DATES_SEPARATOR, $paramDates);

        if ($dates === false || count($dates) === 0) {
            return $result->setData([
                'success' => false,
                'errors' => [
                    'The dates parameter is invalid'
                ]
            ]);
        }

        $categoryId = $this->getRequest()->getParam('categoryId', null);

        $result->setData([
            'data' => $this->gridResource->getGridData($dates, $franchiseId, $categoryId)
        ]);

        return $result;
    }
}
<?php
declare(strict_types=1);

namespace EatLf\FranchiseInventory\Controller\Adminhtml\Stock;

use Magento\Backend\App\Action;
use Magento\Framework\View\Result\PageFactory;

class Manage extends Action
{
    /**
     * @var PageFactory
     */
    private $resultPageFactory;

    public function __construct(
        Action\Context $context,
        PageFactory $resultPageFactory
    )
    {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
    }

    public function execute()
    {
        $resultPage = $this->resultPageFactory->create();

        $resultPage->setActiveMenu('EatLf_FranchiseInventory::stocks');
        $resultPage->getConfig()->getTitle()->prepend(__('Stock management'));

        return $resultPage;
    }
}
<?php
declare(strict_types=1);

namespace EatLf\FranchiseInventory\Controller\Adminhtml\Stock;

use Lf\FranchiseInventory\Api\DailyStockRepositoryInterface;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\NoSuchEntityException;

class Update extends Action
{
    /**
     * @var DailyStockRepositoryInterface
     */
    private $dailyStockRepository;
    /**
     * @var JsonFactory
     */
    private $jsonFactory;
    /**
     * @var FranchiseRepositoryInterface
     */
    private $franchiseRepository;

    public function __construct(
        Action\Context $context,
        FranchiseRepositoryInterface $franchiseRepository,
        DailyStockRepositoryInterface $dailyStockRepository,
        JsonFactory $jsonFactory
    )
    {
        parent::__construct($context);
        $this->dailyStockRepository = $dailyStockRepository;
        $this->jsonFactory = $jsonFactory;
        $this->franchiseRepository = $franchiseRepository;
    }

    public function execute()
    {
        $result = $this->jsonFactory->create();

        $productId = $this->getRequest()->getParam('product_id');
        $date = $this->getRequest()->getParam('date');
        $qty = $this->getRequest()->getParam('qty');

        if (!is_numeric($qty) || intval($qty < 0)) {
            return $result->setData([
               'success' => false,
               'errors' => [
                   __('The quantity received is not a valid number')
               ]
            ]);
        }

        $newQty = intval($qty);

        try {
            $dailyStock = $this->dailyStockRepository->getForProduct(
                $this->franchiseRepository->getFranchiseActif()->getFranchiseId(),
                new \DateTime($date),
                $productId
            );
        } catch (NoSuchEntityException $e) {
            return $result->setData([
                'success' => false,
                'errors' => [
                    __('No franchise found in current context')
                ]
            ]);
        }

        $dailyStock->setStock($newQty);

        $this->dailyStockRepository->save($dailyStock);

        return $result->setData([
            'success' => true,
            'newQty' => $newQty
        ]);
    }
}
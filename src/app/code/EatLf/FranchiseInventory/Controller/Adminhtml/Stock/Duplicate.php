<?php
declare(strict_types=1);

namespace EatLf\FranchiseInventory\Controller\Adminhtml\Stock;

use EatLf\FranchiseInventory\Block\Stock\Manage;
use EatLf\FranchiseInventory\Model\ResourceModel\Rewrite\DailyStock;
use Lf\FranchiseInventory\Api\DailyStockRepositoryInterface;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\Controller\Result\JsonFactory;

class Duplicate extends Action
{
    /**
     * @var DailyStockRepositoryInterface
     */
    private $dailyStockRepository;
    /**
     * @var JsonFactory
     */
    private $jsonFactory;
    /**
     * @var FranchiseRepositoryInterface
     */
    private $franchiseRepository;
    /**
     * @var ProductRepository
     */
    private ProductRepository $productRepository;

    public function __construct(
        Action\Context                $context,
        FranchiseRepositoryInterface  $franchiseRepository,
        DailyStockRepositoryInterface $dailyStockRepository,
        JsonFactory                   $jsonFactory,
        ProductRepository             $productRepository
    )
    {
        parent::__construct($context);
        $this->dailyStockRepository = $dailyStockRepository;
        $this->jsonFactory = $jsonFactory;
        $this->franchiseRepository = $franchiseRepository;
        $this->productRepository = $productRepository;
    }

    public function execute()
    {
        $result = $this->jsonFactory->create();

        $productSkus = $this->getRequest()->getParam('product_skus');
        $date = \DateTime::createFromFormat('j/m/Y',$this->getRequest()->getParam('date'));
        $type = $this->getRequest()->getParam('type');

        $franchiseId = $this->franchiseRepository->getFranchiseActif()->getFranchiseId();

        $franchise = $this->franchiseRepository->getById($franchiseId);

        $nextWorkingDays = $franchise->getNextWorkedDays(Manage::NUMBER_OF_DAYS);

        $nextWorkingDay = "";
        for($i=0; $i<count($nextWorkingDays); $i++)
        {
            if($nextWorkingDays[$i]->format('d-m-Y')==$date->format('d-m-Y'))
            {
                $nextWorkingDay = $nextWorkingDays[$i+1];
            }
        }

        foreach($productSkus as $productSku)
        {
            $product = $this->productRepository->get($productSku);
            $currentDailyStock = $this->dailyStockRepository->getForProduct($franchiseId,$date,$product->getId());
            $nextDailyStock = $this->dailyStockRepository->getForProduct($franchiseId,$nextWorkingDay,$product->getId());

                switch ($type){
                    case 'init' :
                        $nextDailyStock->setStock($currentDailyStock->getStock()+$currentDailyStock->getSales());
                        break;

                    case 'stock' :
                        $nextDailyStock->setStock($currentDailyStock->getStock());
                        break;
                }
                $this->dailyStockRepository->save($nextDailyStock);
        }

        return $result->setData([
            'success' => true,
        ]);
    }
}

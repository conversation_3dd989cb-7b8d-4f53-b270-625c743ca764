<?php
declare(strict_types=1);

namespace EatLf\FranchiseInventory\Block\Stock;

use DateTime;
use Lf\FranchiseInventory\Model\ResourceModel\DailyStock;
use IntlDateFormatter;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Magento\Backend\Block\Template;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class Manage extends Template
{
    const DATES_SEPARATOR = ';';

    const NUMBER_OF_DAYS = 7;

    /**
     * @var DailyStock
     */
    private $dailyStockResource;
    /**
     * @var TimezoneInterface
     */
    private $timezone;
    /**
     * @var array
     */
    private $dates = null;
    /**
     * @var CollectionFactory
     */
    private $categoryCollectionFactory;
    /**
     * @var FranchiseRepositoryInterface
     */
    private $franchiseRepository;

    public function __construct(
        Template\Context $context,
        DailyStock $dailyStockResource,
        TimezoneInterface $timezone,
        CollectionFactory $categoryCollectionFactory,
        FranchiseRepositoryInterface $franchiseRepository,
        array $data = []
    )
    {
        parent::__construct($context, $data);
        $this->dailyStockResource = $dailyStockResource;
        $this->timezone = $timezone;
        $this->categoryCollectionFactory = $categoryCollectionFactory;
        $this->franchiseRepository = $franchiseRepository;
    }

    public function getDates(): array
    {
        if ($this->dates === null) {
            try {
                $franchise = $this->franchiseRepository->getFranchiseActif();
            } catch (NoSuchEntityException $e) {
                return [];
            }

            $this->dates = $franchise->getNextWorkedDays(self::NUMBER_OF_DAYS);
        }

        return $this->dates;
    }

    public function getDisplayDates(): array
    {
        return array_map(function (DateTime $date) {
            return $this->timezone->formatDate(
                $date,
                IntlDateFormatter::SHORT,
                false
            );
        }, $this->getDates());
    }

    public function getTransferDates(): array
    {
        return array_map(function (DateTime $date) {
            return $date->format('Y-m-d');
        }, $this->getDates());
    }

    public function getJsonDates(): string
    {
        return json_encode($this->getTransferDates());
    }

    public function getDataUrl(): string
    {
        return $this->_urlBuilder->getUrl(
            'franchiseinventory/stock/data',
            ['dates' => join(self::DATES_SEPARATOR, $this->getTransferDates())]
        );
    }

    public function getUpdateUrl(): string
    {
        return $this->_urlBuilder->getUrl('franchiseinventory/stock/update');
    }

    public function getDuplicateUrl(): string
    {
        return $this->_urlBuilder->getUrl('franchiseinventory/stock/duplicate');
    }

    public function getCategories(): array
    {
        return $this->categoryCollectionFactory
            ->create()
            ->addFieldToFilter('level', ['eq' => 2])
            ->addFieldToFilter('is_active', 1)
            ->setOrder('name')
            ->getItems();
    }
}
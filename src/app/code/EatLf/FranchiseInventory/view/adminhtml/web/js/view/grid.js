define([
    'jquery',
    'EatLf_FranchiseInventory/js/model/csv-button-builder',
    'EatLf_FranchiseInventory/js/model/qty-input-builder',
    'EatLf_FranchiseInventory/js/model/datatable-language',
    'EatLf_FranchiseInventory/js/model/stock-row',
    'mage/translate',
    'datatables.net',
    'datatables.net-buttonsHtml5',
    'datatables.net-fixedHeader',
    'jquery.loadingOverlay'
], function (
    $,
    csvButtonBuilder,
    qtyInputBuilder,
    datatableLanguage,
    stockRow,
    $t
) {
    'use strict';

    var STOCK_TYPE = 'stock',
        columns = [
            {data: 'category', visible: false},
            {data: 'sku', visible: false},
            {data: 'name'}
        ];

    var grid = {
        init: function (config, element) {
            this.config = config;
            this.element = element;

            this.setupColumns();
            this.setupTable();
            this.setupDuplicate();
            this.setupProductSearch();
            this.setupInlineStockEdit();
            this.setupCategoryFilter();
        },

        setupColumns: function () {
            this.config.dates.forEach(function (date) {
                columns.push({
                    data: date + '_stock_init',
                    render: function (data, type, row) {
                        if (!stockRow.isActive(row)) {
                            return row[date + '_sales'];
                        }

                        return data;
                    }
                }, {
                    date: date,
                    type: STOCK_TYPE,
                    data: date + '_stock',
                    render: function (data, type, row) {
                        if (!stockRow.isActive(row)) {
                            return $t('Disabled');
                        }

                        return data;
                    }
                }, {
                    data: date + '_sales'
                });
            });
        },

        setupDuplicate: function () {
            var self = this;

            $(this.element).find('.duplicate-header').on('click', function (event) {
                $(self.element).LoadingOverlay("show");
                var product_skus = [];
                self.table.column(1, {search: 'applied'}).data().each(function (value, index) {
                    product_skus.push(value);
                });
                $.post(self.config.duplicateUrl, {
                    date: $(this).attr("data-date"),
                    type: $(this).attr("data-type"),
                    product_skus: product_skus,
                })
                    .done(function (response) {
                            if (response.success === true) {

                                var category = $(self.config.categorySelector).val();
                                var url = self.config.dataUrl;
                                if(category != "") {
                                    url += 'categoryId/' + category;
                                }

                                self.table
                                    .ajax
                                    .url(url)
                                    .load();
                                $(self.element).LoadingOverlay("hide", true);
                            }
                        }
                    )
            })
        },

        setupTable: function () {
            this.table = $(this.element).DataTable({
                ajax: this.config.dataUrl,
                fixedHeader: true,
                language: datatableLanguage,
                dom: '<"table-header"Biplr>t',
                pagingType: 'simple',
                pageLength: 100,
                stripeClasses: ['data-row _odd-row', 'data-row'],
                columns: columns,
                buttons: [
                    csvButtonBuilder(this.config.dates)
                ],
                order: [[ 2, 'asc' ]] // name
            }).on('processing.dt', function (e, settings, processing) {
                if (processing) {
                    $(e.currentTarget).LoadingOverlay("show");
                } else {
                    $(e.currentTarget).LoadingOverlay("hide", true);
                }
            });

            $(this.element).show();

            $(this.config.tableWrapperSelector).prepend(
                this.table
                    .buttons(0, null)
                    .containers()
            );
        },

        setupProductSearch: function () {
            var table = this.table;

            $('#productNameFilter').on('keyup change', function () {
                table.search(this.value).draw();
            }).on('click', function (event) {
                event.stopPropagation();
            });
        },

        setupInlineStockEdit: function () {
            var self = this,
                table = this.table;

            table.on('click', 'tbody td', function () {
                var input,
                    td = this,
                    cell = table.cell(this),
                    index = cell.index(),
                    columnDef = columns[index.column];

                if (columnDef.type !== STOCK_TYPE) { // only edit stock columns
                    return;
                }

                if (!stockRow.isActive(table.data()[index.row])) { // inactive products can't be edited
                    return;
                }

                input = qtyInputBuilder(
                    self.onQtyChange.bind(self, cell),
                    cell.data()
                ).blur(function () {
                    $(td).html(cell.data());
                });

                $(td).html(input);
                input.focus().select();
            });
        },

        onQtyChange: function (cell, input, newQty) {
            var index = cell.index();

            this.saveUpdate(
                columns[index.column]['date'],
                cell.table().data()[index.row]['entity_id'],
                newQty
            ).done(function (response) {
                var rowData = cell.table().row(index.row).data(),
                    date = columns[index.column]['date'];

                if (response.success === true) {
                    rowData[date + '_stock'] = newQty;
                    rowData[date + '_stock_init'] = parseInt(newQty, 10) + parseInt(rowData[date + '_sales'], 10);

                    cell.table().row(index.row).data(rowData);

                    return;
                }

                cell.data(cell.data()); // reset the old qty

                // eslint-disable-next-line no-alert
                window.alert(
                    response.errors.join('\n')
                );
            });
        },

        setupCategoryFilter: function () {
            var table = this.table,
                config = this.config;

            $(this.config.categorySelector).change(function () {
                var url = config.dataUrl;

                if (this.value !== '') {
                    url += 'categoryId/' + this.value;
                }

                table
                    .ajax
                    .url(url)
                    .load();
            });
        },

        saveUpdate: function (date, productId, qty) {
            return $.post(this.config.updateUrl, {
                product_id: productId,
                qty: qty,
                date: date
            });
        },
    };

    return function (config, element) {
        $(function () {
            grid.init(config, element);
        });
    };
});

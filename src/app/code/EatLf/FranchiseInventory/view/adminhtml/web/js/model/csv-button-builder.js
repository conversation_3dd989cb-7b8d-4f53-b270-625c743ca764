define([
    'mage/translate'
], function ($t) {
    'use strict';

    return function (dates) {
        return {
            extend: 'csvHtml5',
            text: $t('Export to CSV'),
            filename: 'export_stock',
            header: false,
            fieldSeparator: ';',
            customize: function (csv) {
                var header = dates.reduce(function (acc, date) {
                    return acc + ';"' + date + '_stock_init";"' + date + '_stock";"' + date + '_sales"';
                }, '"category";"sku";"name"');

                return header + '\n' + csv;
            }
        };
    };
});
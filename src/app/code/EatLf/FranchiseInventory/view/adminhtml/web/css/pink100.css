
table.dataTable tbody  tr .data-row {
    background: #f19292 !important;
}
table.dataTable thead {
    background: #514943;
    color: #ffffff;
}
table.dataTable td {

    color: #ffffff;
    border-left: .1rem dashed #d6d6d6;
    border-right: .1rem dashed #d6d6d6;
    color: #303030;
}
table.dataTable tr:last-child td {
    border-bottom: .1rem solid #d6d6d6;
}
.stocks-table-wrapper .stocks-table .date-header {
    text-align: center;
}

.stocks-table-wrapper .stocks-table th.duplicate-header {
    padding: unset;
}

.stocks-table-wrapper .stocks-table .duplicate-header.duplicate-end {
    border-right : .1rem solid #8a837f;
}
.stocks-table-wrapper .stocks-table .duplicate-header {
    text-align: center;
    border-left: none;
    border-right: none;
}

table.dataTable thead th, table.dataTable thead td {
    padding: 10px 18px;
    border-bottom: 1px solid #111;
}
table.dataTable thead th, table.dataTable tfoot th {
    font-weight: bold;
}
table.dataTable th {
    background-color: #514943;
    border: .1rem solid #8a837f;
    border-left-color: transparent;
    color: #fff;
    font-weight: 600;
    padding: 0;
    text-align: left;
}
table.dataTable .sorting, .sorting_asc, .sorting_desc {
    background : none!important;
}

table.dataTable tr td:nth-child(3n)  {
    background: #f6cbdd;
    opacity: 80%;
    overflow: visible;
}
._odd-row {
    background: #d0cdcd !important;
    border-left: .1rem dashed #d6d6d6;
    border-right: .1rem dashed #d6d6d6;
}
table.dataTable, table.dataTable th, table.dataTable td {
    box-sizing: content-box;
}
.data-grid tr._odd-row td {
    background-color: #dbdbdb ;
}

.stocks-table .product-filter {
    width: 77px;
    color: black;
}

.data-grid th:first-child {
    min-width: 100px;
}

define([
    'jquery'
], function ($) {
    'use strict';

    return function (onQtyChange, value) {
        var handleNewValue = function (elem) {
            var newQty = $(elem).val();

            $(elem).prop('disabled', true);
            onQtyChange(this, newQty);
        };

        var hasValueChange = function (elem) {
            var initialValue = parseInt(value, 10),
                newQty = parseInt($(elem).val(), 10);

            return newQty !== initialValue;
        };

        return $('<input/>', {
            'class': 'qty-input',
            'value': value
        }).on('click', function (event) {
            event.stopPropagation();
        }).on('blur', function (event) {
            if (hasValueChange(this)) {
                handleNewValue(this);
                event.stopImmediatePropagation();
            }
        }).on('keypress', function (event) {
            if (event.keyCode === 13 && hasValueChange(this)) {
                handleNewValue(this);
            }
        });
    };
});
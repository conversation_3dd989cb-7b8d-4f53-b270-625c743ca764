var config = {
    map: {
        '*': {
            'datatables.net': 'EatLf_FranchiseInventory/js/vendor/jquery.dataTables',
            'datatables.net-buttons': 'EatLf_FranchiseInventory/js/vendor/dataTables.buttons',
            'datatables.net-buttonsHtml5': 'EatLf_FranchiseInventory/js/vendor/buttons.html5',
            'datatables.net-fixedHeader': 'EatLf_FranchiseInventory/js/vendor/dataTables.fixedHeader',
            'jquery.loadingOverlay': 'EatLf_FranchiseInventory/js/vendor/jquery.loadingOverlay',
        }
    }
};
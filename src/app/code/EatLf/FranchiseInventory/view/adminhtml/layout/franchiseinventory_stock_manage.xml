<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="EatLf_FranchiseInventory::css/jquery.dataTables.css" />
        <css src="EatLf_FranchiseInventory::css/fixedHeader.dataTables.css" />
        <css src="EatLf_FranchiseInventory::css/buttons.dataTables.css" />
        <css src="EatLf_FranchiseInventory::css/pink100.css" />
    </head>
    <body>
        <referenceContainer name="content">
            <block class="EatLf\FranchiseInventory\Block\Stock\Manage"
                   name="eatlf.franchise.inventory.manage"
                   template="EatLf_FranchiseInventory::stock/manage.phtml" />
        </referenceContainer>
    </body>
</page>

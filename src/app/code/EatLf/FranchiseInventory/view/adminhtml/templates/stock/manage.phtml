<?php
/** @var EatLf\FranchiseInventory\Block\Stock\Manage $block */
$formattedDates = $block->getDisplayDates();
$dates = $block->getTransferDates();
$nbCols = count($formattedDates);
?>
<div class="stocks-table-wrapper" id ="stocksTableWrapper">
    <select id="categorySelect" class="admin__control-select">
        <option value="" selected="selected"><?= __('All categories') ?></option>
        <?php foreach ($block->getCategories() as $category): ?>
            <option value="<?= $category->getId() ?>"><?= $category->getName() ?></option>
        <?php endforeach; ?>
    </select>

    <table id="stocksTable"
           class="stocks-table data-grid"
           style="display:none; width: 100%;"
           data-mage-init='{ "EatLf_FranchiseInventory/js/view/grid" : {
                                "dataUrl": "<?= $block->getDataUrl() ?>",
                                "updateUrl": "<?= $block->getUpdateUrl() ?>",
                                "duplicateUrl": "<?= $block->getDuplicateUrl() ?>",
                                "dates": <?= $block->getJsonDates() ?>,
                                "nameFilterSelector": "#productNameFilter",
                                "tableWrapperSelector": "#stocksTableWrapper",
                                "categorySelector": "#categorySelect"
                           }}'>
        <thead>
            <tr>
                <th><!-- category --></th>
                <th><!-- sku --></th>
                <th>
                    <input id="productNameFilter"
                           class="product-filter"
                           placeholder="<?= __('Search by product name') ?>"   />
                </th>
                <?php foreach ($formattedDates as $date): ?>
                    <th colspan="3" class="date-header"><?= $date ?></th>
                <?php endforeach; ?>
            </tr>
            <tr class="duplicate-row">
                <th><!-- category --></th>
                <th><!-- sku --></th>
                <th><!-- product name --></th>
                <?php $x = 1; ?>
                <?php foreach ($formattedDates as $date): ?>
                    <?php if($nbCols == $x): ?>
                        <th class="duplicate-header duplicate-end"><!-- sales --></th>
                        <th class="duplicate-header duplicate-end"><!-- sales --></th>
                        <th class="duplicate-header duplicate-end"><!-- sales --></th>
                    <?php else: ?>
                        <th class="duplicate-header clickable"  data-date = "<?= $date ?>" data-type="init"><img src="<?= /* @escapeNotVerified */ $block->getViewFileUrl('EatLf_FranchiseInventory::images/dupliquer.png') ?>" width="48px"></th>
                        <th class="duplicate-header clickable" data-date = "<?= $date ?>" data-type="stock"><img src="<?= /* @escapeNotVerified */ $block->getViewFileUrl('EatLf_FranchiseInventory::images/dupliquer.png') ?>" width="48px"></th>
                        <th class="duplicate-header duplicate-end"><!-- sales --></th>
                    <?php endif; ?>
                <?php $x++; ?>
                <?php endforeach; ?>
            </tr>
            <tr>
                <th><!-- category --></th>
                <th><!-- sku --></th>
                <th><!-- product name --></th>
                <?php foreach ($formattedDates as $date): ?>
                    <th><?= __('Stock init') ?></th>
                    <th><?= __('Stock') ?></th>
                    <th><?= __('Sales') ?></th>
                <?php endforeach; ?>
            </tr>
        </thead>
    </table>
</div>

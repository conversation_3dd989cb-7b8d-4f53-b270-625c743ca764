<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="EatLf\Catalog\Controller\Hp\Refresh">
        <arguments>
            <argument name="catalogFilterPool" xsi:type="array">
                <item name="eatlf_inventory" xsi:type="object">Lf\CatalogInventory\Model\InventoryProductFilter</item>
            </argument>
        </arguments>
    </type>
</config>
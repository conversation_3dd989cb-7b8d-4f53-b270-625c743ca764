<?php
declare(strict_types=1);

namespace EatLf\Wallet\Controller\Adminhtml\Wallet;

use Magento\Backend\App\Action;
use Magento\Framework\View\Result\PageFactory;

class Newtransactionform extends Action
{
    /**
     * @var PageFactory
     */
    private $resultPageFactory;

    public function __construct(
        Action\Context $context,
        PageFactory $resultPageFactory
    )
    {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
    }

    public function execute()
    {
        $resultPage = $this->resultPageFactory->create();

        $resultPage->setActiveMenu('Ced_Wallet::wallet_transactions_add_new');
        $resultPage->getConfig()->getTitle()->prepend(__('Crédits / débits cagnotte'));

        return $resultPage;
    }


}

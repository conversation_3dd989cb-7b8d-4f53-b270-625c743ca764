<?php
declare(strict_types=1);

namespace EatLf\Wallet\Controller\Adminhtml\Wallet;

use Magento\Backend\App\Action;
use Magento\Framework\Controller\Result\JsonFactory;

class Lasttr extends Action
{
    /**
     * @var JsonFactory
     */
    private $jsonFactory;



    /**
     * @var \Ced\Wallet\Model\ResourceModel\Transaction\CollectionFactory
     */
    protected $transactionFactory;

    /**
     * @var \EatLf\Wallet\Model\Transaction\Attribute\Source\Type
     */
   protected $type;

    public function __construct(
        Action\Context $context,
        \Ced\Wallet\Model\ResourceModel\Transaction\CollectionFactory $transactionFactory,
        \EatLf\Wallet\Model\Transaction\Attribute\Source\Type $type,
        JsonFactory $jsonFactory
    )
    {
        parent::__construct($context);
        $this->jsonFactory = $jsonFactory;
        $this->transactionFactory = $transactionFactory;
        $this->type = $type;
    }

    public function execute()
    {
        $result = $this->jsonFactory->create();
        $montant = '';

        try {
            $data = $this->getRequest()->getParams();


            $customerId = isset($data['customer_id']) ? $data['customer_id'] : 0;
            if ($customerId) {
                $lastTransaction = $this->transactionFactory->create();
                $lastTransaction->addFieldToFilter('customer_id', $customerId)
                    ->addFieldToFilter('type', $this->type::TYPE3 )
                    ->addFieldToFilter('action', 0 )
                    ->getSelect()->order('created_at DESC')->limit(1);

                foreach($lastTransaction as $last){
                    $montant = $last->getTrAmount();
                }
            }else{
                throw new \Magento\Framework\Validator\Exception(__('User unknown.'));
            }

        } catch (\Exception $e) {
            $error = $e->getMessage();
            return $result->setData([
                'success' => false,
                'errors' => $error
            ]);
        }

        return $result->setData([
            'success' => true,
            'data' => $montant
        ]);
    }
}

<?php
declare(strict_types=1);

namespace EatLf\Wallet\Controller\Adminhtml\Wallet;

use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\Controller\Result\JsonFactory;

class Update extends Action
{
    /**
     * @var DailyStockRepositoryInterface
     */
    private $dailyStockRepository;
    /**
     * @var JsonFactory
     */
    private $jsonFactory;

    const WALLET_TRANSACTION_EMAIL_TEMPLATE = 'ced_wallet/active/mail_template_for_transaction';

    /**
     * @var \Ced\Wallet\Helper\Email
     */
    protected $email;

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    protected $customerFactory;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    protected $dateTime;

    /**
     * @var \Ced\Wallet\Model\TransactionFactory
     */
    protected $transactionFactory;

    /**
     * @var \Ced\Wallet\Helper\Data
     */
    protected $walletHelper;
    /**
     * @var FranchiseRepositoryInterface
     */
    private FranchiseRepositoryInterface $franchiseRepository;
    /**
     * @var Session
     */
    private Session $authSession;

    public function __construct(
        Action\Context                              $context,
        \Magento\Customer\Model\CustomerFactory     $customerFactory,
        \Ced\Wallet\Model\TransactionFactory        $transactionFactory,
        \Magento\Framework\Stdlib\DateTime\DateTime $dateTime,
        \Ced\Wallet\Helper\Data                     $walletHelper,
        \Ced\Wallet\Helper\Email                    $email,
        Session                                     $authSession,
        JsonFactory                                 $jsonFactory,
        FranchiseRepositoryInterface                $franchiseRepository
    ) {
        parent::__construct($context);
        $this->jsonFactory = $jsonFactory;
        $this->customerFactory = $customerFactory;
        $this->authSession = $authSession;
        $this->transactionFactory = $transactionFactory;
        $this->dateTime = $dateTime;
        $this->walletHelper = $walletHelper;
        $this->email = $email;
        $this->franchiseRepository = $franchiseRepository;
    }

    public function execute()
    {
        $result = $this->jsonFactory->create();

        try {
            $data = $this->getRequest()->getParams();

            $total = $data['total'];
            if (!is_numeric($total) || intval($total < 0)) {
                throw new \Magento\Framework\Validator\Exception(__('Montant non valide.'));
            }
            if($data['typet'] == 3){
                //check ticket resto
                $trmontant =  $data['trMontant'];
                if (!is_numeric($trmontant) || intval($trmontant <= 0)) {
                    throw new \Magento\Framework\Validator\Exception(__('Montant ticket restaurant invalide.'));
                }
                $trQty = $data['trQty'];
            }else{
                $trmontant = 0;
                $trQty = 0;
            }

            $customerId = isset($data['customer_id']) ? $data['customer_id'] : 0;
            if ($customerId) {
                $customer = $this->customerFactory->create()->load($customerId);
                $email = $customer->getEmail();
                $name = $customer->getFirstname();
            }else{
                throw new \Magento\Framework\Validator\Exception(__('User unknown.'));
            }

            if ($customer->getEnableWalletSystem() != 1) {
                //wallet system is disabled for customer
                throw new \Magento\Framework\Validator\Exception(__('Wallet is disabled for user.'));
            }

            $currentUser = $this->authSession->getUser();
            if ($currentUser->getFranchiseId() != "") {
                $existingKey = null;
                $amount = $data['total'];
                $walletAmount = $customer->getAmountWallet();

                $franchisesWalletAmounts = $customer->getFranchiseAmountWallet();

                $franchisewalletAmount = 0;
                if (is_array($franchisesWalletAmounts) && array_search($currentUser->getFranchiseId(), array_column($franchisesWalletAmounts,'franchise_id')) !== FALSE) {
                    $franchisewalletAmount = $franchisesWalletAmounts[array_search($currentUser->getFranchiseId(),
                                                                                   array_column($franchisesWalletAmounts,
                                                                                                'franchise_id'))]['wallet_amount'];
                    $existingKey = array_search($currentUser->getFranchiseId(),
                                                array_column($franchisesWalletAmounts,
                                                             'franchise_id'));
                }

                if ($data['typea'] == "credit") {
                    $total = $walletAmount + $amount;
                    $totalFranchise = $franchisewalletAmount + $amount;
                } else {
                    if ($franchisewalletAmount >= $amount) {
                        $total = $walletAmount - $amount;
                        $totalFranchise = $franchisewalletAmount - $amount;
                    } else {
                        throw new \Magento\Framework\Validator\Exception(__('Customer has not enough amount for deduction.'));
                    }
                }

                $customerData = $customer->getDataModel();
                $customerData->setCustomAttribute('amount_wallet',
                                                  $total);
                $franchisesWalletAmounts[$existingKey] = [
                    'franchise_id' => $currentUser->getFranchiseId(),
                    'wallet_amount' => $totalFranchise
                ];

                $customerData->setCustomAttribute('franchise_amount_wallet',
                                                  $franchisesWalletAmounts);
                $customer->updateData($customerData);
                $customer->save();
                $time = $this->dateTime;

                $currentTimestamp = $time->timestamp(time());
                $date = date('Y-m-d H:i:s', $currentTimestamp);

                $action = 0;
                if ($data['typea'] == "credit") {
                    $action = 0;
                }else{
                    $action = 1;
                }

                $transaction = $this->transactionFactory->create();
                $transaction->setData('action', $action); //0 for credit
                $transaction->setData('customer_id', $customerId);
                $transaction->setData('amount', $amount);
                $transaction->setData('comment', $data['comments']);
                $transaction->setData('type', $data['typet']);
                $transaction->setData('created_at', $date);
                $transaction->setData('franchise_id', $currentUser->getFranchiseId());
                $transaction->setData('tr_number', $trQty);
                $transaction->setData('tr_amount', $trmontant);
                $transaction->setData('order_id', "admin credits");

                $transaction->save();

                if ($data['typea'] == "credit") {
                    $trasactionMode = (string)__("Credited");
                }else {
                    $trasactionMode = (string)__("Debited");
                }

                $franchise = $this->franchiseRepository->getById($currentUser->getFranchiseId());

                $emailData = [
                    'transaction_mode' => $trasactionMode,
                    'amount' => $this->walletHelper->formatPrice($amount),
                    'comment' => $data['comments'],
                    'customer' => $customer,
                    'franchise' => $franchise
                ];
                $template = self::WALLET_TRANSACTION_EMAIL_TEMPLATE;
                $this->email->sendEmail($email, $name, $emailData, $template);
            }else{
                throw new \Magento\Framework\Validator\Exception(__('Unknown franchise.'));
            }
        } catch (\Exception $e) {
            $error = $e->getMessage();
            return $result->setData([
                                        'success' => false,
                                        'errors' => $error
                                    ]);
        }

        return $result->setData([
                                    'success' => true,
                                ]);
    }
}

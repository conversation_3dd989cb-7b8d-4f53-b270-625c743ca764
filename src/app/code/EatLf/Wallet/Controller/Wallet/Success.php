<?php
declare(strict_types=1);

namespace EatLf\Wallet\Controller\Wallet;

use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Lf\Franchise\Model\FranchiseContext;
use Lf\Systempay\Rewrite\Helper\Payment;
use Lyranetwork\Systempay\Helper\Rest;
use Lyranetwork\Systempay\Model\Api\Form\ResponseFactory;
use Lyranetwork\Systempay\Model\Api\SystempayResponseFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Controller\AbstractAccount;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\CsrfAwareActionInterface;
use Magento\Framework\App\Request\InvalidRequestException;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Message\ManagerInterface;
use Magento\Security\Model\Plugin\AuthSession;

class Success extends AbstractAccount implements CsrfAwareActionInterface
{
    const WALLET_TRANSACTION_EMAIL_TEMPLATE = 'ced_wallet/active/mail_template_for_transaction';

    /**
     * @var \Lyranetwork\Systempay\Helper\Rest
     */
    private $restHelper;
    /**
     * @var \Lf\Systempay\Rewrite\Helper\Payment
     */
    private $payment;
    /**
     * @var \Magento\Customer\Model\Session
     */
    private $customerSession;
    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    private $customerRepository;
    /**
     * @var \Lyranetwork\Systempay\Model\Api\Form\ResponseFactory
     */
    private ResponseFactory $responseFactory;
    private $jsonFactory;
    private CustomerFactory $customerFactory;
    private AuthSession $authSession;
    private Context $context;
    private \Magento\Framework\Stdlib\DateTime\DateTime $dateTime;
    private \Ced\Wallet\Model\TransactionFactory $transactionFactory;
    private FranchiseRepositoryInterface $franchiseRepository;
    private \Ced\Wallet\Helper\Email $email;

    private FranchiseContext $franchiseContext;

    public function __construct(
        Context                                     $context,
        Rest                                        $restHelper,
        Payment                                     $payment,
        Session                                     $customerSession,
        CustomerRepositoryInterface                 $customerRepository,
        ManagerInterface                            $messageManager,
        ResponseFactory                             $responseFactory,
        JsonFactory                                 $jsonFactory,
        CustomerFactory                             $customerFactory,
        AuthSession                                 $authSession,
        FranchiseContext                            $franchiseContext,
        \Magento\Framework\Stdlib\DateTime\DateTime $dateTime,
        \Ced\Wallet\Model\TransactionFactory        $transactionFactory,
        FranchiseRepositoryInterface                $franchiseRepository,
        \Ced\Wallet\Helper\Email                    $email
    ) {
        parent::__construct($context);

        $this->restHelper = $restHelper;
        $this->payment = $payment;
        $this->customerSession = $customerSession;
        $this->customerRepository = $customerRepository;
        $this->messageManager = $messageManager;
        $this->responseFactory = $responseFactory;
        $this->jsonFactory = $jsonFactory;
        $this->customerFactory = $customerFactory;
        $this->franchiseContext = $franchiseContext;

        $this->authSession = $authSession;
        $this->context = $context;
        $this->dateTime = $dateTime;
        $this->transactionFactory = $transactionFactory;
        $this->franchiseRepository = $franchiseRepository;
        $this->email = $email;
    }

    /**
     * @inheritDoc
     */
    public function execute()
    {
        $result = $this->resultRedirectFactory->create()->setPath('wallet/wallet/transaction');

        try {
            $data = $this->getRequest()->getParams();
            $answer = json_decode($data['kr-answer'], true);

            $total = (float)$answer['orderDetails']['orderTotalAmount'];

            $customerId = $this->customerSession->getCustomer()->getId();
            $customerFranchiseId = $answer['transactions']['0']['metadata']['franchise_id'];

            if (!$customerFranchiseId) {
                $this->messageManager->addErrorMessage(__('Missing required franchise parameter'));
                return $result;
            }

            try {
                $franchise = $this->franchiseRepository->getById($customerFranchiseId);
                $this->franchiseContext->setFranchise($franchise);
            } catch (NoSuchEntityException $e) {
                $this->messageManager->addErrorMessage(__('Erreur Technique inattendue'));
                return $result;
            }

            // Check response hmac validity to prevent spoofing
            if (!$this->restHelper->checkResponseHash($data, $this->restHelper->getReturnKey())) {
                $this->messageManager->addErrorMessage(__('Response hash not valid'));
                return $result;
            }

            try {
                $customer = $this->customerFactory->create()->load($customerId);
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage(__('Client inconnu'));
                return $result;
            }

            $email = $customer->getEmail();
            $name = $customer->getFirstname();

            try {
                if ($customer->getEnableWalletSystem() != 1) {
                    throw new \Magento\Framework\Validator\Exception(__('Wallet is disabled for user.'));
                }
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
                return $result;
            }

            $amount = (float)$total / 100;
            $existingKey = $customerFranchiseId;
            $walletAmount = $customer->getAmountWallet();

            $franchisesWalletAmounts = $customer->getFranchiseAmountWallet();

            $franchisewalletAmount = 0;
            if (is_array($franchisesWalletAmounts) && array_search($customerFranchiseId, array_column($franchisesWalletAmounts, 'franchise_id')) !== FALSE) {
                $franchisewalletAmount = $franchisesWalletAmounts[array_search(
                    $customerFranchiseId,
                    array_column(
                        $franchisesWalletAmounts,
                        'franchise_id'))]['wallet_amount'];
                $existingKey = array_search(
                    $customerFranchiseId,
                    array_column(
                        $franchisesWalletAmounts,
                        'franchise_id'));
            }

            $abundment = 0;
            if ($franchise->getMinimumAbundmentAmount() != '0' && $franchise->getAbundmentPercentAmount() != '0') {
                if ($amount > $franchise->getMinimumAbundmentAmount()) {
                    $abundment = ($amount * $franchise->getAbundmentPercentAmount()) / 100;
                }
            }

            $totalFranchise = $franchisewalletAmount + $abundment + $amount;
            $total = $walletAmount + $totalFranchise;

            $customerData = $customer->getDataModel();
            $customerData->setCustomAttribute(
                'amount_wallet',
                $total);
            $franchisesWalletAmounts[$existingKey] = [
                'franchise_id' => $customerFranchiseId,
                'wallet_amount' => $totalFranchise
            ];

            $customerData->setCustomAttribute('franchise_amount_wallet', $franchisesWalletAmounts);

            $customer->updateData($customerData);
            $customer->save();
            $time = $this->dateTime;

            $currentTimestamp = $time->timestamp(time());
            $date = date('Y-m-d H:i:s', $currentTimestamp);

            $transaction = $this->transactionFactory->create();
            $transaction->setData('action', 0); //0 for credit
            $transaction->setData('customer_id', $customerId);
            $transaction->setData('amount', $amount);
            $transaction->setData('comment', 'crédit réalisé par le client');
            $transaction->setData('type', 2);
            $transaction->setData('created_at', $date);
            $transaction->setData('franchise_id', $customerFranchiseId);
            $transaction->setData('tr_number', 1);
            $transaction->setData('tr_amount', $amount);
            $transaction->setData('order_id', "credits CB");

            $transaction->save();
            if ($abundment != 0) {
                $transaction = $this->transactionFactory->create();
                $transaction->setData('action', 0); //0 for credit
                $transaction->setData('customer_id', $customerId);
                $transaction->setData('amount', $abundment);
                $transaction->setData('comment', 'crédit offert');
                $transaction->setData('type', 8);
                $transaction->setData('created_at', $date);
                $transaction->setData('franchise_id', $customerFranchiseId);
                $transaction->setData('tr_number', 1);
                $transaction->setData('tr_amount', $amount);
                $transaction->setData('order_id', "credits CB");

                $transaction->save();
            }
            $franchise = $this->franchiseRepository->getById($customerFranchiseId);

            $emailData = [
                'transaction_mode' => "crédité",
                'amount' => (string)round($amount + $abundment, 2) . ' €',

                'customer' => $customer,
                'franchise' => $franchise
            ];
            $template = self::WALLET_TRANSACTION_EMAIL_TEMPLATE;
            $this->email->sendEmail($email, $name, $emailData, $template);
        } catch (\Exception $e) {
            $error = $e->getMessage();
            $this->messageManager->addComplexErrorMessage($error);

            return $result;
        }

        $this->messageManager->addSuccessMessage('Votre cagnotte a été créditée de ' . round($amount + $abundment, 2) . " €");
        return $result;
    }

    public function createCsrfValidationException(RequestInterface $request): ?InvalidRequestException
    {
        return null;
    }

    public function validateForCsrf(RequestInterface $request): ?bool
    {
        return true;
    }
}

<?php
declare(strict_types=1);

namespace EatLf\Wallet\Controller\Wallet;

use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Lf\Franchise\Model\FranchiseContext;
use Lf\Systempay\Rewrite\Model\Method\Standard;
use Magento\Customer\Controller\AbstractAccount;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;

class Token extends AbstractAccount
{
    private Standard $standard;

    private JsonFactory $jsonFactory;

    private FranchiseContext $franchiseContext;

    private FranchiseRepositoryInterface $franchiseRepository;

    public function __construct(
        Context                      $context,
        Standard                     $standard,
        JsonFactory                  $jsonFactory,
        FranchiseContext             $franchiseContext,
        FranchiseRepositoryInterface $franchiseRepository
    ) {
        parent::__construct($context);

        $this->standard = $standard;
        $this->jsonFactory = $jsonFactory;
        $this->franchiseContext = $franchiseContext;
        $this->franchiseRepository = $franchiseRepository;
    }

    public function execute()
    {
        $amount = $this->getRequest()->getParam('amount');
        $franchiseid = $this->getRequest()->getParam('franchiseid');

        $franchise = $this->franchiseRepository->getById($franchiseid);
        $this->franchiseContext->setFranchise($franchise);

        $amount = (float)$amount * 100;

        $token = $this->standard->getRestApiCreatePaymentToken((float)$amount, $franchiseid);

        return $this->jsonFactory->create()->setData([
                                                         'token' => $token
                                                     ]);
    }
}

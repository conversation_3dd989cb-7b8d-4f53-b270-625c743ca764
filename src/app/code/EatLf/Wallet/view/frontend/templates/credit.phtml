<?php
/**
 * @var EatLf\Wallet\Block\Credit $block
 * @var \Magento\Framework\Escaper $escaper
 */
$franchisesList = $block->getFranchisesList();
$selectedFranchise = $block->getFranchise()->getFranchiseId();

$tipsText = $block->getTipsText();
$explanatoryText = $block->getExplanatoryText();
$customText = $block->getPiggyText();
?>

<div class="block block-wallet-info">
    <div class="block-content">
        <div class="credit-wallet-block credit-wallet-left box box-wallet">
            <h3 class="box-title">
                <span>
                    <?= $escaper->escapeHtml(__('Créditer ma cagnotte par carte bancaire')) ?>
                </span>
            </h3>
            <br>
            <br>
            <div class="wallet-custom-box box-title">
                <?= /** @noEscape */
                $customText ?>
            </div>
            <div class="box-content wallet-form-container" data-bind="scope: 'systempayForm'">
                <form data-bind='mageInit: {"validation": {}}, afterRender: initAmountForm'>
                    <div>
                        <select id="franchise-select"
                                data-bind="value: selectedFranchise, event: { change: onFranchiseSelect }"
                                name="franchise-select"
                                class="kr-theme kr-on-error franchise-select">
                            <?php foreach ($franchisesList as $id => $name): ?>
                                <option value="<?= $escaper->escapeHtmlAttr($id) ?>"
                                        class="kr-theme"
                                    <?= ($id == $selectedFranchise) ? 'selected' : '' ?>>
                                    Cantine Digitale de <?= $escaper->escapeHtml($name) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <br>
                        <br>
                    </div>

                    <div class="kr-theme">
                        <input type="text"
                               data-bind="value: amount"
                               name="amount"
                               class="kr-theme input-text required wallet-customer-amount"
                               placeholder="<?= $escaper->escapeHtmlAttr(__('Amount to be credited')) ?>"/>
                    </div>
                </form>
                <br>
                <div id="systempay_rest_form"
                     class="kr-embedded"
                     data-bind="afterRender: initRestForm">
                    <div class="kr-pan" kr-tab-order="3"></div>
                    <div class="kr-expiry" kr-tab-order="4"></div>
                    <div class="kr-security-code" kr-tab-order="5"></div>
                    <div class="kr-card-holder-name" kr-tab-order="6"></div>
                    <div class="kr-form-error"></div>
                    <button class="kr-payment-button" style="display: none"></button>
                </div>

                <button type="button"
                        data-bind="click: onSaveClick, enable: canSave"

                        class="btn btn-8h action save primary">
                    <span><?= $escaper->escapeHtml(__('CREDITER MA CAGNOTTE')) ?></span>
                </button>
            </div>
        </div>
        <div class="credit-wallet-block credit-wallet-right box box-wallet">
            <h3 class="box-title"><span>Créditer ma cagnotte avec des tickets restaurants papier</span></h3>
            <br/>
            <?= /** @noEscape */
            $explanatoryText ?>
            <h3 class="box-title"><span>Astuce</span></h3>
            <div class="box-content">
                <?= /** @noEscape */
                $tipsText ?>
            </div>
        </div>
    </div>
</div>


<script type="text/x-magento-init">
    {
        "*": {
            "Magento_Ui/js/core/app": {
                "components": {
                    "systempayForm": {
                        "component": "EatLf_Wallet/js/wallet"
                    }
                }
            }
        }
    }
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        var amountInput = document.querySelector('input[name="amount"]');

        amountInput.addEventListener('input', function (event) {
            var inputValue = event.target.value;
            var filteredValue = inputValue.replace(/[.]/g, ',');
            filteredValue = filteredValue.replace(/[^0-9,]/g, '');
            var parts = filteredValue.split(',');
            if (parts[0].length > 5) {
                parts[0] = parts[0].slice(0, 5);
            }
            if (parts[1] && parts[1].length > 2) {
                parts[1] = parts[1].slice(0, 2);
            }

            event.target.value = parts.join(',');
        });
    });
</script>

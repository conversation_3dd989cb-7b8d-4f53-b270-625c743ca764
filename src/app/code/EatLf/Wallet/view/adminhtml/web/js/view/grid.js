define([
    'jquery',
    'underscore',
    'EatLf_FranchiseInventory/js/model/csv-button-builder',
    'EatLf_FranchiseInventory/js/model/qty-input-builder',
    'EatLf_FranchiseInventory/js/model/datatable-language',
    'mage/translate',
    'datatables.net',
    'datatables.net-buttonsHtml5',
    'datatables.net-fixedHeader',
    'jquery.loadingOverlay'
], function (
    $,
    _,
    csvButtonBuilder,
    qtyInputBuilder,
    datatableLanguage,
    $t
) {
    'use strict';

    var columns = [
        {data: 'id' },
        {data: 'partner', orderable: false},
        {data: 'name'},
        {data: 'email'},
        {data: 'ca', orderable: false},
        {data: 'cagnotte', orderable: false}
    ];

    var grid = {
        init: function (config, element) {
            this.config = config;
            this.element = element;
            this.setupTable();
            this.setupEmailFilter();
            this.setupEdit();
            this.setupPopin();

        },

        setupPopin: function() {
            var closeBtn = $(this.config.popinCloseSelector);
            var modal = $(this.config.popinSelector);
            var urlUpdate = this.config.updateUrl;
            var table = this;

            closeBtn.click(function() {
                modal.hide();
            });

            $("body").on("click", function(e){
                if ($(e.target).is(modal)) {
                    $(modal).hide();
                }
            });

            $("#popInputType").change(function(){
                $('#popInputTT').val('');
                $('#popInputComment').val('');
                if(this.value == "3"){
                    $('#popQTRs').show();
                    $('#popQTRn').hide();
                    $('#popTTCTRs').show();
                    $('#popTTCTRn').hide();
                    $('#popInputComment').prop("disabled", true );
                    $('#popInputTT').prop("disabled", true );

                }else{
                    $('#popQTRs').hide();
                    $('#popQTRn').show();
                    $('#popTTCTRs').hide();
                    $('#popTTCTRn').show();
                    $('#popInputComment').prop("disabled", false );
                    $('#popInputTT').prop("disabled", false );


                }
            });

            $("#popInputQTRselect").change(function(){
                var qty =  $("#popInputQTRselect").val();
                var montant = $("#popTTCTRv").val();
                montant = montant.replace(",",".");
                $("#popTTCTRv").val(montant);
                var total = qty * montant;
                $('#popInputTT').val(total);
                $('#popInputComment').val(qty+ ' tickets restaurants de '+ montant+' €');
            });
            $("#popTTCTRv").keyup(function(){
                var qty =  $("#popInputQTRselect").val();
                var montant = $("#popTTCTRv").val();
                montant = montant.replace(",",".");
                $("#popTTCTRv").val(montant);
                var total = qty * montant;
                $('#popInputTT').val(total);
                $('#popInputComment').val(qty+ ' ticket(s) restaurant de '+ montant+' €');
            });

            $('#submitPopin').click(function(){
                $('#submitPopin').prop("disabled", true);

                var typea = $('#popTypeS').val();
                var typet = $("#popInputType").val();
                var trQty = $("#popInputQTRselect").val();
                var trMontant = $("#popTTCTRv").val();
                var total = $('#popInputTT').val();
                var comments = $('#popInputComment').val();
                var customer_id = $('#customer_id').val();

                $.ajax({
                    method: "POST",
                    url:  urlUpdate,
                    cache: false,
                    data: {typea : typea, typet: typet, trQty: trQty, trMontant: trMontant, total: total, comments: comments, customer_id: customer_id}
                }).done(function(data) {
                    if(data['success']){
                        //fermeture de la popin et update du tableau
                        modal.hide();
                        table.table.ajax.reload();
                    }else{
                        //affichage des erreurs
                        $('#popinError').html(data['errors']);
                    }
                });
                $('#submitPopin').prop("disabled", false);
                setTimeout(function() { $('#submitPopin').focus(); }, 500);

            });
        },



        setupEdit: function() {
            var self = this;
            var table = this.table;
            var urlLastTr = this.config.lastTrUrl;
            var modal = $(this.config.popinSelector);

            table.on('dblclick', 'tbody td', function () {
                var cell = table.cell(this);
                var index = cell.index();
                var cagnotte = cell.table().data()[index.row]['cagnotte'];
                if(cagnotte === "" ||  cagnotte === null){
                    cagnotte = '0';
                }

                modal.show();

                $('#customer_id').val(cell.table().data()[index.row]['id']);
                $('#popName').html(cell.table().data()[index.row]['name']);
                $('#popEmail').html(cell.table().data()[index.row]['email']);
                $('#popPartenaire').html(cell.table().data()[index.row]['partner']);
                $('#popCA').html(cell.table().data()[index.row]['ca']);
                $('#popCagnotte').html(cagnotte);
                $('#popInputTT').val(null);
                $('#popInputComment').val(null);
                $('#popInputQTRselect').val(1);
                $('#popTTCTRv').val(0);
                $('#popinError').html(null);
                $("#popInputQTRselect").val(1);
                $("#popTypeS").val('credit');
                $('#popQTRs').hide();
                $('#popQTRn').show();
                $('#popTTCTRs').hide();
                $('#popTTCTRn').show();
                $('#popInputComment').prop("disabled", false );
                $('#popInputTT').prop("disabled", false );

                //setup TR
                $("#popInputType").val(3);
                $('#popQTRs').show();
                $('#popQTRn').hide();
                $('#popTTCTRs').show();
                $('#popTTCTRn').hide();
                $('#popInputComment').prop("disabled", true );
                $('#popInputTT').prop("disabled", true );

                //appel ajax pour le montant du dernier TR
                var customer_id = $('#customer_id').val();
                $.ajax({
                    method: "GET",
                    url:  urlLastTr,
                    cache: false,
                    data: {customer_id: customer_id}
                }).done(function(data) {
                    if(data['success']){
                        $('#popTTCTRv').val(data['data']);
                        var qty =  $("#popInputQTRselect").val();
                        var montant = $("#popTTCTRv").val();
                        montant.replace(",",".");
                        var total = qty * montant;
                        $('#popInputTT').val(total);
                        $('#popInputComment').val(qty+ ' tickets restaurants de '+ montant+' €');
                    }else{
                    }
                });

            });

        },

        setupTable: function () {
            var config = this.config;
            this.table = $(this.element).DataTable({
                ajax: this.config.dataUrl,
                serverSide: true,
                fixedHeader: true,
                language: datatableLanguage,
                dom: '<"table-header"Biplr>t',
                pagingType: 'simple',
                pageLength: 10,
                stripeClasses: ['data-row _odd-row', 'data-row'],
                columns: columns,
                buttons: [
                ],
                order: [[2, 'asc']] // name
            }).on('processing.dt', function (e, settings, processing) {
                if (processing) {
                    $(e.currentTarget).LoadingOverlay("show");
                } else {
                    $(e.currentTarget).LoadingOverlay("hide", true);
                    $(config.emailSelector).prop('disabled', false);
                    setTimeout(function() {$(config.emailSelector).focus();}, 100);
                }
            });

            $(this.element).show();

            $(this.config.tableWrapperSelector).prepend(
                this.table
                    .buttons(0, null)
                    .containers()
            );
        },

        setupEmailFilter: function () {
            var keyCodeValueAccepted = [56, 54, 59, 48, 8, 46];
            var table = this.table,
                config = this.config;
            var tampon = "";
            $(this.config.emailSelector).keyup(_.debounce(function (e) {
                var url = config.dataUrl;
                if ( (tampon!==this.value ||
                    ((e.keyCode >= 65 && e.keyCode <= 90) ||
                        $.inArray(e.keyCode,keyCodeValueAccepted)==0)
                    && (this.value !== '' && this.value.length >= 3))
                ){
                    $(this).prop('disabled', true);
                    tampon = this.value;
                    var queryString = this.value.replace('î','i').replace('ï','i');
                    queryString = queryString.replace('é','e').replace('è','e').replace('ê','e').replace('ë','e');
                    queryString = queryString.replace('ù','u').replace('ú','u').replace('û','u').replace('ü','u');
                    queryString = queryString.replace('à','a');
                    queryString = queryString.replace('ç','c');
                    queryString = queryString.replace('œ','oe');
                    url += 'filter/' + btoa(queryString);
                    table.ajax
                        .url(url)
                        .load();
                }
            }, 2000));
        },
    };

    return function (config, element) {
        $(function () {
            grid.init(config, element);
        });
    };
});

<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
         class="EatLf\Wallet\Rewrite\Listing\Listing">
    <columns name="spinner_columns">
        <column name="type_libelle">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Type</item>
                </item>
            </argument>
        </column>
    </columns>
</listing>

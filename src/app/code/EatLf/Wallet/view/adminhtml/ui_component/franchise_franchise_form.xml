<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="general">
        <settings>
            <label translate="true">Infos Compta</label>
        </settings>
        <field name="minimum_wallet_amount" sortOrder="170" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">minimum_wallet_amount</item>
                </item>
            </argument>
            <settings>
                <dataType>number</dataType>
                <label translate="true">Découvert autorisé</label>
                <dataScope>minimum_wallet_amount</dataScope>
                <notice translate="true">Fournir une valeur positive ( ex: 5 => on autorise jusqu'à 5€ de découvert)</notice>
                <validation>
                    <rule name="validate-not-negative-number" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="abundment_percent_amount" sortOrder="174" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">abundment_percent_amount</item>
                </item>
            </argument>
            <settings>
                <dataType>number</dataType>
                <label translate="true">Abondement cagnotte par CB, % abondé:</label>
                <dataScope>abundment_percent_amount</dataScope>
                <notice translate="true">Fournir une valeur positive ( ex: 5 => on autorise jusqu'à 5€ de découvert)</notice>
            </settings>
        </field>
        <field name="minimum_abundment_amount" sortOrder="173" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">minimum_abundment_amount</item>
                </item>
            </argument>
            <settings>
                <dataType>number</dataType>
                <label translate="true">Abondement cagnotte par CB, montant minimum:</label>
                <dataScope>minimum_abundment_amount</dataScope>
            </settings>
        </field>
    </fieldset>
</form>

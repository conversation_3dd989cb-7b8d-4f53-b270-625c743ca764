<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="transaction_form">
        <field name="amount">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Amount</item>
                </item>
            </argument>
        </field>
        <field name="comment">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Comment</item>
                </item>
            </argument>
        </field>
        <field name="action">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Action</item>
                </item>
            </argument>
        </field>
        <field name="type">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">EatLf\Wallet\Model\Transaction\Attribute\Source\Type</item>
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string">Type</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="default" xsi:type="string">3</item>
                    <item name="source" xsi:type="string">transaction_form</item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>
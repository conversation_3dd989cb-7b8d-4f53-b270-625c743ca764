<?php

namespace EatLf\Wallet\Block;

use Lf\Franchise\Model\Franchise;
use Lf\Franchise\Model\FranchiseContext;
use Lf\Franchise\Model\ResourceModel\Franchise\CollectionFactory as FranchiseCollectionFactory;
use Magento\Backend\Block\Widget\Context;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\View\Element\Template;
use Magento\Store\Model\ScopeInterface;

class Credit extends Template
{
    protected FranchiseCollectionFactory $franchiseCollectionFactory;

    private FranchiseContext $franchiseContext;

    private ScopeConfigInterface $scopeConfig;

    public function __construct(
        ScopeConfigInterface $scopeConfig,
        Context $context,
        FranchiseCollectionFactory $franchiseCollectionFactory,
        FranchiseContext $franchiseContext,
        array $data = []
    ) {
        parent::__construct($context, $data);

        $this->franchiseCollectionFactory = $franchiseCollectionFactory;
        $this->scopeConfig = $scopeConfig;
        $this->franchiseContext = $franchiseContext;
    }

    public function getPiggyText()
    {
        if ($this->getEatlfAbundmentPercent() == null && $this->getEatlfMinimumAbundment() == null) {
            return '';
        }
        if ($this->getEatlfAbundmentPercent() == "0" && $this->getEatlfMinimumAbundment() == "0") {
            return '';
        }

        $piggy_text = $this->scopeConfig->getValue(
            'ced_wallet/active/eatlf_contribution_text',
            ScopeInterface::SCOPE_STORE
        );

        $piggy_text = mb_strtoupper($piggy_text);
        $piggy_text = str_ireplace(
            ["%2", "%3", "%4"],
            [
                "<span style=\"color:#bda25a;\">" . $this->getEatlfAbundmentPercent() . "%" . "</span>",
                "<span style=\"color:#bda25a;\">" . $this->getEatlfMinimumAbundment() . "€" . "</span>",
                $this->franchiseContext->getFranchise()->getCode(),
            ],
            $piggy_text
        );

        return $piggy_text;
    }

    public function getEatlfAbundmentPercent()
    {
        return $this->getFranchise()->getAbundmentPercentAmount();
    }

    public function getFranchise()
    {
        return $this->franchiseContext->getFranchise();
    }

    public function getEatlfMinimumAbundment()
    {
        return $this->getFranchise()->getMinimumAbundmentAmount();
    }

    public function getFranchisesList(): array
    {
        $franchisesCollection = $this->franchiseCollectionFactory->create();
        $franchisesList = [];

        /**
         * @var Franchise $franchise
         */
        foreach ($franchisesCollection->getItems() as $franchise) {
            $franchisesList[$franchise->getId()] = $franchise->getCode();
        }

        return $franchisesList;
    }

    public function getTipsText()
    {
        return $this->scopeConfig->getValue(
            'ced_wallet/active/eat_lf_credit_tips_text',
            ScopeInterface::SCOPE_STORE
        );
    }

    public function getExplanatoryText()
    {
        return $this->scopeConfig->getValue(
            'ced_wallet/active/reload',
            ScopeInterface::SCOPE_STORE
        );
    }
}


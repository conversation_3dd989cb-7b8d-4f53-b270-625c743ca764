<?php
/**
 * @category  EatLf
 * @package   EatLf_Wallet
 * <AUTHOR> <<EMAIL>>
 * @Copyright 2020 Adin
 * @license   Apache License Version 2.0
 */

namespace EatLf\Wallet\Ui\Component\DataProvider;

use Magento\Backend\Model\Auth\Session;

use Magento\Framework\Data\Collection\Db\FetchStrategyInterface as FetchStrategy;
use Magento\Framework\Data\Collection\EntityFactoryInterface as EntityFactory;
use Magento\Framework\Event\ManagerInterface as EventManager;
use Psr\Log\LoggerInterface as Logger;
use EatLf\Wallet\Model\Transaction\Attribute\Source\Type as TransactionTypeSource;

class Transactions extends \Ced\Wallet\Ui\Component\DataProvider\Transactions {
    /**
     * @var Session
     */
    private $authSession;

    /**
     * @var TransactionTypeSource
     */
    private $transactionTypeSource;

    /**
     * @return \Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult|void
     */

    /**
     * Transactions constructor.
     * @param Session $authSession
     * @param EntityFactory $entityFactory
     * @param Logger $logger
     * @param FetchStrategy $fetchStrategy
     * @param TransactionTypeSource $transactionTypeSource
     * @param EventManager $eventManager
     * @param string $mainTable
     * @param string $resourceModel
     */
    public function __construct(
        Session $authSession,
        EntityFactory $entityFactory,
        Logger $logger,
        FetchStrategy $fetchStrategy,
        TransactionTypeSource $transactionTypeSource,
        EventManager $eventManager,
        string $mainTable = 'wallet_transaction',
        string $resourceModel = \Ced\Wallet\Model\ResourceModel\Transaction::class
    )
    {
        $this->authSession = $authSession;
        $this->transactionTypeSource = $transactionTypeSource;
        parent::__construct($entityFactory, $logger, $fetchStrategy, $eventManager, $mainTable, $resourceModel);
    }


    protected function _initSelect()
    {
        $this->addFilterToMap('created_at','main_table.created_at');
        parent::_initSelect();

        $currentUser = $this->authSession->getUser();


        $transactionTypeValues = $this->transactionTypeSource->toArray();

        $typeLibelleExpr = "CASE ";
        foreach($transactionTypeValues as $valueId => $valueLabel)
        {
            $typeLibelleExpr.="WHEN main_table.type=".$valueId." THEN '".$valueLabel."' ";
        }
        $typeLibelleExpr .= " END";

        $this->getSelect()->columns(['type_libelle' => new \Zend_Db_Expr($typeLibelleExpr)]);
        $this->addFilterToMap('type_libelle', new \Zend_Db_Expr($typeLibelleExpr));

        if($currentUser->getFranchiseId()!="")
        {
            $this->addFieldToFilter('main_table.franchise_id',$currentUser->getFranchiseId());
        }
    }


}

<?php

namespace EatLf\Wallet\Model\Transaction\Attribute\Source;

class Type implements \Magento\Framework\Option\ArrayInterface
{
    const TYPE1 = 1;
    const TYPE2 = 2;
    const TYPE3 = 3;
    const TYPE4 = 4;
    const TYPE5 = 5;
    const TYPE6 = 6;
    const TYPE7 = 7;
    const TYPE8 = 8;

    /**
     * Options array
     *
     * @var array
     */
    protected $_options;

    /**
     * Return options array
     * @return array
     */
    public function toOptionArray()
    {
        if (!$this->_options) {
            $this->_options = [
                ['label' => __('Clients chèques'), 'value' => self::TYPE1],
                ['label' => __('Clients CB'), 'value' => self::TYPE2],
                ['label' => __('Clients TR'), 'value' => self::TYPE3],
                ['label' => __('Clients virement'), 'value' => self::TYPE4],
                ['label' => __('Clients espèces'), 'value' => self::TYPE5],
                ['label' => __('Clients CB (VAD)'), 'value' => self::TYPE6],
                ['label' => __('Clients Edenred'), 'value' => self::TYPE7],
                ['label' => __('Compte publicité'), 'value' => self::TYPE8]
            ];
        }
        return $this->_options;
    }

    /**
     *
     * @return array
     */
    public function toArray()
    {
        $options = $this->toOptionArray();
        $values = [];
        foreach ($options as $option) {
            $values[$option['value']] = $option['label'];
        }
        return $values;
    }
}
<?php

declare(strict_types=1);

namespace EatLf\DialogInsightGraphQl\Model\Resolver;

use EatLf\DialogInsight\Model\ContactFields;
use Lf\DialogInsight\Model\Api\DialogInsightApiInterface;
use Lf\DialogInsight\Model\Api\Payload\MobileTokens;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Psr\Log\LoggerInterface;

class SetCustomerFcmToken implements ResolverInterface
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly DialogInsightApiInterface $api,
        private readonly CustomerRepositoryInterface $customerRepository
    ) {
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }

        $userId = $context->getUserId();

        $fcmToken = $args['token'];
        $applicationId = $args['applicationId'];

        $this->logger->debug("Firebase - customer id $userId - token $fcmToken");

        $customer = $this->customerRepository->getById($userId);

        $tokenMessage = new MobileTokens();
        $tokenMessage->setApplicationId($applicationId);
        $tokenMessage->setTokenByIdField(ContactFields::EMAIL->value, $customer->getEmail(), $fcmToken);

        $this->api->mergeFirebaseTokens($tokenMessage);

        return true;
    }
}

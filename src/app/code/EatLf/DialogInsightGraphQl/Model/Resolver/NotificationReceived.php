<?php

declare(strict_types=1);

namespace EatLf\DialogInsightGraphQl\Model\Resolver;

use Lf\DialogInsight\Model\Api\DialogInsightApiInterface;
use Lf\DialogInsight\Model\Api\Payload\TrackingData;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class NotificationReceived implements ResolverInterface
{
    public function __construct(private readonly DialogInsightApiInterface $api)
    {
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        $trackingData = new TrackingData();

        $trackingData->setApplicationId($args['applicationId'])
            ->addNotification($args['messageId']);

        $this->api->setTrackingData($trackingData);

        return true;
    }
}

type Mutation {
    setCustomerFcmToken (token: String!, applicationId: String!): <PERSON><PERSON><PERSON> @resolver(class: "EatLf\\DialogInsightGraphQl\\Model\\Resolver\\SetCustomerFcmToken") @doc(description: "Attach firebase cloud messaging token to current customer") @cache(cacheable: false)
    notificationReceived(messageId: String!, applicationId: String!): <PERSON><PERSON><PERSON> @resolver(class: "EatLf\\DialogInsightGraphQl\\Model\\Resolver\\NotificationReceived") @doc(description: "Notification received event") @cache(cacheable: false)
}


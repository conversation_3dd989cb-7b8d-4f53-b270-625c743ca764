<?php
declare(strict_types=1);

namespace EatLf\CheckoutInventory\Observer;

use EatLf\CheckoutInventory\Model\CartManagement;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class FilterInvalidDate implements ObserverInterface
{
    /**
     * @var \EatLf\CheckoutInventory\Model\CartManagement
     */
    private $cartManagement;

    public function __construct(
        CartManagement $cartManagement
    ) {
        $this->cartManagement = $cartManagement;
    }

    public function execute(Observer $observer)
    {
        $transport = $observer->getData('transport');
        $shippingData = $observer->getData('shippingData');

        $transport->addData(
            $this->cartManagement->validateDate($shippingData)
        );
    }
}
<?php
declare(strict_types=1);

namespace EatLf\CheckoutInventory\Model;

use EatLf\Shipping\Api\ShippingDateManagementInterface;
use Lf\Opc\Api\Data\ShippingDatasInterface;
use Magento\Checkout\Model\Session;
use Magento\Quote\Api\CartRepositoryInterface;

class CartManagement
{
    /**
     * @var ShippingDateManagementInterface
     */
    private $shippingDateManagement;
    /**
     * @var CartRepositoryInterface
     */
    private $cartRepository;
    /**
     * @var Session
     */
    private $checkoutSession;

    public function __construct(
        ShippingDateManagementInterface $shippingDateManagement,
        CartRepositoryInterface $cartRepository,
        Session $checkoutSession
    )
    {
        $this->shippingDateManagement = $shippingDateManagement;
        $this->cartRepository = $cartRepository;
        $this->checkoutSession = $checkoutSession;
    }

    public function validateDate(ShippingDatasInterface $shippingData): array
    {
        try {
            $shippingDate = new \DateTime($shippingData->getShippingDate());

            $isDateValid = $this->shippingDateManagement->isValid(
                $shippingDate,
                $shippingData->getExtensionAttributes()->getPartenaireId()
            );

            if (!$isDateValid) {
                $quote = $this->checkoutSession->getQuote()->setIsActive(false);
                $this->cartRepository->save($quote);
                $this->checkoutSession->clearQuote();

                return [
                    'dateInvalid' => true
                ];
            }
        } catch (\Exception $e) {
            return [];
        }

        return [];
    }
}
<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="lf_checkoutinventory_update_after">
        <observer instance="EatLf\CheckoutInventory\Observer\FilterInvalidDate"
                  name="eatlf_checkoutinventory_update_date"/>
    </event>

    <event name="lf_checkoutinventory_validate_after">
        <observer instance="EatLf\CheckoutInventory\Observer\FilterInvalidDate"
                  name="eatlf_checkoutinventory_validate_date"/>
    </event>
</config>

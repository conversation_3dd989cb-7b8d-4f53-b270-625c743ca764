<?php

namespace Lf\Customer\Block\Adminhtml\Edit\Tab;

use Magento\Backend\Block\Template\Context;
use Magento\Framework\AuthorizationInterface;
use Magento\Framework\Registry;

class Reviews extends \Magento\Review\Block\Adminhtml\ReviewTab
{
    /**
     * @var AuthorizationInterface
     */
    private $authorization;

    /**
     * Reviews constructor.
     * @param Context                $context
     * @param Registry               $registry
     * @param AuthorizationInterface $authorization
     * @param array                  $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        AuthorizationInterface $authorization,
        array $data = []
    ) {
        parent::__construct($context, $registry, $data);
        $this->authorization = $authorization;
    }

    public function canShowTab()
    {
        if (!$this->_isAllowedAction('Magento_Customer::customer_magento_options'))
            return false;
        else
            return parent::canShowTab();
    }

    /**
     * Check permission for passed action
     *
     * @param string $resourceId
     * @return bool
     */
    protected function _isAllowedAction($resourceId)
    {
        return $this->authorization->isAllowed($resourceId);
    }
}
<?php
declare(strict_types=1);

namespace Lf\Customer\Plugin;

use Magento\Customer\Api\Data\GroupInterface;
use Magento\Customer\Api\GroupRepositoryInterface;
use \Magento\Customer\Model\GroupRegistry;

class GroupFranchiseGet
{

    /**
     * @var \Magento\Customer\Model\GroupRegistry
     */
    protected $groupRegistry;

    public function __construct(
        GroupRegistry $groupRegistry
    ){
        $this->groupRegistry = $groupRegistry;
    }

    /**
     * Copies franchise_id to extension attributes
     *
     * @param GroupRepositoryInterface $groupRepository
     * @param GroupInterface $group
     * @return OrderInterface
     */
    public function afterGetById(GroupRepositoryInterface $groupRepository,GroupInterface $group)
    {
        $groupExtension = $group->getExtensionAttributes();

        if ($groupExtension === null) {
            return $group;
        }

        $groupModel = $this->groupRegistry->retrieve($group->getId());
        $groupExtension->setFranchiseIds($groupModel->getFranchiseIds());

        return $group;
    }
}

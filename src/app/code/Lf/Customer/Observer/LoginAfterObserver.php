<?php

namespace Lf\Customer\Observer;

use Lf\Customer\Model\Customer\Cookies\Name;
use Magento\Framework\Event\ObserverInterface;


class LoginAfterObserver implements ObserverInterface
{

    /**
     * @var Name
     */
    protected $customerNameCookie;

    /**
     * @param \Magento\Framework\DataObject\Copy $objectCopyService
    ...
     */
    public function __construct(
        Name $customerNameCookie
    ) {
        $this->customerNameCookie = $customerNameCookie;
    }


    /**
     * Set gift messages to order from quote address
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return $this
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /** @var \Magento\Customer\Model\Customer $model */
        $model = $observer->getModel();

        $this->customerNameCookie->set($model->getFirstname());

        return $this;
    }
}

<?php

namespace Lf\Customer\Model;


use DateInterval;
use DateTime;
use Exception;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Lf\Sales\Model\ResourceModel\Order\Grid\CollectionFactory as OrderCollection;
use Ma<PERSON>o\Customer\Api\CustomerRepositoryInterface;


class UpdateCustomerCA {

    /**
     * @var OrderCollection $orderCollection
     */
    private OrderCollection $orderCollection;
    /**
     * @var TimezoneInterface $date
     */
    private TimezoneInterface $date;
    /**
     * @var CustomerRepositoryInterface $customerRepository
     */
    private CustomerRepositoryInterface $customerRepository;
    /**
     * array $customerIds
     */
    private array $customerIds;

    public function __construct(
        OrderCollection $orderCollection,
        TimezoneInterface $date,
        CustomerRepositoryInterface $customerRepository
    ) {
        $this->orderCollection = $orderCollection;
        $this->date = $date;
        $this->customerRepository = $customerRepository;
        $this->customerIds = [];
    }

    /**
     * do it
     * @throws Exception
     */
    public function execute()
    {
        $this->initCustomerFilter();
        $this->updateYearlyCA();
        $this->updateAllTimeCA();
    }

    /**
     * @throws Exception
     */
    public function reinitAll()
    {
        $this->updateYearlyCA();
        $this->updateAllTimeCA();
    }

    /**
     *
     */
    private function initCustomerFilter()
    {
        $twoDaysAgo = date("Y-m-d", strtotime("-2 day"));
        $orderCollection = $this->orderCollection->create();
        $orderCollection->addFieldToFilter('updated_at', array('gteq' => $twoDaysAgo));

        $ids = [];
        foreach($orderCollection as $item) {
            $ids[$item->getCustomerId()] = $item->getCustomerId();
        }
        $this->customerIds = $ids;
    }

    /**
     * @return bool
     */
    private function updateAllTimeCA(): bool
    {
        //récupération de toutes les commandes depuis 1 an en complete
        $orderCollection = $this->orderCollection->create();
        $orderCollection->addFieldToFilter('status', array('eq' => 'complete'))
            ->addFieldToFilter('total_refunded', array('null' => true))
            ->addFieldToSelect('customer_id')
            ->addFieldToSelect('grand_total')
            ->addFieldToSelect('franchise_id');
        if (count($this->customerIds) > 0) {
            $orderCollection->addFieldToFilter('customer_id', array('in' => $this->customerIds));
        }

        $data = [];
        foreach ($orderCollection as $item) {
            if (!isset($data[$item->getCustomerId()][$item->getFranchiseId()])) {
                $data[$item->getCustomerId()][$item->getFranchiseId()]['customer_id'] = $item->getCustomerId();
                $data[$item->getCustomerId()][$item->getFranchiseId()]['total_ca_ttc'] = 0;
                $data[$item->getCustomerId()][$item->getFranchiseId()]['nb_commande'] = 0;
            }
            $data[$item->getCustomerId()][$item->getFranchiseId()]['total_ca_ttc'] += $item->getData('grand_total');
            $data[$item->getCustomerId()][$item->getFranchiseId()]['nb_commande'] += 1;
        }

        //sauvegarde des données
        foreach($data as $customerId => $franchise){
            $totalCaTTC = [];
            $totalNbCommande = [];
            foreach($franchise as $franchise_id =>  $line){
                $customerId = $line['customer_id'];
                $totalCaTTC[$franchise_id] = [
                    'franchise_id' => $franchise_id,
                    'value' => $line['total_ca_ttc']
                ];
                $totalNbCommande[$franchise_id] = [
                    'franchise_id' => $franchise_id,
                    'value' => $line['nb_commande']
                ];
            }
            try {
                $customer = $this->customerRepository->getById($customerId);
                $customer->setCustomAttribute('total_ca_ttc',$totalCaTTC);
                $customer->setCustomAttribute('nombre_commandes_all',$totalNbCommande);
                $this->customerRepository->save($customer);
            } catch (NoSuchEntityException $e) {
            }catch(Exception $e){
            }
        }
        return true;
    }

    /**
     * @return bool
     * @throws Exception
     */
    private function updateYearlyCA(): bool
    {
        //récupération de toutes les commandes depuis 1 an en complete
        $orderCollection = $this->orderCollection->create();
        $oneYearAgo = $this->date->date()->sub(new DateInterval('P1Y'))->format('Y-m-d');

        $orderCollection->addFieldToFilter('shipping_date', array('gteq' => $oneYearAgo ))
            ->addFieldToFilter('status', array('eq' => 'complete'))
            ->addFieldToSelect('customer_id')
            ->addFieldToSelect('totalHT')
            ->addFieldToSelect('paid')
            ->addFieldToSelect('franchise_id')
            ->addFieldToSelect('shipping_date');

        if (count($this->customerIds) > 0) {
            $orderCollection->addFieldToFilter('customer_id', array('in' => $this->customerIds));
        }

        $data = array();
        $now = new DateTime();
        foreach ($orderCollection->getItems() as $item) {
            if(!isset($data[$item->getCustomerId()][$item->getFranchiseId()])){
                $data[$item->getCustomerId()][$item->getFranchiseId()]['nombre_commande'] = 0;
                $data[$item->getCustomerId()][$item->getFranchiseId()]['total_encours'] = 0;
                $data[$item->getCustomerId()][$item->getFranchiseId()]['total_ca'] = 0;
                $data[$item->getCustomerId()][$item->getFranchiseId()]['commande_impayee'] = 0;
                $data[$item->getCustomerId()][$item->getFranchiseId()]['customer_id'] = $item->getCustomerId();
            }
            $data[$item->getCustomerId()][$item->getFranchiseId()]['nombre_commande'] += 1;
            if($item->getData('paid') == 'Non' && $item->getData('totalHT') > 0) {
                $data[$item->getCustomerId()][$item->getFranchiseId()]['total_encours'] += $item->getData('totalHT');

                $dateCmd = new DateTime($item->getShippingDate());
                $diff = $now->diff($dateCmd)->format("%a");
                if($diff >= 60){
                    $data[$item->getCustomerId()][$item->getFranchiseId()]['commande_impayee'] = 1;
                }
            }
            $data[$item->getCustomerId()][$item->getFranchiseId()]['total_ca'] += $item->getData('totalHT');
        }

        //sauvegarde des données
        foreach($data as $customerId => $franchise){
            $totalCa = [];
            $totalEncours = [];
            $nombreCommandes = [];
            $commandeImpayee = [];
            foreach($franchise as $franchise_id =>  $line){
                $customerId = $line['customer_id'];
                $totalCa[$franchise_id] = [
                    'franchise_id' => $franchise_id,
                    'value' => $line['total_ca']
                ];
                $totalEncours[$franchise_id] = [
                    'franchise_id' => $franchise_id,
                    'value' => $line['total_encours']
                ];
                $nombreCommandes[$franchise_id] = [
                    'franchise_id' => $franchise_id,
                    'value' => $line['nombre_commande']
                ];
                $commandeImpayee[$franchise_id] = [
                    'franchise_id' => $franchise_id,
                    'value' => $line['commande_impayee']
                ];
            }

            try {
                $customer = $this->customerRepository->getById($customerId);
                $customer->setCustomAttribute('total_ca',$totalCa);
                $customer->setCustomAttribute('total_encours',$totalEncours);
                $customer->setCustomAttribute('nombre_commandes',$nombreCommandes);
                $customer->setCustomAttribute('commande_impayee',$commandeImpayee);
                $this->customerRepository->save($customer);
            } catch (NoSuchEntityException $e) {

            }catch(Exception $e){

            }
        }
        return true;
    }

}

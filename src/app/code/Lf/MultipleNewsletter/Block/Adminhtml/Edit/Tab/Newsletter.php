<?php

namespace Lf\MultipleNewsletter\Block\Adminhtml\Edit\Tab;

use Lf\MultipleNewsletter\Block\Form\Options;
use Magento\Backend\Block\Template\Context;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Controller\RegistryConstants;
use Magento\Customer\Model\Config\Share;
use Magento\Framework\AuthorizationInterface;
use Magento\Framework\Data\Form\Element\Fieldset;
use Magento\Framework\Data\FormFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Registry;
use Magento\Newsletter\Model\SubscriberFactory;
use Magento\Store\Model\System\Store as SystemStore;

class Newsletter extends \Magento\Customer\Block\Adminhtml\Edit\Tab\Newsletter
{
    /**
     * @var AuthorizationInterface
     */
    private $authorization;

    private Share $shareConfig;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;
    private Options $options;

    public function __construct(
        Context $context,
        Registry $registry,
        FormFactory $formFactory,
        SubscriberFactory $subscriberFactory,
        AccountManagementInterface $customerAccountManagement,
        SystemStore $systemStore,
        CustomerRepositoryInterface $customerRepository,
        Share $shareConfig,
        AuthorizationInterface $authorization,
        Options $options,
        array $data = []
    ) {
        parent::__construct($context, $registry, $formFactory, $subscriberFactory, $customerAccountManagement, $systemStore, $customerRepository, $shareConfig, $data);
        $this->authorization = $authorization;
        $this->options = $options;
        $this->shareConfig = $shareConfig;
        $this->customerRepository = $customerRepository;
    }

    /**
     * Can show tab in tabs
     *
     * @return boolean
     */
    public function canShowTab()
    {
        if (!$this->_isAllowedAction('Magento_Customer::customer_magento_options'))
            return false;
        else
            return parent::canShowTab();
    }

    /**
     * Check permission for passed action
     *
     * @param string $resourceId
     * @return bool
     */
    protected function _isAllowedAction($resourceId)
    {
        return $this->authorization->isAllowed($resourceId);
    }

    /**
     * Prepare form fields for single website mode
     *
     * @param Fieldset $fieldset
     * @return void
     */
    public function prepareFormSingleWebsite(Fieldset $fieldset): void
    {
        $newsletterOptions = $this->options->getNewsletterOptions();
        $customer = $this->getCurrentCustomer();
        if($customer->getCustomAttribute('newsletter_options')==null) $currentlySubscribe = false;
        else $currentlySubscribe = $customer->getCustomAttribute('newsletter_options')->getValue();
        $currentlySubscribe = explode(",", $currentlySubscribe);
        if(count($currentlySubscribe) == 1) $currentlySubscribe[] = 0;

        if (isset($newsletterOptions[0]['label'])) {
            $fieldset->addField(
                'newsletter_options_' . 0,
                'checkbox',
                [
                    'label' => $newsletterOptions[0]['label'],
                    'name' => "newsletter_options[". $newsletterOptions[0]['code'] ."]",
                    'data-form-part' => $this->getData('target_form'),
                    'value' => 0,
                    'checked' => $currentlySubscribe[0] == $newsletterOptions[0]['code'] || $currentlySubscribe[1] == $newsletterOptions[0]['code'] || $currentlySubscribe[0] === 'all',
                    'onchange' => 'this.value = this.checked;',
                ]
            );
        }

        if (isset($newsletterOptions[1]['label'])) {
            $fieldset->addField(
                'newsletter_options_' . 1,
                'checkbox',
                [
                    'label' => $newsletterOptions[1]['label'],
                    'name' => "newsletter_options[". $newsletterOptions[1]['code'] ."]",
                    'data-form-part' => $this->getData('target_form'),
                    'value' => 0,
                    'checked' => $currentlySubscribe[0] == $newsletterOptions[1]['code'] || $currentlySubscribe[1] == $newsletterOptions[1]['code'] || $currentlySubscribe[0] === 'all',
                    'onchange' => 'this.value = this.checked;',
                ]
            );
        }
    }

    /**
     * Is single systemStore mode
     *
     * @return bool
     */
    private function isSingleStoreMode(): bool
    {
        return $this->_storeManager->isSingleStoreMode();
    }

    /**
     * Is single website mode
     *
     * @return bool
     */
    private function isSingleWebsiteMode(): bool
    {
        return $this->isSingleStoreMode()
            || !$this->shareConfig->isGlobalScope()
            || count($this->_storeManager->getWebsites()) === 1;
    }

    /**
     * Get Customer Subscriptions on Websites
     *
     * @return array
     */
    private function getCustomerSubscriptionsOnWebsites(): array
    {
        return $this->options->getNewsletterOptions();
    }

    /**
     * Get current customer model
     *
     * @return CustomerInterface|null
     */
    private function getCurrentCustomer(): ?CustomerInterface
    {
        $customerId = $this->getCurrentCustomerId();
        try {
            $customer = $this->customerRepository->getById($customerId);
        } catch (NoSuchEntityException $e) {
            return null;
        }

        return $customer;
    }

    /**
     * Get current customer id
     *
     * @return int
     */
    private function getCurrentCustomerId(): int
    {
        return (int)$this->_coreRegistry->registry(RegistryConstants::CURRENT_CUSTOMER_ID);
    }

    /**
     * Init form values
     *
     * @return \Magento\Customer\Block\Adminhtml\Edit\Tab\Newsletter
     */
    public function initForm()
    {

        if (!$this->canShowTab()) {
            return $this;
        }

        $form = $this->_formFactory->create();
        $form->setHtmlIdPrefix('_newsletter');
        $this->setForm($form);
        $fieldset = $form->addFieldset(
            'base_fieldset',
            [
                'legend' => __('Newsletter Information'),
                'class' => 'customer-newsletter-fieldset' . (!$this->isSingleWebsiteMode() ? ' multi-website' : ''),
            ]
        );

        $customerSubscriptions = $this->options->getNewsletterOptions();

        if (empty($customerSubscriptions)) {
            return $this;
        }

        if ($this->isSingleWebsiteMode()) {
            $this->prepareFormSingleWebsite($fieldset, $customerSubscriptions, $this->getCurrentCustomerId());
            $this->updateFromSession($form, $this->getCurrentCustomerId());
        } else {
            $this->prepareFormMultiplyWebsite($fieldset, $customerSubscriptions);
        }

        if ($this->customerAccountManagement->isReadonly($this->getCurrentCustomerId())) {
            $fieldset->setReadonly(true, true);
        }

        return $this;
    }

}

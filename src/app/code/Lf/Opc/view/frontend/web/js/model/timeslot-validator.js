define([
    'jquery',
    'Lf_ShippingBar/js/model/shippingDelay',
    'uiRegistry'
], function ($, shippingDelay, registry) {
    'use strict';

    return {
        validate: function () {
            if (!shippingDelay.isCreneauDepasse()) {
                return true;
            }

            registry.async('checkout.shipping-expired-popin')(function (shippingExpiredPopin) {
                shippingExpiredPopin.showModal();
            });

            return false;
        }
    }
});
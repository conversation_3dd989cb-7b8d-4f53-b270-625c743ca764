<?php

namespace Lf\Opc\Observer;

use Magento\Framework\Event\ObserverInterface;


class SetItemCustomAttributeObserver implements ObserverInterface
{
    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $quoteItem = $observer->getQuoteItem();
        $product = $observer->getProduct();
        $quoteItem->setLibelleBonLivraison($product->getData('libelle_bon_livraison'));
    }
}

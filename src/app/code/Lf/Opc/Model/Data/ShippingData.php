<?php

namespace Lf\Opc\Model\Data;

use Lf\Opc\Api\Data\ShippingDatasExtensionInterface;
use Lf\Opc\Api\Data\ShippingDatasInterface;
use Lf\Opc\Model\ShippingModel;
use Magento\Framework\Model\AbstractExtensibleModel;

class ShippingData extends AbstractExtensibleModel implements ShippingDatasInterface
{
    /**
     * @inheritdoc
     */
    public function getShippingDate()
    {
        return $this->getData(ShippingModel::SHIPPING_DATE);
    }

    /**
     * @inheritdoc
     */
    public function setShippingDate($date)
    {
        if ($date instanceof \DateTime) {
            $this->setData(ShippingModel::SHIPPING_DATE, $date->format('Y-m-d'));
        } else {
            $this->setData(ShippingModel::SHIPPING_DATE, $date);
        }

        return $this;
    }

    /**
     * @inheritdoc
     */
    public function getTimeslotId()
    {
        return $this->getData(ShippingModel::TIMESLOT_ID);
    }

    /**
     * @inheritdoc
     */
    public function setTimeslotId($timeslotId)
    {
        $this->setData(ShippingModel::TIMESLOT_ID, $timeslotId);

        return $this;
    }

    /**
     * @inheritdoc
     */
    public function getZone()
    {
        return $this->getData(ShippingModel::ZONE);
    }

    /**
     * @inheritdoc
     */
    public function setZone($zone)
    {
        $this->setData(ShippingModel::ZONE, $zone);

        return $this;
    }

    /**
     * @return string
     */
    public function getReference()
    {
        return $this->getData(ShippingModel::REFERENCE);
    }

    /**
     * @param $reference
     * @return string
     */
    public function setReference($reference)
    {
        $this->setData(ShippingModel::REFERENCE, $reference);

        return $this;
    }

    /**
     * @return self
     */
    public function getComment()
    {
        return $this->getData(ShippingModel::COMMENT);
    }

    /**
     * @param $comment
     * @return string
     */
    public function setComment($comment)
    {
        $this->setData(ShippingModel::COMMENT, $comment);

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getGoogleZoneRecoveryDatetime()
    {
        return $this->getData(ShippingModel::GOOGLE_ZONE_RECOVERY_DATETIME);
    }

    /**
     * @inheritDoc
     */
    public function setGoogleZoneRecoveryDatetime($datetime)
    {
        $this->setData(ShippingModel::GOOGLE_ZONE_RECOVERY_DATETIME, $datetime);

        return $this;
    }


    /**
     * Retrieve Franchise Id
     *
     * @return string
     */
    public function getFranchiseId()
    {
        return $this->getData(ShippingModel::FRANCHISE_ID);
    }

    /**
     * Set Franchise Id
     *
     * @param string $franchiseId
     * @return self
     */
    public function setFranchiseId($franchiseId)
    {
        $this->setData(ShippingModel::FRANCHISE_ID, $franchiseId);

        return $this;
    }

    /**
     * Retrieve shipping amount
     *
     * @return double
     */
    public function getShippingAmount()
    {
        return $this->getData(ShippingModel::SHIPPING_AMOUNT);
    }

    /**
     * Set shipping amount
     *
     * @param double $shippingAmount
     * @return self
     */
    public function setShippingAmount($shippingAmount)
    {
        $this->setData(ShippingModel::SHIPPING_AMOUNT, $shippingAmount);

        return $this;
    }

    /**
     * {@inheritDoc}
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * {@inheritDoc}
     */
    public function setExtensionAttributes(ShippingDatasExtensionInterface $extensionAttributes)
    {
        $this->_setExtensionAttributes($extensionAttributes);

        return $this;
    }
}
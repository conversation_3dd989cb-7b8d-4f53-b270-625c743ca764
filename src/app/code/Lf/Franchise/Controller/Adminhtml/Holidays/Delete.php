<?php

namespace Lf\Franchise\Controller\Adminhtml\Holidays;

class Delete extends \Lf\Franchise\Controller\Adminhtml\Franchise
{
    /**
     * Authorization level of a basic admin session
     *
     * @see _isAllowed()
     */
    const ADMIN_RESOURCE = 'Lf_Franchise::holidays';
    /**
     * Delete action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        // check if we know what should be deleted
        $id = $this->getRequest()->getParam('holidays_id');
        if ($id) {
            try {
                // init model and delete
                $model = $this->_objectManager->create(\Lf\Franchise\Model\Holidays::class);
                $model->load($id);
                $model->delete();
                // display success message
                $this->messageManager->addSuccess(__('You deleted the holidays.'));
                // go to grid
                return $resultRedirect->setPath('franchise/franchise/holidays');
            } catch (\Exception $e) {
                // display error message
                $this->messageManager->addError($e->getMessage());
                // go back to edit form
                return $resultRedirect->setPath('*/*/edit', ['holidays_id' => $id]);
            }
        }
        // display error message
        $this->messageManager->addError(__('We can\'t find a holidays to delete.'));
        // go to grid
        return $resultRedirect->setPath('franchise/franchise/holidays');
    }
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed(self::ADMIN_RESOURCE);
    }
}

<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="timeslot_grid" resource="default" engine="innodb" comment="Timeslot Grid">
        <column xsi:type="int" name="timeslot_id" unsigned="true" nullable="false" identity="false"
                comment="timeslot ID"/>

        <column xsi:type="varchar" name="timeslot_type_name" length="50" nullable="false"
                comment="timeslot TYPE"/>

        <column xsi:type="int" name="is_enable" unsigned="true" nullable="false"
                comment="enable"/>

        <column xsi:type="double" name="start_hour" unsigned="true" nullable="false"
                comment="start hour"/>

        <column xsi:type="double" name="end_hour" unsigned="true" nullable="false"
                comment="end hour"/>

        <column xsi:type="double" name="preparation_time" unsigned="true" nullable="true"
                comment="preparation time"/>

        <column xsi:type="int" name="is_retention" unsigned="true" nullable="false"
                comment="retention"/>

        <column xsi:type="varchar" name="zone_code" length="10" nullable="true"
                comment="Zone Code"/>

        <column xsi:type="int" name="zone_id" unsigned="true" nullable="false" identity="false"
                comment="Zone Id"/>

        <column xsi:type="int" name="franchise_id" unsigned="true" nullable="false" identity="false"
                comment="Franchise Id"/>

        <column xsi:type="int" name="timeslot_type_id" unsigned="true" nullable="false" identity="false"
                comment="Timeslot Type Id"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="timeslot_id"/>
        </constraint>

    </table>
    <table name="timeslot" resource="default" engine="innodb" comment="Timeslots">
        <column xsi:type="int" name="timeslot_id" unsigned="true" nullable="false" identity="true"
                comment="timeslot Id"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="timeslot_id"/>
        </constraint>

    </table>
    <table name="closed_timeslot" resource="default" engine="innodb" comment="Closed Timeslot">
        <column xsi:type="int" name="closed_timeslot_id" unsigned="true" nullable="false" identity="true"
                comment="closed timeslot ID"/>

        <column xsi:type="int" name="timeslot_id" unsigned="true" nullable="false" identity="false"
                comment="Timeslot Id"/>

        <column xsi:type="date" name="date" comment="Date to close timeslot"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="closed_timeslot_id"/>
        </constraint>

        <constraint xsi:type="foreign" referenceId="CLOSED_TIMESLOT_TIMESLOT_ID"
                    table="closed_timeslot" column="timeslot_id" referenceTable="timeslot"
                    referenceColumn="timeslot_id" onDelete="CASCADE"/>
    </table>
    <table name="closed_timeslot_grid" resource="default" engine="innodb" comment="Closed Timeslot Grid">
        <column xsi:type="int" name="closed_timeslot_id" unsigned="true" nullable="false" identity="false"
                comment="closed timeslot ID"/>
        <column xsi:type="date" name="date" comment="Date to close timeslot"/>
        <column xsi:type="int" name="timeslot_id" unsigned="true" nullable="false" identity="false"
                comment="Timeslot Id"/>
        <column xsi:type="double" name="start_hour" unsigned="true" nullable="false" comment="start hour"/>
        <column xsi:type="double" name="end_hour" unsigned="true" nullable="false" comment="end hour"/>
        <column xsi:type="varchar" name="zone_code" length="10" nullable="true" comment="Zone Code"/>
        <column xsi:type="int" name="zone_id" unsigned="true" nullable="false" identity="false" comment="Zone Id"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="closed_timeslot_id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="CLOSED_TIMESLOT_TIMESLOT_ID"
                    table="closed_timeslot_grid" column="timeslot_id" referenceTable="timeslot"
                    referenceColumn="timeslot_id" onDelete="CASCADE"/>
    </table>
    <table name="franchise" resource="default" engine="innodb">
        <column xsi:type="boolean" default="1" name="authorize_payment_on_delivery" nullable="false"
                comment="Do I authorize payment on delivery ?"/>
    </table>
    <table name="franchise_product" resource="default" engine="innodb">
        <column xsi:type="int" name="code_compta" unsigned="true" nullable="false" identity="false" comment="Code Comptable"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP" comment="Creation Time"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP" comment="Modification Time"/>
    </table>
    <table name="franchise_product_grid" resource="default" engine="innodb">
        <column xsi:type="int" name="code_compta" unsigned="true" nullable="false" identity="false" comment="Code Comptable"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP" comment="Creation Time"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP" comment="Modification Time"/>
    </table>

    <!-- Performance optimization indexes for franchise filtering -->
    <table name="sales_order">
        <index referenceId="IDX_SALES_ORDER_FRANCHISE_ID" indexType="btree">
            <column name="franchise_id"/>
        </index>
    </table>

    <table name="sales_order_grid">
        <index referenceId="IDX_SALES_ORDER_GRID_FRANCHISE_ID" indexType="btree">
            <column name="franchise_id"/>
        </index>
    </table>

    <table name="sales_invoice">
        <index referenceId="IDX_SALES_INVOICE_ORDER_ID" indexType="btree">
            <column name="order_id"/>
        </index>
    </table>

    <table name="sales_creditmemo">
        <index referenceId="IDX_SALES_CREDITMEMO_ORDER_ID" indexType="btree">
            <column name="order_id"/>
        </index>
    </table>

    <table name="sales_invoice_grid">
        <index referenceId="IDX_SALES_INVOICE_GRID_ORDER_ID" indexType="btree">
            <column name="order_id"/>
        </index>
    </table>

    <table name="sales_creditmemo_grid">
        <index referenceId="IDX_SALES_CREDITMEMO_GRID_ORDER_ID" indexType="btree">
            <column name="order_id"/>
        </index>
    </table>

    <table name="customer_entity_text">
        <index referenceId="IDX_CUSTOMER_ENTITY_TEXT_ENTITY_ATTR" indexType="btree">
            <column name="entity_id"/>
            <column name="attribute_id"/>
        </index>
    </table>

    <table name="franchise_product">
        <index referenceId="IDX_FRANCHISE_PRODUCT_FRANCHISE_ID" indexType="btree">
            <column name="franchise_id"/>
        </index>
    </table>

    <table name="partenaire">
        <index referenceId="IDX_PARTENAIRE_FRANCHISE_ID" indexType="btree">
            <column name="franchise_id"/>
        </index>
    </table>

    <table name="eatlf_partenaire_contribution">
        <index referenceId="IDX_PARTENAIRE_CONTRIBUTION_PARTENAIRE_ID" indexType="btree">
            <column name="partenaire_id"/>
        </index>
    </table>

    <table name="eatlf_bap">
        <index referenceId="IDX_EATLF_BAP_CONTRIBUTION_ID" indexType="btree">
            <column name="contribution_id"/>
        </index>
    </table>

</schema>

<?php
/**
 * @category  <todo>
 * @package   <todo>
 * <AUTHOR> <<EMAIL>>
 * @Copyright 2018 Adin
 * @license   Apache License Version 2.0
 */

namespace Lf\Franchise\Model\ResourceModel\Override\Sequence;

class Meta extends \Magento\SalesSequence\Model\ResourceModel\Meta
{

    /**
     * Retrieves Metadata for entity by entity type and store id
     *
     * @param string $entityType
     * @param int $storeId
     * @return \Magento\SalesSequence\Model\Meta
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function loadByEntityTypeAndStoreAndFranchise($entityType, $storeId, $franchiseId)
    {
        $meta = $this->metaFactory->create();
        $connection = $this->getConnection();
        $bind = ['entity_type' => $entityType, 'store_id' => $storeId, 'franchise_id' => $franchiseId];
        $select = $connection->select()->from(
            $this->getMainTable(),
            [$this->getIdFieldN<PERSON>()]
        )->where(
            'entity_type = :entity_type AND store_id = :store_id  AND franchise_id = :franchise_id'
        );
        $metaId = $connection->fetchOne($select, $bind);

        if ($metaId) {
            $this->load($meta, $metaId);
        }
        return $meta;
    }
}
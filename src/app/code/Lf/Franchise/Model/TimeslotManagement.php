<?php


namespace Lf\Franchise\Model;

use Lf\Franchise\Api\Data\TimeslotInterfaceFactory;
use Lf\Franchise\Api\TimeslotManagementInterface;
use Lf\Franchise\Api\TimeslotRepositoryInterface;
use Magento\Checkout\Model\Session;

class TimeslotManagement implements TimeslotManagementInterface
{

    /**
     * @var TimeslotRepositoryInterface
     */
    private $timeslotRepository;
    /**
     * @var TimeslotInterfaceFactory
     */
    private $timeslotFactory;
    /**
     * @var Session
     */
    private $checkoutSession;

    public function __construct(
        Session $checkoutSession,
        TimeslotRepositoryInterface $timeslotRepository,
        TimeslotInterfaceFactory $timeslotFactory
    )
    {
        $this->timeslotRepository = $timeslotRepository;
        $this->timeslotFactory = $timeslotFactory;
        $this->checkoutSession = $checkoutSession;
    }

    /**
     * @inheritDoc
     */
    public function getNextAvailableTimeslot()
    {
        $quote = $this->checkoutSession->getQuote();

        if ($quote->getExtensionAttributes() === null || $quote->getExtensionAttributes()->getShippingData() === null ||
        $quote->getExtensionAttributes()->getShippingData()->getTimeslotId() === null) {
            return null;
        }

        return $this->timeslotRepository->getNextAvailable(
            $quote->getExtensionAttributes()->getShippingData()->getTimeslotId()
        );
    }
}
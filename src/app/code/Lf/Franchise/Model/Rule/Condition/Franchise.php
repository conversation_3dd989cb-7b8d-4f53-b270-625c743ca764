<?php

namespace Lf\Franchise\Model\Rule\Condition;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Rule\Model\Condition\AbstractCondition;
use Magento\Rule\Model\Condition\Context;
use Magento\Framework\Model\AbstractModel;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;


class Franchise extends AbstractCondition
{

    /**
     * @var FranchiseRepositoryInterface $franchiseRepository
     */
    private FranchiseRepositoryInterface $franchiseRepository;

    /**
     * @param Context $context
     * @param FranchiseRepositoryInterface $franchiseRepository
     * @param array $data
     */
    public function __construct(
        Context $context,
        FranchiseRepositoryInterface $franchiseRepository,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->franchiseRepository = $franchiseRepository;
    }

    public function loadAttributeOptions(): Franchise
    {
        $this->setAttributeOption([
            'franchise_id' => __('franchise_id')
        ]);
        return $this;
    }

    /**
     * @return mixed
     */
    public function getInputType(): string
    {
        return 'select';  // input type for admin condition
    }

    /**
     * @return string
     */
    public function getValueElementType(): string
    {
        return 'text';
    }

    /**
     * @param AbstractModel $model
     * @return bool
     * @throws LocalizedException
     */
    public function validate(AbstractModel $model): bool
    {
        try {
            $quote = $model->getQuote();
            $franchise = $this->franchiseRepository->getForQuote($quote->getId());
            $franchiseId = $franchise->getFranchiseId();
        } catch (NoSuchEntityException $e) {
            return false;
        }
        $model->setData('franchise_id', $franchiseId);
        return parent::validate($model);
    }
}

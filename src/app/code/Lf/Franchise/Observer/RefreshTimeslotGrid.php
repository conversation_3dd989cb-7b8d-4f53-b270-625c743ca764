<?php

namespace Lf\Franchise\Observer;;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Lf\Franchise\Model\ResourceModel\TimeslotGridInterface;

class RefreshTimeslotGrid implements ObserverInterface
{
    /**
     * @var TimeslotGridInterface
     */
    private $entityGrid;
    /**
     * @var ScopeConfigInterface
     */
    private $globalConfig;

    public function __construct(
        TimeslotGridInterface $entityGrid,
        ScopeConfigInterface $globalConfig
    )
    {
        $this->entityGrid = $entityGrid;
        $this->globalConfig = $globalConfig;
    }

    /**
     * Updates order grid on payment save
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        if (!$this->globalConfig->getValue('dev/grid/async_indexing')) {
            $this->entityGrid->refresh($observer->getObject()->getTimeslotId());
        }
    }
}
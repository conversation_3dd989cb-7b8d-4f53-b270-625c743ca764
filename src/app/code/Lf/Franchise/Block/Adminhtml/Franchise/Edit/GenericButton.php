<?php

namespace Lf\Franchise\Block\Adminhtml\Franchise\Edit;

use Magento\Backend\Block\Widget\Context;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class GenericButton
 */
class GenericButton
{
    /**
     * @var Context
     */
    protected $context;

    /**
     * @var FranchiseRepositoryInterface
     */
    protected $franchiseRepository;

    /**
     * @param Context $context
     * @param FranchiseRepositoryInterface $franchiseRepository
     */
    public function __construct(
        Context $context,
        FranchiseRepositoryInterface $franchiseRepository
    ) {
        $this->context = $context;
        $this->franchiseRepository = $franchiseRepository;
    }

    /**
     * Return Franchise ID
     *
     * @return int|null
     */
    public function getFranchiseId()
    {
        try {
            return $this->franchiseRepository->getById(
                $this->context->getRequest()->getParam('franchise_id')
            )->getId();
        } catch (NoSuchEntityException $e) {
        }
        return null;
    }

    /**
     * Generate url by route and parameters
     *
     * @param   string $route
     * @param   array $params
     * @return  string
     */
    public function getUrl($route = '', $params = [])
    {
        return $this->context->getUrlBuilder()->getUrl($route, $params);
    }
}

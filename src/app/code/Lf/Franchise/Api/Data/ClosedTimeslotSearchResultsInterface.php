<?php

namespace Lf\Franchise\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

/**
 * Interface for closed timeslot search results.
 * @api
 * @since 100.0.2
 */
interface ClosedTimeslotSearchResultsInterface extends SearchResultsInterface
{
    /**
     * Get closed timeslot list.
     *
     * @return ClosedTimeslotInterface[]
     */
    public function getItems();

    /**
     * Set closed timeslot list.
     *
     * @param ClosedTimeslotInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

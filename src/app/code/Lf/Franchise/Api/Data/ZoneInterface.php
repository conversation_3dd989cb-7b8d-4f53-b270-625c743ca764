<?php

namespace Lf\Franchise\Api\Data;

interface ZoneInterface
{

    const ZONE_ID = 'zone_id';
    const FRANCHISE_ID = 'franchise_id';
    const CODE = 'code';
    const NAME = 'name';
    const IS_ACTIVE = 'is_active';
    const ORDER_MINIMUM_AMOUNT = 'order_minimum_amount';
    const ORDER_PREPARATION_TIME = 'order_preparation_time';
    const REGULAR_SHIPPING_AVAILABLE = 'regular_shipping_available';
    const REGULAR_SHIPPING_AMOUNT = 'regular_shipping_amount';
    const EXPRES_SHIPPING_AVAILABLE = 'express_shipping_available';
    const EXPRESS_SHIPPING_AMOUNT = 'express_shipping_amount';
    const PICKUP_AVAILABLE = 'pickup_available';
    const PICKUP_AMOUNT = 'pickup_amount';

    /**
     * Get ID
     *
     * @return int|null
     */
    public function getId();

    /**
     * Get Franchise ID
     *
     * @return int|null
     */
    public function getFranchiseId();

    /**
     * Get Code
     *
     * @return string|null
     */
    public function getCode();

    /**
     * Get Name
     *
     * @return string|null
     */
    public function getName();

    /**
     * Get active status
     *
     * @return bool|null
     */
    public function getIsActive();

    /**
     * Get Order Minimum Amount
     *
     * @return float|null
     */
    public function getOrderMinimumAmount();

    /**
     * Get Order Prepration Time
     *
     * @return int|null
     */
    public function getOrderPreparationTime();

    /**
     * Get Regular Shipping Status
     *
     * @return bool|null
     */
    public function getRegularShippingAvailable();

    /**
     * Get Regular Shipping Amount
     *
     * @return float|null
     */
    public function getRegularShippingAmount();

    /**
     * Get Express Shipping Status
     *
     * @return bool|null
     */
    public function getExpressShippingAvailable();

    /**
     * Get Express Shipping Amount
     *
     * @return float|null
     */
    public function getExpressShippingAmount();

    /**
     * Get Pickup status
     *
     * @return bool|null
     */
    public function getPickupAvailable();

    /**
     * Get Pickup Amount
     *
     * @return float|null
     */
    public function getPickupAmount();

    /**
     * Set ID
     *
     * @param int $id
     * @return ZoneInterface
     */
    public function setId($id);

    /**
     * Set Franchise ID
     *
     * @param int $franchiseId
     * @return ZoneInterface
     */
    public function setFranchiseId($franchiseId);

    /**
     * Set Code
     *
     * @param string $code
     * @return ZoneInterface
     */
    public function setCode($code);

    /**
     * Set Name
     *
     * @param string $name
     * @return ZoneInterface
     */
    public function setName($name);

    /**
     * Set status
     *
     * @param bool $status
     * @return ZoneInterface
     */
    public function setIsActive($status);

    /**
     * Set Order Minimum Amount
     *
     * @param float $minimumAmount
     * @return ZoneInterface
     */
    public function setOrderMinimumAmount($minimumAmount);

    /**
     * Set Order Preparation Time
     *
     * @param int $preparationTime
     * @return ZoneInterface
     */
    public function setOrderPreparationTime($preparationTime);

    /**
     * Set Regular Shipping State
     *
     * @param bool $state
     * @return ZoneInterface
     */
    public function setRegularShippingAvailable($state);

    /**
     * Set Regular Shipping Amount
     *
     * @param float $amount
     * @return ZoneInterface
     */
    public function setRegularShippingAmount($amount);

    /**
     * Set Express Shipping State
     *
     * @param bool $state
     * @return ZoneInterface
     */
    public function setExpressShippingAvailable($state);

    /**
     * Set Express Shipping Amount
     *
     * @param float $amount
     * @return ZoneInterface
     */
    public function setExpressShippingAmount($amount);
    /**
     * Set Pickup State
     *
     * @param bool $state
     * @return ZoneInterface
     */
    public function setPickupAvailable($state);

    /**
     * Set Pickup Amount
     *
     * @param float $amount
     * @return ZoneInterface
     */
    public function setPickupAmount($amount);

}
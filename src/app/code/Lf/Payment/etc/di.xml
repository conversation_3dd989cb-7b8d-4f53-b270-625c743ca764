<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <preference for="Adin\Edenred\Api\ConfigManagementInterface" type="Lf\Payment\Model\Edenred\ConfigManagement" />

    <type name="Lf\Payment\Model\Edenred\FranchiseActivationHandler">
        <arguments>
            <argument name="configInterface" xsi:type="object">Adin\Edenred\Gateway\Config\Config</argument>
        </arguments>
    </type>

    <virtualType name="Adin\Edenred\Gateway\ValueHandlerPool">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="active" xsi:type="string">Lf\Payment\Model\Edenred\FranchiseActivationHandler</item>
                <item name="mid" xsi:type="string">Lf\Payment\Model\Edenred\FranchiseMidHandler</item>
            </argument>
        </arguments>
    </virtualType>
</config>

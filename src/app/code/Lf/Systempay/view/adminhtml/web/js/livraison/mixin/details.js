define([
    'jquery',
    'uiRegistry',
    'Lf_Livraison/js/model'
], function (
    $,
    registry,
    model
) {
    'use strict';

    return function (Details) {
        return Details.extend({
            show: function () {
                this._super();

                registry.async('details.systempay')(function (Systempay) {
                    Systempay.onShow();
                });
            },

            handleBackButton: function () {
                this._super();

                registry.async('details.systempay')(function (Systempay) {
                    Systempay.onBackButton();
                });
            },

            doValiderLivraison: function () {
                if (model.currentOrder().paymentMethod === 'lf_payment_livraison'
                    && parseFloat(this.amountCarte()) > 0) {

                    registry.async('details.systempay')(function (Systempay) {
                        Systempay.onValidate();
                    });
                } else {
                    this._super();
                }
            }
        })
    }
});
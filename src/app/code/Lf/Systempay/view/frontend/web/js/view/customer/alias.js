define([
    'jquery',
    'uiComponent'
], function (
    $,
    Component
) {
    'use strict';

    // Use default messages for these errors.
    var DFAULT_MESSAGES = [
        'CLIENT_300', 'CLIENT_304', 'CLIENT_502', 'PSP_539'
    ];

    var RECOVERABLE_ERRORS = [
        'CLIENT_300', 'CLIENT_304', 'CLIENT_502',
        'PSP_539', 'CLIENT_001', 'CLIENT_101',
        'CLIENT_301', 'CLIENT_302', 'CLIENT_303',
        'PSP_003', 'PSP_108', 'ACQ_001', 'PSP_099'
    ];

    var ERROR_MESSAGES = {
        fr: {
            CLIENT_001: 'Le paiement est refusé. Essayez de payer avec une autre carte.',
            CLIENT_101: 'Le paiement est annulé.',
            CLIENT_301: 'Le numéro de carte est invalide. Vérifiez le numéro et essayez à nouveau.',
            CLIENT_302: 'La date d\'expiration est invalide. Vérifiez la date et essayez à nouveau.',
            CLIENT_303: 'Le code de sécurité CVV est invalide. Vérifiez le code et essayez à nouveau.',
            CLIENT_999: 'Une erreur technique est survenue. Merci de réessayer plus tard.',

            INT_999: 'Une erreur technique est survenue. Merci de réessayer plus tard.',

            PSP_003: 'Le paiement est refusé. Essayez de payer avec une autre carte.',
            PSP_099: 'Trop de tentatives ont été effectuées. Merci de réessayer plus tard.',
            PSP_108: 'Le formulaire a expiré. Veuillez rafraîchir la page.',
            PSP_999: 'Une erreur est survenue durant le processus de paiement.',

            ACQ_001: 'Le paiement est refusé. Essayez de payer avec une autre carte.',
            ACQ_999: 'Une erreur est survenue durant le processus de paiement.'
        },

        en: {
            CLIENT_001: 'Payment is refused. Try to pay with another card.',
            CLIENT_101: 'Payment is cancelled.',
            CLIENT_301: 'The card number is invalid. Please check the number and try again.',
            CLIENT_302: 'The expiration date is invalid. Please check the date and try again.',
            CLIENT_303: 'The card security code (CVV) is invalid. Please check the code and try again.',
            CLIENT_999: 'A technical error has occurred. Please try again later.',

            INT_999: 'A technical error has occurred. Please try again later.',

            PSP_003: 'Payment is refused. Try to pay with another card.',
            PSP_099: 'Too many attempts. Please try again later.',
            PSP_108: 'The form has expired. Please refresh the page.',
            PSP_999: 'An error has occurred during the payment process.',

            ACQ_001: 'Payment is refused. Try to pay with another card.',
            ACQ_999: 'An error has occurred during the payment process.'
        }
    };

    return Component.extend({
        canSave: true,

        initObservable: function () {
            this._super();

            this.observe('canSave');

            return this;
        },

        initRestForm: function () {
            var self = this;

            require(['krypton'], function (KR) {
                KR.onFocus(function (e) {
                    $('.kr-form-error').html('');
                });

                KR.onError(function (e) {
                    // Not recoverable error, reload page after a while.
                    if (RECOVERABLE_ERRORS.indexOf(e.errorCode) === -1) {
                        setTimeout(function () {
                            window.location.reload();
                        }, 4000);
                    }

                    var msg = '';
                    if (DFAULT_MESSAGES.indexOf(e.errorCode) > -1) {
                        msg = e.errorMessage;
                        var endsWithDot = (msg.lastIndexOf('.') == (msg.length - 1) && msg.lastIndexOf('.') >= 0);

                        msg += (endsWithDot ? '' : '.');
                    } else {
                        msg = self.translateError(e.errorCode);
                    }

                    $('.kr-form-error').html('<span style="color: red;"><span>' + msg + '</span></span>');
                    self.canSave(true);
                });
            });
        },

        translateError: function (code) {
            var lang = 'fr';
            var messages = ERROR_MESSAGES.hasOwnProperty(lang) ? ERROR_MESSAGES[lang] : ERROR_MESSAGES['en'];

            if (!messages.hasOwnProperty(code)) {
                var index = code.lastIndexOf('_');
                code = code.substring(0, index + 1) + '999';
            }

            return messages[code];
        },

        onSaveClick: function () {
            this.canSave(false);

            $.post('/lfsystempay/aliases/token', {
                cardName: $('[name="card-name"]').val()
            }).then(function (response) {
                KR.setFormConfig({formToken: response.token}).then(function () {
                    $('.kr-payment-button').trigger('click');
                });
            });
        }
    });
});
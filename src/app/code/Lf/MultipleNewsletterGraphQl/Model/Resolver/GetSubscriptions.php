<?php

declare(strict_types=1);

namespace Lf\MultipleNewsletterGraphQl\Model\Resolver;

use Lf\MultipleNewsletter\Model\NewsletterOptionService;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Psr\Log\LoggerInterface;

/**
 * Orders data resolver
 */
class GetSubscriptions implements ResolverInterface
{
    private CustomerRepositoryInterface $customerRepository;
    private NewsletterOptionService $newsletterOptionService;
    private LoggerInterface $logger;

    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        LoggerInterface $logger,
        NewsletterOptionService $newsletterOptionService
    ) {
        $this->customerRepository = $customerRepository;
        $this->newsletterOptionService = $newsletterOptionService;
        $this->logger = $logger;
    }

    /**
     * @inheritDoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }

        $customerId = $context->getUserId();

        if ($customerId === 0) {
            return false;
        }

        try {
            $customer = $this->customerRepository->getById($customerId);
        } catch (NoSuchEntityException $e) {
            $this->logger->critical(__('Unable to load customer : %1.', $e->getMessage()));
        }

        $customerSubscriptions = $this->newsletterOptionService->getCustomerSubscription($customer);

        $subscriptions = [];

        foreach(explode(',',$this->newsletterOptionService->getAllListIds()) as $key => $listId)
        {
            if(in_array($listId,$customerSubscriptions))
            {
                $subscriptions['newsletterList'.$key+1] = true;
            }
            else
            {
                $subscriptions['newsletterList'.$key+1] = false;
            }
        }

        return $subscriptions;
    }
}

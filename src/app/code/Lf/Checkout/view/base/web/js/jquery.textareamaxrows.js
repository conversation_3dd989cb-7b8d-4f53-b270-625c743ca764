(function ($) {
    $.fn.jQTArea = function (options) {
        var
            defaults =
                {
                    setLimit: 208 // Input limit default

                },
            plugin = $.extend({}, defaults, options);

        this.each(function () {
            var
                // get textarea element
                getTextarea = $(this),
                maxnblines = plugin.maxlines;
            maxcarinlines = plugin.maxcarinlines;


            // bind events to textarea
            if (getTextarea.is('textarea')) {
                $(this).val($(this).val().substring(0, plugin.setLimit - 1));
                getTextarea.on("keypress change focus paste input propertychange", function (e) {

                    // validate on event trigger
                    if (typeof (e) != "undefined" && e !== null) {
                        fnValidate($(this));
                        $(this).keydown(' '); //force refresh
                    } else return;

                });


                document.body.addEventListener("keydown", function (e) {

                    e = e || window.event;
                    var key = e.which || e.keyCode; // keyCode detection
                    var ctrl = e.ctrlKey ? e.ctrlKey : ((key === 17) ? true : false); // ctrl detection

                    if (key == 86 && ctrl) {
                        var nboflines = getTextarea.val().split(/\r|\r\n|\n/);

                        getTextarea.val(getTextarea.val().substring(0, plugin.setLimit - 1));
                        getTextarea.val(getTextarea.val().replace(/.{57}/g, "\n"));
                        if (nboflines.length > plugin.maxlines) {
                            nboflines = nboflines.slice(0, maxnblines);
                            getTextarea.val(nboflines.join('\n').replace(/(?:(?:\r\n|\r|\n)\s*){2}/gm, ""));
                            getTextarea.keypress(' '); // force refresh;
                        }

                    }
                    if (typeof (e) != "undefined" && e !== null) {
                        fnValidate($(this));
                    } else return;

                }, false);
            }


            function fnValidate(e) {
                if (typeof (e) != "undefined" && e !== null) {
                    var
                        // get input
                        getInput = e.val(),
                        // get input length
                        inputLength = getInput.length,
                        // get set limit
                        limit = plugin.setLimit;
                    e.val(getInput.substring(0, limit - 1));

                    // get number of lines
                    var nboflines = e.val().split(/\r|\r\n|\n/);

                    nboflines = nboflines.slice(0, maxnblines);
                    e.val(nboflines.join('\n').replace(/(?:(?:\r\n|\r|\n)\s*){2}/gm, ""));

                    var count = nboflines.length;
                    if (count > maxnblines) {
                        // truncate any new lines
                        e.val((getInput).replace(/\n+$/, ""));

                        return;
                    }

                    if (inputLength > limit) {
                        // truncate any input beyond the set limit
                        e.val(getInput.substring(0, limit - 1));

                    }
                } else return;

            }
        });

        return this;
    }
})(jQuery);
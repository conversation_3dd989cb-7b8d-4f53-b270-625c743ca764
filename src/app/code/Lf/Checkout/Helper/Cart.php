<?php

namespace Lf\Checkout\Helper;

use Lf\Franchise\Api\TimeslotRepositoryInterface;
use Lf\Franchise\Model\Zone;
use Magento\Checkout\Model\Session;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Quote\Model\Quote;

/**
 * Class Cart Helper
 * @package Lf\Checkout\Helper
 */
class Cart extends AbstractHelper
{

    /**
     * @var Session
     */
    protected $checkoutSession;

    /**
     * @var Zone
     */
    protected $zone;
    /**
     * @var TimeslotRepositoryInterface
     */
    private $timeslotRepository;

    /**
     * Cart constructor.
     * @param Context $context
     * @param Session $checkoutSession
     * @param Zone $zone
     * @param TimeslotRepositoryInterface $timeslotRepository
     */
    public function __construct(
        Context $context,
        Session $checkoutSession,
        Zone $zone,
        TimeslotRepositoryInterface $timeslotRepository
    )
    {
        parent::__construct($context);
        $this->checkoutSession = $checkoutSession;
        $this->zone = $zone;
        $this->timeslotRepository = $timeslotRepository;
    }

    /**
     * Retrieve order minimum amount for the current session zone
     * @return float
     */
    public function getMinimumOrderAmount()
    {
        $quote = $this->checkoutSession->getQuote();

        $extensionAttributes = $quote->getExtensionAttributes();

        $order_minimum_amount = "";

        if (null !== $extensionAttributes) {
            $zoneCode = $quote->getExtensionAttributes()->getShippingData()->getZone();
            $timeslotId = $quote->getExtensionAttributes()->getShippingData()->getTimeslotId();

            if ($zoneCode) {
                $this->zone->load($zoneCode, 'code');
                if ($this->zone->getId()) {
                    $order_minimum_amount = $this->zone->getOrderMinimumAmount();
                }
            }

            try {
                $timeslot = $this->timeslotRepository->getById($timeslotId);

                if ($timeslot->getMinimumCommande() !== null) {
                    $order_minimum_amount = $timeslot->getMinimumCommande();
                }
            } catch (LocalizedException $e) {
            }
        }
        return $order_minimum_amount;
    }

    /**
     * check if minimum order amount has been reached for the current session quote
     * @return bool
     */
    public function isMinimumOrderAmountReached()
    {
        /** @var Quote $quote */
        $quote = $this->checkoutSession->getQuote();

        /** @var Quote\Address $shippingAddress */
        $quoteShippingAddress = $quote->getShippingAddress();
        $minimumOrderAmount = $this->getMinimumOrderAmount();
        if ($minimumOrderAmount) {
            if ($quoteShippingAddress->getSubtotalInclTax() < $minimumOrderAmount) {
                return false;
            }
        }

        return true;
    }

}

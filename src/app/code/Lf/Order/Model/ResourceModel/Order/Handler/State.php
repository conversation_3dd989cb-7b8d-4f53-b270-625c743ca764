<?php
namespace Lf\Order\Model\ResourceModel\Order\Handler;

use Magento\Sales\Model\Order;
use Lf\Sales\Setup\UpgradeData;

class State extends \Magento\Sales\Model\ResourceModel\Order\Handler\State
{
    /**
     * Même si la commande peut-être clôturée pour magento (facturée, expédiée...) on empêche
     * le changement de statut si la commande est en cours de livraison.
     *
     * @param Order $order
     * @return \Magento\Sales\Model\ResourceModel\Order\Handler\State
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function check(Order $order)
    {
        if ($order->getStatus() == UpgradeData::ORDER_STATUS_PROCESSING_SHIPMENT_CODE) {
            return $this;
        }

        return parent::check($order);
    }
}
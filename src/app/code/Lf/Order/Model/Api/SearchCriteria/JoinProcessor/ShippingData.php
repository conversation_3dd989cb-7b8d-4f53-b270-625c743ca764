<?php

namespace Lf\Order\Model\Api\SearchCriteria\JoinProcessor;

use Magento\Framework\Api\SearchCriteria\CollectionProcessor\JoinProcessor\CustomJoinInterface;
use Magento\Framework\Data\Collection\AbstractDb;

/**
 * Class Rate
 * @package Lf\Order\Model\Api\SearchCriteria\JoinProcessor
 */
class ShippingData implements CustomJoinInterface
{
    /**
     * @param \Magento\Tax\Model\ResourceModel\Calculation\Rule\Collection $collection
     * @return true
     */
    public function apply(AbstractDb $collection)
    {
        $aliasShipping = 'shipping_data';
        $aliasQuote = 'quote';
        $tableName = 'quote_shipping_datas';
        $aliasTimeslot = 'timeslot';
        $timeslotTableName = 'timeslot';
        $aliasZone = 'zone';
        $zoneTableName = 'zone';
        $aliasShippingOrderAddress = 'sales_order_shipping_address';
        $orderAddressableName = 'sales_order_address';

        $collection->getSelect()
            ->joinLeft(
                [$aliasQuote => $collection->getTable($aliasQuote)],
                "main_table.quote_id = {$aliasQuote}.entity_id",
                []
            )
            ->joinLeft(
                [$aliasShipping => $collection->getTable($tableName)],
                "{$aliasQuote}.entity_id = {$aliasShipping}.quote_id",
                ["timeslot_id" => "{$aliasShipping}.timeslot_id"]
            )
            ->joinLeft(
                [$aliasTimeslot => $collection->getTable($timeslotTableName)],
                "{$aliasShipping}.timeslot_id = {$aliasTimeslot}.timeslot_id",
                ["timeslot_start_hour" => "{$aliasTimeslot}.start_hour","timeslot_end_hour" => "{$aliasTimeslot}.end_hour", "timeslot_is_retention" => "{$aliasTimeslot}.is_retention"]
            )
            ->joinLeft(
                [$aliasZone => $collection->getTable($zoneTableName)],
                "{$aliasShipping}.zone = {$aliasZone}.code",
                ["zone_id" => "{$aliasZone}.zone_id","zone_name" => "{$aliasZone}.code"]
            )
            ->joinLeft(
                [$aliasShippingOrderAddress => $collection->getTable($orderAddressableName)],
                "{$aliasShippingOrderAddress}.parent_id= main_table.entity_id and {$aliasShippingOrderAddress}.address_type='shipping'",
                [
                    "shipping_address_company" => "{$aliasShippingOrderAddress}.company",
                    "shipping_address_lastname" => "{$aliasShippingOrderAddress}.lastname",
                    "shipping_address_firstname" => "{$aliasShippingOrderAddress}.firstname",
                ]
            )
        ;

        return true;
    }
}

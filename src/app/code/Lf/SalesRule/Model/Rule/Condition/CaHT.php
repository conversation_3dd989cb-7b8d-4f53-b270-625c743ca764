<?php

namespace Lf\SalesRule\Model\Rule\Condition;

use Magento\Rule\Model\Condition\AbstractCondition;
use Magento\Rule\Model\Condition\Context;
use Magento\Checkout\Model\Session;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Exception\LocalizedException;

class CaHT extends AbstractCondition
{
    /**
     * @var Session $_checkoutSession
     */
    protected Session $_checkoutSession;

    public function __construct(
        Context $context,
        Session $checkoutSession,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->_checkoutSession = $checkoutSession;
    }

    public function loadAttributeOptions(): CaHT
    {
        $this->setAttributeOption([
            'total_ca' => __('Last year customer\'s sales revenue without taxes')
        ]);
        return $this;
    }

    /**
     * @return mixed
     */
    public function getInputType(): string
    {
        return 'select';  // input type for admin condition
    }

    /**
     * @return string
     */
    public function getValueElementType(): string
    {
        return 'text';
    }

    /**
     * Load operator options
     *
     * @return $this
     */
    public function loadOperatorOptions()
    {
        $this->setOperatorOption(
            [
                '>=' => __('equals or greater than'),
                '<=' => __('equals or less than'),
            ]
        );
        return $this;
    }

    /**
     * @param AbstractModel $model
     * @return bool
     * @throws LocalizedException
     */
    public function validate(AbstractModel $model): bool
    {
        $quote = $model->getQuote();
        $htAtt = $quote->getCustomer()->getCustomAttribute('total_ca');
        $totalCAHt = 0;
        if ($htAtt !== null) {
            foreach ($htAtt->getValue() as $FranchiseCa) {
                $totalCAHt += $FranchiseCa['value'];
            }
        }

        $model->setData('total_ca', $totalCAHt);
        return parent::validate($model);
    }
}

<?php
namespace Lf\Sales\Rewrite\Controller\Adminhtml\Order\Creditmemo;

use Lf\Sales\Model\Creditmemo\Refund;
use Magento\Backend\App\Action;
use Magento\Sales\Model\Order\Email\Sender\CreditmemoSender;

class Save extends \Magento\Sales\Controller\Adminhtml\Order\Creditmemo\Save
{
    /**
     * @var \Lf\Sales\Model\Creditmemo\Refund
     */
    private $creditmemoRefund;

    public function __construct(
        Action\Context $context,
        \Magento\Sales\Controller\Adminhtml\Order\CreditmemoLoader $creditmemoLoader,
        CreditmemoSender $creditmemoSender,
        \Magento\Backend\Model\View\Result\ForwardFactory $resultForwardFactory,
        Refund $creditmemoRefund
    ) {
        parent::__construct($context, $creditmemoLoader, $creditmemoSender, $resultForwardFactory);

        $this->creditmemoRefund = $creditmemoRefund;
    }

    /**
     * Save creditmemo
     * We can save only new creditmemo. Existing creditmemos are not editable
     *
     * @return \Magento\Backend\Model\View\Result\Redirect|\Magento\Backend\Model\View\Result\Forward
     *
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $data = $this->getRequest()->getPost('creditmemo');
        if (!empty($data['comment_text'])) {
            $this->_getSession()->setCommentText($data['comment_text']);
        }
        try {
            $this->creditmemoLoader->setOrderId($this->getRequest()->getParam('order_id'));
            $this->creditmemoLoader->setCreditmemoId($this->getRequest()->getParam('creditmemo_id'));
            $this->creditmemoLoader->setCreditmemo($this->getRequest()->getParam('creditmemo'));
            $this->creditmemoLoader->setInvoiceId($this->getRequest()->getParam('invoice_id'));
            $creditmemo = $this->creditmemoLoader->load();
            if ($creditmemo) {
                $isZeroAmountPromoOrder = $creditmemo->getData('_zero_amount_promo_order') || $this->isZeroAmountOrderWithPromo($creditmemo);
                if (!$creditmemo->isValidGrandTotal() && !$isZeroAmountPromoOrder) {
                    throw new \Magento\Framework\Exception\LocalizedException(
                        __('The credit memo\'s total must be positive.')
                    );
                }

                if (!empty($data['comment_text'])) {
                    $creditmemo->addComment(
                        $data['comment_text'],
                        isset($data['comment_customer_notify']),
                        isset($data['is_visible_on_front'])
                    );

                    $creditmemo->setCustomerNote($data['comment_text']);
                    $creditmemo->setCustomerNoteNotify(isset($data['comment_customer_notify']));
                }

                if (isset($data['do_offline'])) {
                    //do not allow online refund for Refund to Store Credit
                    if (!$data['do_offline'] && !empty($data['refund_customerbalance_return_enable'])) {
                        throw new \Magento\Framework\Exception\LocalizedException(
                            __('Cannot create online refund for Refund to Store Credit.')
                        );
                    }
                }
                $creditmemoManagement = $this->_objectManager->create(
                    \Magento\Sales\Api\CreditmemoManagementInterface::class
                );
                $creditmemo->getOrder()->setCustomerNoteNotify(!empty($data['send_email']));
                $creditmemoManagement->refund($creditmemo, (bool)$data['do_offline']);

                if (!empty($data['send_email'])) {
                    $this->creditmemoSender->send($creditmemo);
                }

                if ($creditmemo->getId() !== null) {
                    $this->creditmemoRefund->refund(
                        $creditmemo->getId(),
                        $this->getRequest()->getParam('selectedDate'),
                        $this->getRequest()->getParam('selectedReglement')
                    );
                }

                $this->messageManager->addSuccess(__('You created the credit memo.'));
                $this->_getSession()->getCommentText(true);
                $resultRedirect->setPath('sales/order/view', ['order_id' => $creditmemo->getOrderId()]);
                return $resultRedirect;
            } else {
                $resultForward = $this->resultForwardFactory->create();
                $resultForward->forward('noroute');
                return $resultForward;
            }
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $this->messageManager->addError($e->getMessage());
            $this->_getSession()->setFormData($data);
        } catch (\Exception $e) {
            $this->_objectManager->get(\Psr\Log\LoggerInterface::class)->critical($e);
            $this->messageManager->addError(__('We can\'t save the credit memo right now.'));
        }
        $resultRedirect->setPath('sales/*/new', ['_current' => true]);
        return $resultRedirect;
    }

    /**
     *
     * @param \Magento\Sales\Model\Order\Creditmemo $creditmemo
     * @return bool
     */
    private function isZeroAmountOrderWithPromo($creditmemo): bool
    {
        $order = $creditmemo->getOrder();

        $orderTotal = $order->getGrandTotal();
        if ($orderTotal > 0.01) {
            return false;
        }

        $discountAmount = abs($order->getDiscountAmount());
        if ($discountAmount > 0) {
            return true;
        }
        if ($order->getCouponCode()) {
            return true;
        }
        $creditmemoTotal = $creditmemo->getGrandTotal();
        if ($creditmemoTotal <= 0.01 && $creditmemoTotal >= -0.01) {
            return true;
        }

        return false;
    }
}

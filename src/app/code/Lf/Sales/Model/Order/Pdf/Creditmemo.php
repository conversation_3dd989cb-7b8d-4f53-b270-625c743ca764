<?php
/**
 * @category  Lf
 * @package   Lf_Sales
 * @Copyright 2018 Adin
 * @license   Apache License Version 2.0
 */

namespace Lf\Sales\Model\Order\Pdf;

use Lf\Franchise\Model\FranchiseFactory;
use Lf\Franchise\Model\ResourceModel\FranchiseRepository;
use Lf\Franchise\Model\TimeslotRepository;
use Lf\Opc\Model\ResourceModel\ShippingDatasRepository;
use Lf\ShippingBar\Helper\Date;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\QuoteFactory;
use Magento\Quote\Model\QuoteRepository;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\ResourceModel\Order\Tax\Item;
use Zend_Pdf_Page;
use Zend_Pdf_Resource_Font;

class Creditmemo extends \Magento\Sales\Model\Order\Pdf\Creditmemo
{
    const ADDRESS_LENGTH = 25;

    const HEADER_WIDTH = 570;

    const LEFT_MARGIN = 20;

    const FOOTER_HEIGHT = 110;

    const TOP_START = 820;

    private $franchise;

    private $timeslot;
    private $timeslotRepository;
    private $quoteRepository;
    private $quoteFactory;
    private $franchiseFactory;

    /** @var Order*/
    private $order;
    /** @var Quote */
    private $quote;
    /** @var Order\Creditmemo */
    private $creditmemo;
    private $shippingData;
    private $shippingHelper;

    private $franchiseRepository;
    private $shippingDatasRepository;

    private $taxItem;

    private $taxes = [];
    private $prcRemises = 0;
    private $totalRemises = 0;
    private $totalTTCHorsRemisePanierText ='';

    private $_localResolver;
    private \Magento\Framework\Locale\ResolverInterface $_localeResolver;

    public function __construct(
        \Magento\Payment\Helper\Data $paymentData,
        \Magento\Framework\Stdlib\StringUtils $string,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Sales\Model\Order\Pdf\Config $pdfConfig,
        \Magento\Sales\Model\Order\Pdf\Total\Factory $pdfTotalFactory,
        \Magento\Sales\Model\Order\Pdf\ItemsFactory $pdfItemsFactory,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate,
        \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation,
        \Magento\Sales\Model\Order\Address\Renderer $addressRenderer,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Store\Model\App\Emulation $appEmulation,
        \Magento\Framework\Locale\ResolverInterface $localeResolver,
        QuoteFactory $quoteFactory,
        QuoteRepository $quoteRepository,
        FranchiseFactory $franchiseFactory,
        FranchiseRepository $franchiseRepository,
        TimeslotRepository $timeslotRepository,
        ShippingDatasRepository $shippingDatasRepository,
        Date $shippingHelper,
        Item $taxItem,
        array $data = []
    ) {
        $this->taxItem = $taxItem;
        $this->shippingHelper = $shippingHelper;
        $this->timeslotRepository = $timeslotRepository;
        $this->quoteRepository = $quoteRepository;
        $this->quoteFactory = $quoteFactory;
        $this->franchiseFactory = $franchiseFactory;
        $this->franchiseRepository = $franchiseRepository;
        $this->shippingDatasRepository = $shippingDatasRepository;
        $this->_localeResolver = $localeResolver;
        parent::__construct($paymentData, $string, $scopeConfig, $filesystem, $pdfConfig, $pdfTotalFactory, $pdfItemsFactory, $localeDate, $inlineTranslation, $addressRenderer, $storeManager, $appEmulation, $data);
    }

    public function drawLineBlocks(\Zend_Pdf_Page $page, array $draw, array $pageSettings = [])
    {
        foreach ($draw as $itemsProp) {
            if (!isset($itemsProp['lines']) || !is_array($itemsProp['lines'])) {
                throw new \Magento\Framework\Exception\LocalizedException(
                    __('We don\'t recognize the draw line data. Please define the "lines" array.')
                );
            }
            $lines = $itemsProp['lines'];
            $height = isset($itemsProp['height']) ? $itemsProp['height'] : 10;

            if (empty($itemsProp['shift'])) {
                $shift = 0;
                foreach ($lines as $line) {
                    $maxHeight = 0;
                    foreach ($line as $column) {
                        $lineSpacing = !empty($column['height']) ? $column['height'] : $height;
                        if (!is_array($column['text'])) {
                            $column['text'] = [$column['text']];
                        }
                        $top = 0;
                        foreach ($column['text'] as $part) {
                            $top += $lineSpacing;
                        }

                        $maxHeight = $top > $maxHeight ? $top : $maxHeight;
                    }
                    $shift += $maxHeight;
                }
                $itemsProp['shift'] = $shift;
            }

            if ($this->y - $itemsProp['shift'] < 80) {
                $page = $this->newPage($pageSettings);
            }

            foreach ($lines as $line) {
                $maxHeight = 0;
                foreach ($line as $column) {
                    $font = $this->setFont($page, $column);
                    $fontSize = $column['font_size'];

                    if (!is_array($column['text'])) {
                        $column['text'] = [$column['text']];
                    }

                    $lineSpacing = !empty($column['height']) ? $column['height'] : $height;
                    $top = 0;
                    foreach ($column['text'] as $part) {
                        if ($this->y - $lineSpacing < 80) {
                            $page = $this->newPage($pageSettings);
                            $font = $this->setFont($page, $column);
                            $fontSize = $column['font_size'];
                        }

                        $feed = $column['feed'];
                        $textAlign = empty($column['align']) ? 'left' : $column['align'];
                        $width = empty($column['width']) ? 0 : $column['width'];
                        switch ($textAlign) {
                            case 'right':
                                if ($width) {
                                    $feed = $this->getAlignRight($part, $feed, $width, $font, $fontSize);
                                } else {
                                    $feed = $feed - $this->widthForStringUsingFontSize($part, $font, $fontSize);
                                }
                                break;
                            case 'center':
                                if ($width) {
                                    $feed = $this->getAlignCenter($part, $feed, $width, $font, $fontSize);
                                }
                                break;
                            default:
                                break;
                        }
                        $page->drawText($part, $feed, $this->y - $top, 'UTF-8');
                        $top += $lineSpacing;
                    }

                    $maxHeight = $top > $maxHeight ? $top : $maxHeight;
                }
                $this->y -= $maxHeight;
            }
        }

        return $page;
    }


    /**
     * Set page font.
     *
     * column array format
     * font         string; font style, optional: bold, italic, regular
     * font_file    string; path to font file (optional for use your custom font)
     * font_size    int; font size (default 10)
     *
     * @param \Zend_Pdf_Page $page
     * @param array $column
     * @return \Zend_Pdf_Resource_Font
     * @throws \Zend_Pdf_Exception
     */
    private function setFont($page, &$column)
    {
        $fontSize = empty($column['font_size']) ? 10 : $column['font_size'];
        $column['font_size'] = $fontSize;
        if (!empty($column['font_file'])) {
            $font = \Zend_Pdf_Font::fontWithPath($column['font_file']);
            $page->setFont($font, $fontSize);
        } else {
            $fontStyle = empty($column['font']) ? 'regular' : $column['font'];
            switch ($fontStyle) {
                case 'bold':
                    $font = $this->_setFontBold($page, $fontSize);
                    break;
                case 'italic':
                    $font = $this->_setFontItalic($page, $fontSize);
                    break;
                default:
                    $font = $this->_setFontRegular($page, $fontSize);
                    break;
            }
        }

        return $font;
    }

    /**
     * Return PDF document
     *
     * @param array|Collection $creditmemos
     * @return \Zend_Pdf
     */
    public function getPdf($creditmemos = [])
    {
        $this->_beforeGetPdf();
        $this->_initRenderer('creditmemo');

        $pdf = new \Zend_Pdf();
        $this->_setPdf($pdf);
        $style = new \Zend_Pdf_Style();
        $this->_setFontBold($style, 10);

        /** @var Order\Creditmemo $creditmemo */
        foreach ($creditmemos as $creditmemo) {
            if ($creditmemo->getStoreId()) {
                $this->_localeResolver->emulate($creditmemo->getStoreId());
                $this->_storeManager->setCurrentStore($creditmemo->getStoreId());
            }
            $quoteIsMissing = false;

            $order = $creditmemo->getOrder();
            $this->creditmemo = $creditmemo;
            $this->order = $order;

            $this->shippingData = $this->shippingDatasRepository->getByQuoteId($this->order->getQuoteId());

            try {
                $this->quote = $this->quoteRepository->get($order->getQuoteId());
            } catch (NoSuchEntityException $e) {
                $quoteIsMissing = true;
            }

            $this->franchise = $this->franchiseRepository->getForQuote($order->getQuoteId());

            if(is_numeric($this->shippingData->getTimeslotId()))
            {
                try {
                    $this->timeslot = $this->timeslotRepository->getById($this->shippingData->getTimeslotId());
                } catch (NoSuchEntityException $e) {
                    $this->timeslot = null;
                }
            }
            else
            {
                $this->timeslot = $this->shippingData->getTimeslotId();
            }

            $page = $this->newPage();

            /* Add body */
            $splitItems = [];
            $hasSplit = false;
            /** @var Order\Creditmemo\Item $item */
            foreach ($creditmemo->getAllItems() as $item) {
                /** @var Order\Item $orderItem */
                $orderItem = $item->getOrderItem();

                if(!$quoteIsMissing)
                {
                    $quoteItem = $this->quote->getItemById($orderItem->getQuoteItemId());
                }
                if (!$quoteIsMissing && $quoteItem->getSplit() && $quoteItem->getSplit()!="Split par défaut") {
                    $splitItems[$quoteItem->getSplit()][] = $item;
                    $hasSplit=true;
                } else {
                    $splitItems[0][] = $item;
                }
            }

            $totalTTCHorsRemisePanier = 0;
            $totalTTCHorsRemise = 0;
            $totalRemises = abs($creditmemo->getDiscountAmount());

            foreach ($splitItems as $splitName => $items) {
                if($hasSplit) {
                    if($splitName=="0")
                    {
                        $splitName= "Défaut";
                    }

                    if ($this->y - 3 * 30  < self::FOOTER_HEIGHT) {
                        $page = $this->newPage();
                    }

                    $splitTotal = 0;

                    foreach($items as $item)
                    {
                        $splitTotal += $item->getRowTotalInclTax();
                    }

                    $this->_drawSplitName($page, $splitName, $splitTotal);
                }

                $this->_drawHeader($page);

                foreach($items as $item)
                {
                    if ($item->getOrderItem()->getParentItem()) {
                        continue;
                    }
                    if ($this->y - 30 < self::FOOTER_HEIGHT) {
                        $page = $this->newPage();
                        $this->_drawHeader($page);
                    }

                    $orderItem = $item->getOrderItem();

                    $totalTTCHorsRemisePanier += $item->getRowTotalInclTax();
                    $totalTTCHorsRemise += $orderItem->getOriginalPrice() * $item->getQty();

                    if ($orderItem->getOriginalPrice() - $orderItem->getPriceInclTax() > 0.01) {
                        $totalRemises += ($orderItem->getOriginalPrice() - $orderItem->getPriceInclTax()) * $item->getQty();
                    }

                    /* Draw item */
                    $page->setLineWidth(1);
                    $page->drawRectangle(20, $this->y + 11, self::HEADER_WIDTH, $this->y - 4, Zend_Pdf_Page::SHAPE_DRAW_STROKE);
                    $this->_drawItem($item, $page, $order);
                    $page = end($pdf->pages);
                }
            }

            $this->totalRemises = $totalRemises;

            if ($totalTTCHorsRemise > 0) {
                $this->prcRemises = $this->formatPrc($totalRemises / $totalTTCHorsRemise * 100);
            }

            $this->totalTTCHorsRemisePanierText = $this->order->formatPriceTxt($totalTTCHorsRemisePanier);

            /* Add totals */
            $page = $this->insertTotals($page, $creditmemo);

            if ($this->y - 140 < self::FOOTER_HEIGHT ) {
                $page = $this->newPage();
            }

            /* Add Condition de paiment */
            $this->insertRecapTVA($page);

            /* Add footer */
            $this->insertFooter();

            if ($creditmemo->getStoreId()) {
                $this->_localeResolver->revert();
            }
        }

        $this->_afterGetPdf();
        return $pdf;
    }

    protected function insertLogo(&$page, $store = null)
    {
        $this->y = self::TOP_START;
        $image = $this->_scopeConfig->getValue(
            'sales/identity/logo',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );
        if ($image) {
            $imagePath = '/sales/store/logo/' . $image;
            if ($this->_mediaDirectory->isFile($imagePath)) {
                $image = \Zend_Pdf_Image::imageWithPath($this->_mediaDirectory->getAbsolutePath($imagePath));
                $top = $this->y;
                //top border of the page
                $widthLimit = 120;
                //half of the page width
                $heightLimit = 120;
                //assuming the image is not a "skyscraper"
                $width = $image->getPixelWidth();
                $height = $image->getPixelHeight();

                //preserving aspect ratio (proportions)
                $ratio = $width / $height;
                if ($ratio > 1 && $width > $widthLimit) {
                    $width = $widthLimit;
                    $height = $width / $ratio;
                } elseif ($ratio < 1 && $height > $heightLimit) {
                    $height = $heightLimit;
                    $width = $height * $ratio;
                } elseif ($ratio == 1 && $height > $heightLimit) {
                    $height = $heightLimit;
                    $width = $widthLimit;
                }

                $y1 = $top - $height;
                $y2 = $top;
                $x1 = 25;
                $x2 = $x1 + $width;

                //coordinates after transformation are rounded by Zend
                $page->drawImage($image, $x1, $y1, $x2, $y2);

                $this->y = $y1 - 20;
            }
        }

        $this->_setFontRegular($page, 10);

        $lines = [
            'lines' => [
                [['text' => $this->franchise->getName(), 'feed' => 60, 'align' => 'center', 'font_size' => 10, 'width' => 50]],
                [['text' => $this->franchise->getAddress(), 'feed' => 60, 'align' => 'center', 'font_size' => 10, 'width' => 50]],
                [['text' => $this->franchise->getPostCode() . ' '. $this->franchise->getCity(), 'feed' => 60, 'align' => 'center', 'font_size' => 10, 'width' => 50]]
            ], 'height' => 12
        ];

        $this->drawLineBlocks($page, [$lines]);

        $this->y -= 15;
    }


    /**
     * Insert customer  address to pdf page
     *
     * @param \Zend_Pdf_Page &$page
     * @param null $store
     * @return void
     * @throws \Zend_Pdf_Exception
     */
    protected function insertAddress(&$page, $store = null)
    {
        $blockxStart = 430;
        $blockyStart = self::TOP_START;
        $blockWidth = 140;
        $headerPaddingY = 15;
        $linePaddingY = 10;
        $paddingY = 12;
        $paddingX = 5;

        /** @var Order\Address $billingAddress */
        $shippingAddress = $this->order->getShippingAddress();

        $shippingText = $shippingAddress->getCompany()
            . ' / ' . implode(' ',$shippingAddress->getStreet())
            . ' ' . $shippingAddress->getPostcode() . ' ' . $shippingAddress->getCity();

        $addressHeight = $this->_calcAddressHeight([$shippingText]);

        $billingAddress = $this->order->getBillingAddress();

        $billingText = $billingAddress->getCompany()
            . ' / ' . implode(' ',$billingAddress->getStreet())
            . ' ' . $billingAddress->getPostcode() . ' ' . $billingAddress->getCity();

        $addressHeight += $this->_calcAddressHeight([$billingText]);

        $contactText = $shippingAddress->getLastname()
            . ' ' . $shippingAddress->getFirstname()
            . ' / ' . $shippingAddress->getTelephone()
            . ' / ' . $shippingAddress->getEmail();

        $addressHeight += $this->_calcAddressHeight([$contactText]);
        $addressHeight += 3 * $headerPaddingY;

        $y = $blockyStart - $paddingY;

        $page->drawRectangle($blockxStart, $blockyStart, $blockxStart + $blockWidth, $blockyStart - $addressHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);

        $this->_setFontBold($page, 10);
        $page->drawText(strtoupper(__('Livraison')), $blockxStart + $paddingX, $y, 'UTF-8');
        $page->drawLine($blockxStart, $y - 5, $blockxStart + $blockWidth, $y - 5);
        $y -= $headerPaddingY;

        foreach ($this->string->split($shippingText, self::ADDRESS_LENGTH, true, true) as $value) {
            $this->_setFontRegular($page, 10);
            $page->drawText($value, $blockxStart + $paddingX, $y, 'UTF-8');
            $y -= $paddingY;
        }

        $this->y += $paddingY - $linePaddingY;
        $page->drawLine($blockxStart, $y, $blockxStart + $blockWidth, $y);
        $y -= $linePaddingY;

        $this->_setFontBold($page, 10);
        $page->drawText(strtoupper(__('Facturation')), $blockxStart + $paddingX, $y, 'UTF-8');
        $page->drawLine($blockxStart, $y - 5, $blockxStart + $blockWidth, $y - 5);
        $y -= $headerPaddingY;

        foreach ($this->string->split($billingText, self::ADDRESS_LENGTH, true, true) as $value) {
            $this->_setFontRegular($page, 10);
            $page->drawText($value, $blockxStart + $paddingX, $y, 'UTF-8');
            $y -= $paddingY;
        }

        $y += $paddingY - $linePaddingY;
        $page->drawLine($blockxStart, $y, $blockxStart + $blockWidth, $y);
        $y -= $linePaddingY;

        $this->_setFontBold($page, 10);
        $page->drawText(strtoupper(__('Contact')), $blockxStart + $paddingX, $y, 'UTF-8');
        $page->drawLine($blockxStart, $y - 5, $blockxStart + $blockWidth, $y - 5);
        $y -= $headerPaddingY;

        foreach ($this->string->split($contactText, self::ADDRESS_LENGTH, true, true) as $value) {
            $this->_setFontRegular($page, 10);
            $page->drawText($value, $blockxStart + $paddingX, $y, 'UTF-8');
            $y -= $paddingY;
        }

        $y -= 20;

        if ($y < $this->y) {
            $this->y = $y;
        }
    }

    protected function insertOrder(&$page, $obj, $putOrderId = true)
    {
        if ($obj instanceof \Magento\Sales\Model\Order) {
            $shipment = null;
            $order = $obj;
        } elseif ($obj instanceof \Magento\Sales\Model\Order\Shipment) {
            $shipment = $obj;
            $order = $shipment->getOrder();
        }

        $top = $this->y - 15;
        $lineMargin = 15;

        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $this->setDocHeaderCoordinates([25, $top, 570, $top - 55]);
        $this->_setFontRegular($page, 10);
        $page->setLineWidth(0.5);

        $referenceFacture = $this->shippingData->getReference();
        if ($referenceFacture) {
            $page->drawText(__('Reference facture : ') .$referenceFacture, self::LEFT_MARGIN, $top, 'UTF-8');
            $textWidth = $this->getTextWidth(__('Reference facture'), $this->_setFontRegular($page, 10), 10);
            $page->drawLine(self::LEFT_MARGIN, $top-2, self::LEFT_MARGIN + $textWidth, $top-2);
            $top -= $lineMargin;
        }

        $text = __('Numéro de commande') . ': ' . $this->order->getIncrementId();

        $textWidth = $this->getTextWidth(__('Numero de commande'), $this->_setFontRegular($page, 10), 10);
        $page->drawText($text,self::LEFT_MARGIN,$top,'UTF-8');
        $page->drawLine(self::LEFT_MARGIN, $top-2, self::LEFT_MARGIN + $textWidth, $top-2);
        $top -= $lineMargin;

        $text = __('Date de commande') . ': ' .
            $this->_localeDate->formatDate(
                $this->_localeDate->scopeDate(
                    $order->getStore(),
                    $order->getCreatedAt(),
                    true
                ),
                \IntlDateFormatter::LONG,
                false
            );

        $textWidth = $this->getTextWidth(__('Date de commande'), $this->_setFontRegular($page, 10), 10);
        $page->drawText($text,self::LEFT_MARGIN,$top,'UTF-8');
        $page->drawLine(self::LEFT_MARGIN, $top-2, self::LEFT_MARGIN + $textWidth, $top-2);
        $top -= $lineMargin;

        $text = __('Date de livraison') . ': ' .
            $this->_localeDate->formatDate(
                $this->_localeDate->scopeDate(
                    $order->getStore(),
                    $this->shippingData->getShippingDate(),
                    true
                ),
                \IntlDateFormatter::LONG,
                false
            );

        $textWidth = $this->getTextWidth(__('Date de livraison'), $this->_setFontRegular($page, 10), 10);
        $page->drawText($text,self::LEFT_MARGIN,$top,'UTF-8');
        $page->drawLine(self::LEFT_MARGIN, $top-2, self::LEFT_MARGIN + $textWidth, $top-2);
        $top -= $lineMargin;

        if($this->timeslot)
        {
            if(is_numeric($this->shippingData->getTimeslotId()))
            {
                $timeslot = $this->shippingHelper->formatTimeslot($this->timeslot->getStartHour()). '-'.$this->shippingHelper->formatTimeslot($this->timeslot->getEndHour());
            }
            else{
                if($this->timeslot=='pickup')
                {
                    $timeslot = 'Retrait en Magasin';
                }
                else
                {
                    $timeslot = 'Livraison Express';
                }
            }

            $textWidth = $this->getTextWidth(__('Creneau de livraison'), $this->_setFontRegular($page, 10), 10);
            $page->drawText(__('Créneau de livraison') . ': ' . $timeslot,self::LEFT_MARGIN,$top,'UTF-8');
            $page->drawLine(self::LEFT_MARGIN, $top-2, self::LEFT_MARGIN+$textWidth, $top-2);

            $top -= $lineMargin;
        }

        $page->setLineWidth(1);

        if ($this->y > $top) {
            $this->y = $top;
        }
    }


    /**
     * Draw header for item table
     *
     * @param \Zend_Pdf_Page $page
     * @return void
     */
    protected function _drawHeader(\Zend_Pdf_Page $page)
    {
        /* Add table head */
        $this->_setFontBold($page, 10);
        $page->setFillColor(new \Zend_Pdf_Color_Html('#003456'));
        $page->setLineWidth(1);
        $page->drawRectangle(self::LEFT_MARGIN, $this->y, self::HEADER_WIDTH, $this->y - 15);
        $page->setFillColor(new \Zend_Pdf_Color_Rgb(255, 255, 255));

        $this->y -= 11;

        //columns headers
        $lines[0][] = ['text' => mb_strtoupper(__('Designation')), 'font' => 'bold',
            'feed' => $this->getAlignCenter(
                mb_strtoupper(__('Designation')),
                self::LEFT_MARGIN,
                280,
                \Zend_Pdf_Font::fontWithName(\Zend_Pdf_Font::FONT_TIMES_BOLD),
                10
            ), 'font_size' => 10];

        $lines[0][] = ['text' => mb_strtoupper(__('Quantité')), 'feed' => 320, 'align' => 'right', 'font' => 'bold', 'font_size' => 10];

        $lines[0][] = ['text' => mb_strtoupper(__('PU HT')), 'feed' => 375, 'align' => 'right', 'font' => 'bold', 'font_size' => 10];

        $lines[0][] = ['text' => mb_strtoupper(__('TVA')), 'feed' => 425, 'align' => 'right', 'font' => 'bold', 'font_size' => 10];

        $lines[0][] = ['text' => mb_strtoupper(__('PU TTC')), 'feed' => 490, 'align' => 'right', 'font' => 'bold', 'font_size' => 10];

        $lines[0][] = ['text' => mb_strtoupper(__('Total TTC')), 'feed' => 565, 'align' => 'right', 'font' => 'bold', 'font_size' => 10];

        $lineBlock = ['lines' => $lines, 'height' => 5];

        $this->drawLineBlocks($page, [$lineBlock], ['table_header' => true]);
        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $this->y -= 10;
    }

    protected function _drawSplitName(\Zend_Pdf_Page $page, $spliteName, $splitTotal)
    {
        $this->_setFontBold($page, 10);
        $page->setFillColor(new \Zend_Pdf_Color_Html('#bda25a'));
        $page->drawRectangle(self::LEFT_MARGIN, $this->y+1, 570, $this->y - 15, Zend_Pdf_Page::SHAPE_DRAW_FILL);
        $this->y -= 10;
        $page->setFillColor(new \Zend_Pdf_Color_Rgb(255, 255, 255));
        $page->drawText($spliteName, 22, $this->y, 'UTF-8');
        $page->drawText($this->order->formatPriceTxt($splitTotal) ,
            $this->getAlignRight($this->order->formatPriceTxt($splitTotal), 22, 550, $this->_setFontBold($page, 10), 10),
            $this->y,
            'UTF-8'
        );
        $page->setFillColor(new \Zend_Pdf_Color_Rgb(0, 0, 0));
        $this->y -= 5;
    }

    public function insertDocumentNumber(\Zend_Pdf_Page $page, $text)
    {
        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $this->_setFontRegular($page, 14);
        $page->drawText($text, $this->getAlignCenter($text,0, 600, $this->_setFontBold($page, 14) ,14), self::TOP_START - 65, 'UTF-8');

        $text = __('Avoir émis le: ') .
            $this->_localeDate->formatDate(
                $this->_localeDate->scopeDate(
                    $this->creditmemo->getStoreId(),
                    $this->creditmemo->getCreatedAt(),
                    false
                ),
                \IntlDateFormatter::LONG,
                false
            );

        $page->drawText(
            $text,
            $this->getAlignCenter($text, 0, 600, $this->_setFontRegular($page, 10), 10),
            self::TOP_START - 90,
            'UTF-8'
        );
    }

    /**
     * Insert totals to pdf page
     *
     * @param \Zend_Pdf_Page $page
     * @param Order\Creditmemo $creditmemo
     * @return \Zend_Pdf_Page
     * @throws \Zend_Pdf_Exception
     */
    protected function insertTotals($page, $creditmemo)
    {
        /** @var Order $order */
        $order = $creditmemo->getOrder();
        $this->y -= 30;

        $labelStartX = 380;
        $labelWidth = 120;
        $valueStartX = $labelStartX + $labelWidth;
        $valueWidth = 70;
        $valueEndX = $valueStartX + $valueWidth;
        $rowHeight = 15;

        if ($this->y - 190 < self::FOOTER_HEIGHT ) {
            $page = $this->newPage();
        }

        /**
         * Sous total TTC
         */
        if ($creditmemo->getDiscountAmount() != 0 || $creditmemo->getShippingInclTax() > 0) {
            $page->drawRectangle($labelStartX, $this->y, $valueStartX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);
            $page->drawRectangle($valueStartX, $this->y, $valueEndX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);

            $page->drawText(__('Sous total TTC'),
                $this->getAlignRight(__('Sous total TTC'), $labelStartX, $labelWidth, $this->_setFontRegular($page, 10), 10),
                $this->y - 10,
                'UTF-8'
            );

            $page->drawText($this->totalTTCHorsRemisePanierText,
                $this->getAlignRight($this->totalTTCHorsRemisePanierText, $valueStartX, $valueWidth, $this->_setFontRegular($page, 10), 10),
                $this->y - 10,
                'UTF-8'
            );

            $this->y -= $rowHeight;
        }

        /**
         * Remise
         */
        if($creditmemo->getDiscountAmount() != 0)
        {
            $page->drawRectangle($labelStartX, $this->y, $valueStartX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);
            $page->drawRectangle($valueStartX, $this->y, $valueEndX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);

            $page->drawText(__('Remise TTC'),
                $this->getAlignRight(__('Remise TTC'), $labelStartX, $labelWidth, $this->_setFontRegular($page, 10), 10),
                $this->y - 10,
                'UTF-8'
            );

            $discountAmount = $order->formatPriceTxt(abs($creditmemo->getDiscountAmount()));

            $page->drawText($discountAmount,
                $this->getAlignRight($discountAmount, $valueStartX, $valueWidth, $this->_setFontRegular($page, 10), 10),
                $this->y - 10,
                'UTF-8'
            );

            $this->y -= $rowHeight;
        }

        /**
         * Livraison
         */
        if($creditmemo->getShippingInclTax() > 0)
        {
            $page->drawRectangle($labelStartX, $this->y, $valueStartX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);
            $page->drawRectangle($valueStartX, $this->y, $valueEndX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);

            $page->drawText(__('Frais de livraison TTC'),
                $this->getAlignRight(__('Frais de livraison TTC'), $labelStartX, $labelWidth, $this->_setFontRegular($page, 10), 10),
                $this->y - 10,
                'UTF-8'
            );

            $shippingAmount = $order->formatPriceTxt($creditmemo->getShippingInclTax());

            $page->drawText($shippingAmount,
                $this->getAlignRight($shippingAmount, $valueStartX, $valueWidth, $this->_setFontRegular($page, 10), 10),
                $this->y - 10,
                'UTF-8'
            );

            $this->y -= $rowHeight;
        }

        /**
         * Montant TTC
         */
        $page->drawRectangle($labelStartX, $this->y, $valueStartX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);
        $page->drawRectangle($valueStartX, $this->y, $valueEndX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);

        $label = strtoupper(__('Total TTC'));
        $page->drawText($label,
            $this->getAlignRight($label, $labelStartX, $labelWidth, $this->_setFontBold($page, 10), 10),
            $this->y - 11,
            'UTF-8'
        );

        $totalTTC = $order->formatPriceTxt($creditmemo->getGrandTotal());

        $page->drawText($totalTTC,
            $this->getAlignRight($totalTTC, $valueStartX, $valueWidth, $this->_setFontBold($page, 10), 10),
            $this->y - 11,
            'UTF-8'
        );

        $this->y -= $rowHeight;

        /**
         * Total remises
         */
        if ($this->totalRemises > 0) {
            $remisesLabel = __('Dont montant total des remises TTC');

            $page->drawText($remisesLabel,
                $this->getAlignRight($remisesLabel, 0, $valueStartX, $this->_setFontItalic($page, 10), 10),
                $this->y - 11,
                'UTF-8'
            );

            $amount = $this->order->formatPriceTxt($this->totalRemises);

            $page->drawText($amount,
                $this->getAlignRight($amount, $valueStartX, $valueWidth, $this->_setFontItalic($page, 10), 10),
                $this->y - 11,
                'UTF-8'
            );

            $this->y -= 11;

            /**
             * Pourcentage des remises
             */
            $remisesLabel = __('Soit un % de remise total TTC');

            $page->drawText($remisesLabel,
                $this->getAlignRight($remisesLabel, 0, $valueStartX, $this->_setFontItalic($page, 10), 10),
                $this->y - 11,
                'UTF-8'
            );

            $page->drawText($this->prcRemises,
                $this->getAlignRight($this->prcRemises, $valueStartX, $valueWidth, $this->_setFontItalic($page, 10), 10),
                $this->y - 11,
                'UTF-8'
            );
        }

        $this->y -= 30;

        /**
         * Récap taxes
         */
        $taxItems = $this->taxItem->getTaxItemsByOrderId($order->getId());

        $taxes = [];
        $taxPercent = [];
        foreach($taxItems as $taxItem)
        {
            $taxPercent[$taxItem['item_id']] = $taxItem['tax_percent'];

            if($taxItem['taxable_item_type'] == 'shipping' && $creditmemo->getShippingAmount() > 0) {
                $taxes[$taxItem['tax_percent']] = [
                    'amount' => $creditmemo->getShippingTaxAmount(),
                    'base' => $creditmemo->getShippingAmount()
                ];
            }
        }

        foreach ($creditmemo->getAllItems() as $item) {
            if (isset($taxPercent[$item->getOrderItemId()])) {
                if (!isset($taxes[$taxPercent[$item->getOrderItemId()]])) {
                    $taxes[$taxPercent[$item->getOrderItemId()]] = [
                        'amount' => $item->getTaxAmount(),
                        'base' => $item->getRowTotal()
                    ];
                } else {
                    $taxes[$taxPercent[$item->getOrderItemId()]]['amount'] += $item->getTaxAmount();
                    $taxes[$taxPercent[$item->getOrderItemId()]]['base'] += $item->getRowTotal();
                }
            }
        }

        ksort($taxes);

        $this->taxes = $taxes;

        $page->drawRectangle($labelStartX, $this->y, $valueStartX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);
        $page->drawRectangle($valueStartX, $this->y, $valueEndX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);

        $page->drawText(__('Montant total HT'),
            $this->getAlignRight(__('Montant total HT'), $labelStartX, $labelWidth, $this->_setFontRegular($page, 10), 10),
            $this->y - 10,
            'UTF-8'
        );

        $amount = $order->formatPriceTxt($creditmemo->getGrandTotal() - $creditmemo->getTaxAmount());

        $page->drawText($amount,
            $this->getAlignRight($amount, $valueStartX, $valueWidth, $this->_setFontRegular($page, 10), 10),
            $this->y - 10,
            'UTF-8'
        );

        $this->y -= $rowHeight;

        foreach ($taxes as $key => $taxValue) {
            $page->drawRectangle($labelStartX, $this->y, $valueStartX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);
            $page->drawRectangle($valueStartX, $this->y, $valueEndX, $this->y - $rowHeight, Zend_Pdf_Page::SHAPE_DRAW_STROKE);

            $label = __('Montant TVA') . ' ' . $this->formatPrc($key, 1);

            $page->drawText($label,
                $this->getAlignRight($label, $labelStartX, $labelWidth, $this->_setFontRegular($page, 10), 10),
                $this->y - 10,
                'UTF-8'
            );

            $amount = $order->formatPriceTxt($taxValue['amount']);

            $page->drawText($amount,
                $this->getAlignRight($amount, $valueStartX, $valueWidth, $this->_setFontRegular($page, 10), 10),
                $this->y - 10,
                'UTF-8'
            );

            $this->y -= $rowHeight;
        }

        $this->y -= 35;

        return $page;
    }

    /**
     * Calculate address height
     *
     * @param  array $address
     * @return int Height
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    protected function _calcAddressHeight($address)
    {
        $y = 0;
        foreach ($address as $value) {
            if ($value !== '') {
                $text = [];
                foreach ($this->string->split($value, self::ADDRESS_LENGTH, true, true) as $_value) {
                    $text[] = $_value;
                }
                foreach ($text as $part) {
                    $y += 15;
                }
            }
        }
        return $y;
    }

    /**
     * Insert Recapitulatif tva to pdf page
     *
     * @param \Zend_Pdf_Page $page
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Zend_Pdf_Exception
     */
    protected function insertRecapTVA($page)
    {
        $top = $this->y;
        /** @var Order $order */
        $this->_setFontRegular($page, 10);
        $page->setFillColor(new \Zend_Pdf_Color_Rgb(0.93, 0.92, 0.92));
        $page->setLineWidth(0.5);
        $page->drawRectangle(412, $this->y, 570, $this->y - 15, Zend_Pdf_Page::SHAPE_DRAW_FILL);
        $page->setFillColor(new \Zend_Pdf_Color_Rgb(0, 0, 0));
        $this->y-=10;
        $header = mb_strtoupper(__('Base de calcul de la TVA'));
        $page->drawText($header,
            $this->getAlignCenter($header, 418, 150, $this->_setFontRegular($page, 10), 10)
            , $this->y , 'UTF-8');
        $this->y-=15;
        $this->_setFontRegular($page, 10);

        $lineBlock['lines'][] = [
            [
                'text' => 'MONTANT HT',
                'feed' => 472,
                'align' => 'right',
                'font_size' => 9,
                'font' => 'regular',
                'height' => 15
            ],
            [
                'text' => 'TVA',
                'feed' => 500,
                'align' => 'right',
                'font_size' => 9,
                'font' => 'regular',
                'height' => 15
            ],
            [
                'text' => 'MONTANT TVA',
                'feed' => 570,
                'align' => 'right',
                'font_size' => 9,
                'font' => 'regular',
                'height' => 15
            ],
        ];


        foreach($this->taxes as $taxPercent => $datas)
        {
            $lineBlock['lines'][] = [
                [
                    'text' => $this->order->formatPriceTxt($datas['base']),
                    'feed' => 465,
                    'align' => 'right',
                    'font_size' => 10,
                    'font' => 'regular',
                    'height' => 15
                ],
                [
                    'text' => $this->formatPrc($taxPercent, 1),
                    'feed' => 505,
                    'align' => 'right',
                    'font_size' => 10,
                    'font' => 'regular',
                    'height' => 15
                ],
                [
                    'text' => $this->order->formatPriceTxt($datas['amount']),
                    'feed' => 570,
                    'align' => 'right',
                    'font_size' => 10,
                    'font' => 'regular',
                    'height' => 15
                ],
            ];

        }
        $page->drawLine(413, $this->y-1, 471, $this->y-1);
        $page->drawLine(485, $this->y-1, 500, $this->y-1);
        $page->drawLine(506, $this->y-1, 570, $this->y-1);

        $page = $this->drawLineBlocks($page, [$lineBlock]);
        $this->y = $top;

        $page->setLineWidth(1);
    }

    /**
     * Insert Conditions de paiement to pdf page
     *
     * @return \Zend_Pdf_Page
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Zend_Pdf_Exception
     */
    protected function insertFooter()
    {
        /** @var Zend_Pdf_Page $page */
        foreach($this->_getPdf()->pages as $key => $page) {
            $top = 70;
            $this->_setFontItalic($page, 10);
            $page->drawText('Page ' . ($key + 1) . '/' . count($this->_getPdf()->pages), 540, $top, 'UTF-8');
            $top -= 4;

            if (!empty($this->franchise->getInvoiceMessage())) {
                $height = 60;
            } else {
                $height = 45;
            }

            $page->drawRectangle(self::LEFT_MARGIN, $top, 580, $top - $height, Zend_Pdf_Page::SHAPE_DRAW_STROKE);
            $this->_setFontRegular($page, 7);
            $top-=10;
            $text = $this->franchise->getName() . " - " . $this->franchise->getAddress(). " " . $this->franchise->getPostCode(). " " . $this->franchise->getCity(). ' tel: '.$this->franchise->getPhone();
            $page->drawText($text, $this->getAlignCenter($text,0, 600, $this->_setFontRegular($page, 10) ,10 ), $top, 'UTF-8');
            $top-=15;

            $text = "SIRET : " . $this->franchise->getSiret()
                . " - N°TVA : " . $this->franchise->getVAT()
                . " - Forme/Capital :" . $this->franchise->getCapital()
                . " - CODE APE " . $this->franchise->getApe();
            $page->drawText($text, $this->getAlignCenter($text,0, 600, $this->_setFontRegular($page, 10) ,10 ), $top , 'UTF-8');

            if (!empty($this->franchise->getInvoiceMessage())) {
                $top-=15;
                $text = $this->franchise->getInvoiceMessage();
                $page->drawText($text, $this->getAlignCenter($text,0, 600, $this->_setFontRegular($page, 10) ,10 ), $top , 'UTF-8');
                $top-=14;
            } else {
                $top-=14;
            }

            $text = "IBAN : " . $this->franchise->getIban()
                . ' BIC : ' . $this->franchise->getCodeBanque();
            $page->drawText($text, $this->getAlignCenter($text,0, 600, $this->_setFontBold($page, 10) ,10 ), $top , 'UTF-8');
        }

        return $page;
    }

    /**
     * Override to change the default used Font
     *
     * @param \Zend_Pdf_Page $object
     * @param int $size
     * @return \Zend_Pdf_Resource_Font
     * @throws \Zend_Pdf_Exception
     */
    protected function _setFontRegular($object, $size = 7)
    {
        $font = \Zend_Pdf_Font::fontWithName(\Zend_Pdf_Font::FONT_HELVETICA); // or FONT_TIMES IBfor serif
        $object->setFont($font, $size);
        return $font;
    }

    /**
     * Override to change the default used Font
     *
     * @param \Zend_Pdf_Page $object
     * @param int $size
     * @return \Zend_Pdf_Resource_Font
     * @throws \Zend_Pdf_Exception
     */
    protected function _setFontBold($object, $size = 7)
    {
        $font = \Zend_Pdf_Font::fontWithName(\Zend_Pdf_Font::FONT_HELVETICA_BOLD); // or FONT_TIMES_BOLD for serif
        $object->setFont($font, $size);
        return $font;
    }

    /**
     * Override to change the default used Font
     *
     * @param \Zend_Pdf_Page $object
     * @param int $size
     * @return \Zend_Pdf_Resource_Font
     * @throws \Zend_Pdf_Exception
     */
    protected function _setFontItalic($object, $size = 7)
    {
        $font = \Zend_Pdf_Font::fontWithName(\Zend_Pdf_Font::FONT_HELVETICA_ITALIC); // or FONT_TIMES_ITALIC for serif
        $object->setFont($font, $size);
        return $font;
    }

    private function formatPrc($value, $precision = 2)
    {
        return str_replace('.', ',', number_format($value, $precision) . '%');
    }

    private function getTextWidth($text, Zend_Pdf_Resource_Font $font, $fontSize)
    {
        $text = iconv('', 'UTF-16BE', $text);

        $chars = array();

        for ($i = 0; $i < strlen($text); $i++) {

            $chars[] = (ord($text[$i++]) << 8) | ord($text[$i]);

        }

        $glyphs = $font->glyphNumbersForCharacters($chars);
        $widths = $font->widthsForGlyphs($glyphs);
        return (array_sum($widths) / $font->getUnitsPerEm()) * $fontSize;
    }

    private function insertPageHeader(Zend_Pdf_Page $page)
    {
        /* Add franchise info */
        $this->insertLogo($page, $this->creditmemo->getStore());
        /* Add document number */
        $this->insertDocumentNumber($page, __('AVOIR N°') . $this->creditmemo->getIncrementId());
        /* Add shipping info */
        $this->insertOrder(
            $page,
            $this->order,
            $this->_scopeConfig->isSetFlag(
                self::XML_PATH_SALES_PDF_INVOICE_PUT_ORDER_ID,
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
                $this->order->getStoreId()
            )
        );

        /* Add address */
        $this->insertAddress($page, $this->creditmemo->getStore());

        $this->y -= 30;
    }

    public function newPage(array $settings = [])
    {
        $page = parent::newPage($settings);

        $this->insertPageHeader($page);

        return $page;
    }
}

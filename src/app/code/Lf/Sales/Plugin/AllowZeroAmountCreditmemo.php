<?php

declare(strict_types=1);

namespace Lf\Sales\Plugin;

use Magento\Sales\Api\CreditmemoManagementInterface;
use Magento\Sales\Api\Data\CreditmemoInterface;
use Magento\Sales\Model\Order\Creditmemo;

class AllowZeroAmountCreditmemo
{
    /**
     * Plugin to allow credit memo creation for zero-amount orders with promotional codes
     */
    public function aroundRefund(
        CreditmemoManagementInterface $subject,
        \Closure                      $proceed,
        CreditmemoInterface           $creditmemo,
                                      $offlineRequested = false
    ) {
        if ($this->isZeroAmountOrderWithPromo($creditmemo)) {
            if ($creditmemo instanceof Creditmemo) {
                $creditmemo->setState(Creditmemo::STATE_REFUNDED);
            }
        }

        return $proceed($creditmemo, $offlineRequested);
    }

    /**
     *
     * @param CreditmemoInterface $creditmemo
     * @return bool
     */
    private function isZeroAmountOrderWithPromo(CreditmemoInterface $creditmemo): bool
    {
        $order = $creditmemo->getOrder();

        $orderTotal = $order->getGrandTotal();
        if ($orderTotal > 0.01) {
            return false;
        }

        $discountAmount = abs($order->getDiscountAmount());
        if ($discountAmount > 0) {
            return true;
        }

        if ($order->getCouponCode()) {
            return true;
        }

        $creditmemoTotal = $creditmemo->getGrandTotal();
        if ($creditmemoTotal <= 0.01 && $creditmemoTotal >= -0.01) {
            return true;
        }

        return false;
    }
}

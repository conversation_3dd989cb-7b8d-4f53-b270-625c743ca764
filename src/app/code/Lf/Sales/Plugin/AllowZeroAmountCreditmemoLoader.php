<?php

declare(strict_types=1);

namespace Lf\Sales\Plugin;

use Magento\Sales\Controller\Adminhtml\Order\CreditmemoLoader;
use Magento\Sales\Model\Order\Creditmemo;

class AllowZeroAmountCreditmemoLoader
{
    /**
     * Plugin to allow creditmemo loading for zero-amount orders with promotional codes
     */
    public function afterLoad(
        CreditmemoLoader $subject,
                         $result
    ) {
        if ($result instanceof Creditmemo) {
            if ($this->isZeroAmountOrderWithPromo($result)) {
                // Override the isValidGrandTotal method result for zero-amount orders with promo codes
                $result->setData('_zero_amount_promo_order', true);
            }
        }

        return $result;
    }

    /**
     *
     * @param Creditmemo $creditmemo
     * @return bool
     */
    private function isZeroAmountOrderWithPromo(Creditmemo $creditmemo): bool
    {
        $order = $creditmemo->getOrder();

        $orderTotal = $order->getGrandTotal();
        if ($orderTotal > 0.01) {
            return false;
        }

        $discountAmount = abs($order->getDiscountAmount());
        if ($discountAmount > 0) {
            return true;
        }

        if ($order->getCouponCode()) {
            return true;
        }

        $creditmemoTotal = $creditmemo->getGrandTotal();
        if ($creditmemoTotal <= 0.01 && $creditmemoTotal >= -0.01) {
            return true;
        }

        return false;
    }
}

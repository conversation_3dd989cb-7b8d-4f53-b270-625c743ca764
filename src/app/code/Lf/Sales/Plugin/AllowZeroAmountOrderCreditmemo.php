<?php

declare(strict_types=1);

namespace Lf\Sales\Plugin;

use Magento\Sales\Model\Order;

class AllowZeroAmountOrderCreditmemo
{
    /**
     * Plugin to allow creditmemo creation for zero-amount orders with promotional codes
     */
    public function afterCanCreditmemo(
        Order $subject,
              $result
    ) {
        if ($result) {
            return $result;
        }

        if ($this->isZeroAmountOrderWithPromo($subject)) {
            return true;
        }

        return $result;
    }

    /**
     *
     * @param Order $order
     * @return bool
     */
    private function isZeroAmountOrderWithPromo(Order $order): bool
    {
        $orderTotal = $order->getGrandTotal();
        if ($orderTotal > 0.01) {
            return false;
        }

        $discountAmount = abs($order->getDiscountAmount());
        if ($discountAmount > 0) {
            return true;
        }

        if ($order->getCouponCode()) {
            return true;
        }

        if (!$order->hasInvoices()) {
            return false;
        }

        return true;
    }
}

define([

], function () {
    return function (MultiSelect) {
        return MultiSelect.extend({
            initialize: function () {
                this._super();

                if (this.modules && this.modules.source && this.modules.source.indexOf('sales_order_grid') !== -1) {
                    this.actions.splice(0,1);
                }

                return this;
            },

            /**
             * Selects or deselects by page only.
             *
             * @returns {Multiselect} Chainable.
             */
            toggleSelectAll: function () {
                if (this.modules && this.modules.source && this.modules.source.indexOf('sales_order_grid') !== -1) {
                    this.isPageSelected(true) ?
                        this.deselectPage() :
                        this.selectPage();

                    return this;
                }

                return this._super();
            }
        })
    }
});
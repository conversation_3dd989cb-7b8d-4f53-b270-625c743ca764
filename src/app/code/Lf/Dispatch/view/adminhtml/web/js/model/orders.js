define([
    'ko',
    'jquery',
    'ko-mapping',
    'Lf_OrderManagement/js/common/messages',
], function (ko, $, koMapping, errors) {

    const EN_ATTENTE = 0,
        EN_LIVRAISON = 1,
        LIVREE = 2,
        ANNULEE = 3;

    const orders = ko.observableArray([]);
    const delivery_man = ko.observableArray([]);
    const delivery = ko.observableArray([]);
    const totalDeliveryOrders = ko.pureComputed(function () {
        var s = 0;
        ko.utils.arrayForEach(dm, function (item) {
            s += item[1]().length + item[2]().length;
        }.bind(this));
        return s;
    });
    const hasOrders = ko.pureComputed(function () {
        return orders().length > 0 ? true : false;
    });


    const ordersToDispatchSortField = ko.observable('entity_id');
    const ordersToDispatchSortOrder = ko.observable(1);

    const dm = [];
    const isShippingInProgress = [];

    const dataReady = ko.observable(false),
        zoneId = ko.observable(''),
        franchise = {
            lat: 0,
            lng: 0
        };

    const mapFilterDatas = [
        {
            field: 'all',
            order: 1,
            label: 'Toutes les commandes'
        },
        {
            field: 'not_dispatched_only',
            order: 2,
            label: 'Toutes les commandes non dispatchées'
        },
        {
            field: 'dispatched_only',
            order: 3,
            label: 'Toutes les commandes dispatchées'
        },
        {
            field: 'dispatched_not_delivery',
            order: 4,
            label: 'Toutes les commandes dispatchées mais pas en cours de livraison'
        },
        {
            field: 'in_delivery',
            order: 5,
            label: 'Toutes les commandes dispatchées en cours de livraison'
        }
    ];

    const sortDatas = [
        {
            field: 'zone_name_sort',
            order: 1,
            label: 'zone',
        },
        {
            field: 'timeslot_start_hour',
            order: 1,
            label: 'créneau horaire',
        },
        {
            field: 'entity_id',
            order: 1,
            label: 'numéro de commande',
        },
        {
            field: 'grand_total',
            order: -1,
            label: 'prix descendant',
        },
        {
            field: 'grand_total',
            order: 1,
            label: 'prix ascendant',
        },
        {
            field: 'ready_to_dispatch',
            order: -1,
            label: 'commandes prêtes',
        },
    ];

    const filterAndSortOrder = function (orderFilter, sortField, sortOrder) {
        return function () {
            return orders().filter(orderFilter).sort(function (a, b) {
                let aValue, bValue = null;

                if (ko.isObservable(a[sortField()])) {
                    aValue = a[sortField()]();
                    bValue = b[sortField()]();
                } else {
                    aValue = a[sortField()];
                    bValue = b[sortField()];
                }

                if (isNaN(aValue)) {
                    return aValue.localeCompare(bValue) * sortOrder();
                }
                return (aValue - bValue) * sortOrder();
            });
        };
    };


    const ordersModel = {
        EN_ATTENTE,
        EN_LIVRAISON,
        LIVREE,
        ANNULEE,
        orders,
        delivery_man,
        delivery,
        ordersToDispatchSortField,
        ordersToDispatchSortOrder,
        sortDatas,
        mapFilterDatas,
        hasOrders,
        dataReady,
        zoneId,
        franchise,
        dm,
        isShippingInProgress,
        totalDeliveryOrders,


        ordersToDispatch: ko.pureComputed(filterAndSortOrder(function (order) {
            return order.delivery_id() == undefined && order.dispatched() != 1;
        }.bind(this), ordersToDispatchSortField, ordersToDispatchSortOrder)),


        refreshDatas: function () {
            $.get(window.ordersRetrieveUrl, null, function (data) {
                if (!data.error) {
                    orders.removeAll();
                    zoneId(data.zone);

                    franchise.lat = parseFloat(data.franchise.latitude);
                    franchise.lng = parseFloat(data.franchise.longitude);

                    ko.utils.arrayForEach(data.orders, function (item) {
                        item.ready_to_dispatch = ko.observable(item.ready_to_dispatch);
                        item.dispatched = ko.observable(item.dispatched);
                        item.delivery_id = ko.observable(item.delivery_id);
                        item.delivery_number = ko.observable(item.delivery_number);
                        item.delivery_status = ko.observable(item.delivery_status);
                        item.delivery_man_id = ko.observable(item.delivery_man_id);
                        item.status = ko.observable(item.status);
                        item.lat = parseFloat(item.lat);
                        item.lng = parseFloat(item.lng);
                        item.zone_name_sort = item.zone_name;
                        item.timeslot_start_hour = !isNaN(parseFloat(item.timeslot_start_hour)) ? parseFloat(item.timeslot_start_hour) : parseFloat(0);
                        item.grand_total = parseFloat(item.grand_total);
                        item.zone_name = ko.observable(item.zone_name);
                        item.zone_class = ko.pureComputed(function () {
                            return 'badge small f-right ' + (item.ready_to_dispatch() == 1 ? 'badge-success' : 'badge-inverse');
                        });
                        item.computed_zone_name = ko.pureComputed(
                            function () {
                                return item.zone_name() ? item.zone_name() : 'Hors Zone';
                            }
                        );
                        orders.push(item);
                    }.bind(this));

                    delivery.removeAll();
                    ko.utils.arrayForEach(data.delivery.items, function (item) {
                        item.status = ko.observable(item.status);
                        item.number = ko.observable(item.number);
                        item.delivery_man_id = ko.observable(item.delivery_man_id);
                        delivery.push(item);
                    }.bind(this));

                    delivery_man.removeAll();
                    ko.utils.arrayForEach(data.delivery_man.items, function (item) {
                        isShippingInProgress[item.delivery_man_id] = ko.pureComputed(function () {
                            var found = false;
                            ko.utils.arrayForEach(this.delivery(), function (delivery) {
                                if (delivery.delivery_man_id() == item.delivery_man_id && delivery.status() == 1) {
                                    found = 1;
                                }
                            }.bind(this));
                            return found;
                        }.bind(this)),
                            dm[item.delivery_man_id] = {
                                1: ko.pureComputed(function () {
                                    return orders().filter(function (order) {
                                        return order.delivery_man_id() == item.delivery_man_id && order.delivery_number() == 1
                                    })
                                }),
                                2: ko.pureComputed(function () {
                                    return orders().filter(function (order) {
                                        return order.delivery_man_id() == item.delivery_man_id && order.delivery_number() == 2
                                    })
                                }),
                            };
                        delivery_man.push(item);
                    }.bind(this));

                    dataReady(true);
                    this.sortDatas = sortDatas;
                    this.mapFilterDatas = mapFilterDatas;
                } else {
                    console.log('Unable to retrieve orders datas');
                }
            }.bind(this))
                .fail(function () {
                    console.log("error");
                });
        },
    };

    //on récupère une première fois les données
    ordersModel.refreshDatas();

    return ordersModel;

});

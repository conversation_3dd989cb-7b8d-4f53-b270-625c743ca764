define([
    'uiComponent',
    'ko',
    'jquery',
    'moment',
    './model/model',
    'mage/calendar',
    'overlap',
    'jquery-ui-modules/datepicker',
    'bootstrap-multiselect'
], function (Component, ko, $, moment, model) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Lf_Dispatch/layout',
            logoSrc: window.logoSrc,
            dateOfTheDay: window.dateOfTheDay,
            selectedDate: ko.observable(new Date()),
            model: model,
            inputId: '#datetimepicker',
            selectId: '#timeslot-picker'
        },

        initialize: function () {
            this._super();
            this.selectedValue = ko.observable();
            this.onTimeslotChange = this.onTimeslotChange.bind(this);
            this.timeslotAfterRender = this.timeslotAfterRender.bind(this);
            return this;
        },
        initObservable: function () {
            this._super().observe({
                timeslot: ''
            });

            this.timeslotsInDb = ko.observableArray();
            this.selectedTimeslots = ko.observableArray([]);
            ko.utils.arrayForEach(window.timeslots, function(timeslot) {
                timeslot.zoneId = ko.observable(timeslot.zoneId);
                this.timeslotsInDb.push(timeslot);
            }.bind(this));

            this.timeslotsInDb.push(
                {
                    id: 'express_express',
                    zoneId: model.zoneId,
                    value: 'Livraison express'
                },
                {
                    id: 'pickup_pickup',
                    zoneId: model.zoneId,
                    value: 'Retrait en magasin'
                });

                this.filteredTimeslots = ko.computed(
                    function () {
                        var uniqueTimeslots = {};

                        var orderTimeslots = model.orders().map(function(order) {

                            return order.timeslot_id;
                        });

                        ko.utils.arrayForEach(this.timeslotsInDb(), function(timeslot) {
                            var value = String(timeslot.value).trim();

                            if (value && orderTimeslots.includes(timeslot.id)) {
                                uniqueTimeslots[timeslot.id] = value;
                            }
                        });
                        return uniqueTimeslots;
                    }, this
                );

            ko.computed(function () {
                if (!model.timeslot()) {
                    $(this.selectId).val('');
                } else {
                    const selectedOption = $(this.selectId).find('option[value="' + model.timeslot() + '"]');
                    if (selectedOption.length !== 0) {
                        $(this.selectId).val(model.timeslot());
                    } else {
                        this.selectedValue(model.timeslot());
                        $(this.selectId).val('');
                        model.timeslot(null);
                    }
                }
            }, this);

            return this;
        },

        onTimeslotChange: function (source, event) {
            const newValue = $(event.target).val();
            const oldValue = model.timeslot();

            if (newValue === oldValue) {
                return;
            }

            model.timeslot(newValue);

        },
        timeslotAfterRender: function () {
            if (this.selectedValue != null && this.selectedValue != undefined) {
                const selectedOption = $("#timeslot-picker").find('option[value="' + this.selectedValue() + '"]');
                if (selectedOption.length !== 0) {
                    $("#timeslot-picker").val(this.selectedValue());
                }
                return this.selectedValue();
            }
        },

        creneauAfterRender: function (element) {
            const self = this;

            $(element).multiselect({
                includeSelectAllOption: true,
                enableFiltering: true,
                nonSelectedText: 'Choix des créneaux',
                allSelectedText: 'Tous les créneaux sélectionnés',
                nSelectedText: 'créneaux sélectionnés'
            })

            .on('change', function() {
                var selectedOptions = $(this).val();
                self.selectedTimeslots(selectedOptions);
            });
        },
        hasOrders: function () {
            return model.hasOrders();
        },

        showMap: function () {
            $('.col-lg-100, .hidemap').show();
            $('.col-lg-26, .col-lg-77, .viewmap').hide();
            window.location.hash = "map";
        },

        hideMap: function () {
            $('.col-lg-100, .hidemap').hide();
            $('.col-lg-26, .col-lg-77, .viewmap').show();
            window.location.hash = "";
        },

        setupPicker: function (element) {
            const today = new Date(),
                $element = $(element),
                self = this;

             this.$datePicker = $element;

            $element.on('change', function () {
                self.selectedDate(null);
            });
            moment.lang('fr');
            $element.val(moment(today).format('LL'));

            $element.calendar({
                changeYear: false,
                changeMonth: false,
                showButtonPanel: false,
                showOn: 'focus',
                defaultDate: today,
                minDate: today,
                dateFormat: 'DD d MMMM yy',
                beforeShow: function (input, inst) {
                    $('#ui-datepicker-div').addClass("ll-skin-latoja");

                },
                onSelect: function (dateText, inst) {
                    var newDate = $element.datepicker('getDate');
                    $element.val(dateText);
                    model.selectedDate(newDate);
                    model.refreshDatas();
                },
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-crosshairs',
                    clear: 'fa fa-trash'
                }
            });
        },
    });


});

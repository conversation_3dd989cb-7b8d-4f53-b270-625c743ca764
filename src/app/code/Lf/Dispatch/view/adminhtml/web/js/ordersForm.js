define([
    'uiComponent',
    'knockout',
    'jquery',
    './model/model',
    'Magento_Catalog/js/price-utils',
    'Lf_OrderManagement/js/common/messages',
    'bootstrap',
    'overlap'
], function (Component, ko, $, model, priceUtils, errors) {
    'use strict';

    var Selector = '[data-toggle="portlet"]';
    var SelectorTournee = '[data-toggle="tournee"]';
    var SelectorLivreur = '[data-toggle="livreur"]';
    var draggedItems;

    const kmlFile = "/media/lafamille.xml";

    function updateSort(pr, event, ui) {
        var portletTo = $(event.target).attr('id');

        var aPortletTo = portletTo.split('-');

        var portletId = aPortletTo[1];
        if (portletId > 2) {
            const jqXHR = $.post({
                url: window.deliveryOrderSort,
                data: {
                    orderIds: $(pr).sortable('toArray'),
                }
            });

            jqXHR.done(function (data) {
                if (data !== null && data.error) {
                    errors.showError(data.error);
                }
            }.bind(this)).fail(errors.showUnkownError);
        }
    }

    function updateOrderState(pr, event, ui) {
        var items = draggedItems;

        $(items).each(function () {

            var item = this;

            var orderId = $(item).attr('data-commande');
            var portletTo = $(event.target).attr('id');

            var aPortletTo = portletTo.split('-');

            var portletId = aPortletTo[1];

            var deliveryManId = 0;
            var deliveryNumber = 0;

            if (portletId > 2) {
                var deliveryManId = aPortletTo[2];
                var deliveryNumber = aPortletTo[3];
            }
            const jqXHR = $.post({
                url: window.ordersDispatchUrl,
                data: {
                    orderId: orderId,
                    deliveryManId: deliveryManId,
                    deliveryNumber: deliveryNumber,
                }
            });

            jqXHR.done(function (data) {
                if (data !== null && data.error) {
                    errors.showError(data.error);
                }

                var searchOrder = model.orders().find(function (order) {
                    return order.entity_id == orderId;
                });

                if (searchOrder !== -1) {
                    if (portletId > 2) {
                        $('#' + portletTo + ' > [data-commande=' + orderId + ']').remove();
                        searchOrder.dispatched(1);
                        searchOrder.delivery_id(data.delivery.id);
                    } else {
                        searchOrder.dispatched(0);
                    }
                    searchOrder.delivery_number(deliveryNumber);
                    searchOrder.delivery_man_id(deliveryManId);
                }

                var searchDelivery = model.delivery().find(function (delivery) {
                    return delivery.id == data.delivery.id;
                });

                if (searchDelivery !== -1 && searchDelivery !== undefined) {
                    searchDelivery.status(data.delivery.status);
                    searchDelivery.number(data.delivery.number);
                    searchDelivery.delivery_man_id(data.delivery.delivery_man_id);
                } else {
                    if ('delivery' in data) {
                        if ('status' in data.delivery) {
                            data.delivery.status = ko.observable(data.delivery.status);
                            data.delivery.number = ko.observable(data.delivery.number);
                            data.delivery.delivery_man_id = ko.observable(data.delivery.delivery_man_id);
                            model.delivery.push(data.delivery);
                        }
                    }
                }

            }.bind(this)).fail(errors.showUnkownError);

        });

    }

    function countUpFromTime(countFrom, id) {
        countFrom = new Date(countFrom).getTime();
        var now = new Date(),
            nowCorrected = now.setHours(now.getHours() - 1),
            countFrom = new Date(countFrom),
            timeDifference = (nowCorrected - countFrom);
        var secondsInADay = 60 * 60 * 1000 * 24,
            secondsInAHour = 60 * 60 * 1000;
        var hours = Math.floor((timeDifference % (secondsInADay)) / (secondsInAHour) * 1);
        var mins = Math.floor(((timeDifference % (secondsInADay)) % (secondsInAHour)) / (60 * 1000) * 1);
        var secs = Math.floor((((timeDifference % (secondsInADay)) % (secondsInAHour)) % (60 * 1000)) / 1000 * 1);
        var idEl = $("#" + id);
        if (hours != 0) {
            var formathours = ("0" + hours).slice(-2);
            idEl.find('.hours').html(formathours + " :");
        }
        var formatmins = ("0" + mins).slice(-2);
        idEl.find('.minutes').html(formatmins + " :");
        var formatsecs = ("0" + secs).slice(-2);
        idEl.find('.seconds').html(formatsecs);
        clearTimeout(eval(id).interval);
        eval(id).interval = setTimeout(function () {
            countUpFromTime(countFrom, id);
        }, 1000);
    }

    return Component.extend({

        defaults: {
            template: 'Lf_Dispatch/orders-form',
            displayArea: 'orders-form',
            markers: [],
            mapFilterStatus: 0,
        },


        initialize: function () {
            this._super();
            this.orders = model.orders;
            this.isShippingInProgress = model.isShippingInProgress;
            this.dm = model.dm;
            this.totalDeliveryOrders = model.totalDeliveryOrders;

            this.delivery_man = model.delivery_man;

            this.delivery = model.delivery;

            this.ordersToDispatchSortField = model.ordersToDispatchSortField;
            this.ordersToDispatchSortOrder = model.ordersToDispatchSortOrder;

            this.ordersToDispatch = model.ordersToDispatch;

            this.sortDatas = model.sortDatas;
            this.mapFilterDatas = model.mapFilterDatas;
            this.sortit = this.sortit.bind(this);
            this.mapFilter = this.mapFilter.bind(this);
            this.deliveryLaunch = this.deliveryLaunch.bind(this);

            if (window.location.hash == "#map") {
                setTimeout(function () {
                    $('.col-lg-100, .hidemap').show();
                    $('.col-lg-26, .col-lg-77, .viewmap').hide();
                }, 200);
            }

            return this;
        },

        afterRender: function (element) {
            setTimeout(function () {

                $("body").on("click", "[data-toggle='portlet'] .card.o-1", function (event) {
                    if (event.ctrlKey) {
                        if ($(this).parent().hasClass("ui-sortable")) {
                            $(this).toggleClass("selected");
                        }
                    }
                });

                $("body").on("click", "[data-toggle='livreur'] .card.o-1", function () {
                    if (event.ctrlKey) {
                        if ($(this).parent().hasClass("ui-sortable")) {
                            $(this).toggleClass("selected");
                        }
                    }
                });

                $('div[style="color: transparent; font-size: 15px; font-weight: bold; font-family: Roboto, Arial, sans-serif;"]:contains("data-bou")').each(function () {
                    $(this).html($(this).html().replace(/\&lt;/g, "<").replace(/\&gt;/g, ">").replace(":::", "<br>"));
                });

                google.maps.event.addListener(this.map, "mouseover", function () {
                    $('div[style="color: transparent; font-size: 15px; font-weight: bold; font-family: Roboto, Arial, sans-serif;"]:contains("data-bou")').each(function () {
                        $(this).html($(this).html().replace(/\&lt;/g, "<").replace(/\&gt;/g, ">").replace(":::", "<br>"));
                    });
                });
                google.maps.event.addListener(this.map, "dragstart", function () {
                    $('div[style="color: transparent; font-size: 15px; font-weight: bold; font-family: Roboto, Arial, sans-serif;"]:contains("data-bou")').each(function () {
                        $(this).html($(this).html().replace(/\&lt;/g, "<").replace(/\&gt;/g, ">").replace(":::", "<br>"));
                    });
                });
                google.maps.event.addListener(this.map, "drag", function () {
                    $('div[style="color: transparent; font-size: 15px; font-weight: bold; font-family: Roboto, Arial, sans-serif;"]:contains("data-bou")').each(function () {
                        $(this).html($(this).html().replace(/\&lt;/g, "<").replace(/\&gt;/g, ">").replace(":::", "<br>"));
                    });
                });
                google.maps.event.addListener(this.map, "dragend", function () {
                    $('div[style="color: transparent; font-size: 15px; font-weight: bold; font-family: Roboto, Arial, sans-serif;"]:contains("data-bou")').each(function () {
                        $(this).html($(this).html().replace(/\&lt;/g, "<").replace(/\&gt;/g, ">").replace(":::", "<br>"));
                    });
                });
                google.maps.event.addListener(this.map, "mousemove", function () {
                    $('div[style="color: transparent; font-size: 15px; font-weight: bold; font-family: Roboto, Arial, sans-serif;"]:contains("data-bou")').each(function () {
                        $(this).html($(this).html().replace(/\&lt;/g, "<").replace(/\&gt;/g, ">").replace(":::", "<br>"));
                    });
                });


                if (window.location.hash == "#map") {
                    setTimeout(function () {
                        $('.col-lg-100, .hidemap').show();
                        $('.col-lg-26, .col-lg-77, .viewmap').hide();
                    }, 200);
                }

                $(Selector).sortable({
                    connectWith: Selector + " , " + SelectorLivreur,
                    items: 'div.card',
                    handle: '.portlet-handler',
                    opacity: 0.7,
                    delay: 100,
                    placeholder: 'portlet box-placeholder',
                    cancel: '.portlet-cancel',
                    forcePlaceholderSize: true,
                    iframeFix: false,
                    tolerance: 'pointer',
                    update: function (event, ui) {
                        updateSort(this, event, ui)
                    },
                    receive: function (event, ui) {
                        updateOrderState(this, event, ui)
                    },
                    helper: function (e, item) {
                        if (!item.hasClass('selected')) {
                            item.parent().children('.selected').removeClass('selected');
                            item.addClass('selected');
                        }
                        draggedItems = $('.card.o-1.selected');
                        var selected = item.parent().children('.selected').clone();

                        item.data('multidrag', selected).siblings('.selected').remove();

                        return $('<div class="isSelected">').append(selected);

                    },
                    stop: function (e, ui) {
                        var selected = ui.item.data('multidrag');
                        ui.item.after(selected);
                        ui.item.remove();
                        $(".card.o-1").removeClass('selected');
                    },
                    revert: 200,
                    forceHelperSize: true,
                });


                $(SelectorTournee).sortable({
                    connectWith: '.inconnu',
                    items: 'div.card',
                    handle: '.portlet-handler',
                    opacity: 0.7,
                    placeholder: 'portlet box-placeholder cf',
                    cancel: '.portlet-cancel',
                    forcePlaceholderSize: true,
                    iframeFix: false,
                    tolerance: 'pointer',
                    update: function (event, ui) {
                        updateSort(this, event, ui)
                    },
                    receive: function (event, ui) {
                        updateOrderState(this, event, ui)
                    },
                    helper: function (e, item) {
                        if (!item.hasClass('selected')) {
                            item.addClass('selected');
                        }
                        draggedItems = item;
                        var selected = item.parent().children('.selected').clone();
                        item.data('multidrag', selected).siblings('.selected').remove();

                        return $('<div class="isSelected2"/>').append(selected);

                    },
                    stop: function (e, ui) {
                        var selected = ui.item.data('multidrag');
                        ui.item.after(selected);
                        ui.item.remove();
                        $(".card.o-1").removeClass('selected');
                    },
                    revert: 200,
                    forceHelperSize: true,
                });

                $(SelectorLivreur).sortable({
                    connectWith: Selector + " , " + SelectorLivreur,
                    items: 'div.card',
                    handle: '.portlet-handler',
                    opacity: 0.7,
                    delay: 100,
                    placeholder: 'portlet box-placeholder cf',
                    cancel: '.portlet-cancel',
                    forcePlaceholderSize: true,
                    iframeFix: false,
                    tolerance: 'pointer',
                    update: function (event, ui) {
                        updateSort(this, event, ui)
                    },
                    receive: function (event, ui) {
                        updateOrderState(this, event, ui)
                    },
                    helper: function (e, item) {

                        if (!item.hasClass('selected')) {
                            item.addClass('selected');
                        }
                        draggedItems = $('.card.o-1.selected');

                        item.parent(".tournee").addClass("withouteight");
                        var selected = item.parent().children('.selected').clone();
                        item.data('multidrag', selected).siblings('.selected').remove();

                        return $('<div class="isSelected2"/>').append(selected);

                    },
                    stop: function (e, ui) {
                        var selected = ui.item.data('multidrag');
                        ui.item.after(selected);
                        ui.item.remove();
                        $(".tournee").css("height", "100%");
                        $(".card.o-1").removeClass('selected');
                    },
                    revert: 200,
                    forceHelperSize: true,
                });


                $(".mycontainer").on('mouseenter', function () {
                    var _this = $(this);
                    setTimeout(function () {
                        _this.find(".tournee").addClass("withouteight hovered");
                    }, 100);

                }).on('mouseleave', function () {
                    var _this = $(this);
                    _this.find(".tournee").removeClass("withouteight hovered");
                });

            }.bind(this), 1000);

        },

        deliveryLaunch: function (item, event) {

            const jqXHR = $.post({
                url: window.deliveryLaunch,
                data: {
                    deliveryManId: item.delivery_man_id,
                }
            });

            jqXHR.done(function (data) {
                if (data !== null && data.error) {
                    errors.showError(data.error);
                }

                var searchDelivery = this.delivery().find(function (delivery) {
                    return delivery.id == data.delivery.id;
                }.bind(this));

                if (searchDelivery !== -1 && searchDelivery !== undefined) {
                    searchDelivery.status(1);
                }

                model.orders().forEach((order) => {
                    // Passage du statut de livraison à 1 côté client pour que les filtres puissent se baser dessus.
                    if (order.delivery_id() === data.delivery.id && order.dispatched() == 1) {
                        order.delivery_status(1);
                    }
                });

                // cedric to refacto ?
                $(SelectorTournee).sortable({
                    connectWith: '.inconnu',
                    items: 'div.card',
                    handle: '.portlet-handler',
                    opacity: 0.7,
                    placeholder: 'portlet box-placeholder cf',
                    cancel: '.portlet-cancel',
                    forcePlaceholderSize: true,
                    iframeFix: false,
                    delay: 100,
                    tolerance: 'pointer',
                    update: function (event, ui) {
                        updateSort(this, event, ui)
                    },
                    receive: function (event, ui) {
                        updateOrderState(this, event, ui)
                    },
                    helper: function (e, item) {
                        if (!item.hasClass('selected')) {
                            item.addClass('selected');
                        }
                        draggedItems = item;
                        var selected = item.parent().children('.selected').clone();
                        item.data('multidrag', selected).siblings('.selected').remove();

                        return $('<div class="isSelected2"/>').append(selected);

                    },
                    stop: function (e, ui) {
                        var selected = ui.item.data('multidrag');
                        ui.item.after(selected);
                        ui.item.remove();
                        $(".card.o-1").removeClass('selected');
                    },
                    revert: 200,
                    forceHelperSize: true,
                });
                // cedric to refacto ?


            }.bind(this)).fail(errors.showUnkownError);

        },

        initMap: function (element) {
            const showZone = this.showZone.bind(this);

            this.directionService = new google.maps.DirectionsService();

            this.map = new google.maps.Map(element, {
                zoom: 12,
                scaleControl: true,
                mapTypeId: google.maps.MapTypeId.ROADMAP
            });
            //
            // this.directionRenderer = new google.maps.DirectionsRenderer({suppressMarkers: true});
            // this.directionRenderer.setMap(this.map);
            //
            if (!model.dataReady()) {
                model.dataReady.subscribe(function () {
                    this.map.setCenter(model.franchise);
                    new geoXML3.parser({afterParse: showZone}).parse(kmlFile);
                    this.showAllMarkers();
                }.bind(this));
            } else {
                this.map.setCenter(model.franchise);
                new geoXML3.parser({afterParse: showZone}).parse(kmlFile);
                this.showAllMarkers();
            }
        },

        showZone: function (docs) {

            var mymap = this.map;
            $.each(docs[0].placemarks, function () {
                var _this = this;
                var fillcolor = _this.styleUrl.substring(6, 12);
                var polygon = new google.maps.Polygon({
                    path: _this.Polygon[0].outerBoundaryIs[0].coordinates,
                    geodesic: true,
                    strokeColor: '#FFd000',
                    strokeOpacity: 1.0,
                    strokeWeight: 4,
                    clickable: false,
                    fillColor: '#' + fillcolor,
                    fillOpacity: 0.35
                });
                polygon.setMap(mymap);
            });

        },

        pinSymbolOrder: function (order, bg) {

            let color = '#8d9fbc';

            var livreur = "";

            livreur = $(".livree[data-commande='" + parseInt(order.entity_id).toString() + "']").attr("data-livreur");
            if (livreur !== undefined) {
                color = '#3F8C2C';
            }

            livreur = $(".inProgress[data-commande='" + parseInt(order.entity_id).toString() + "']").attr("data-livreur");
            if (livreur !== undefined) {
                color = '#003456';
            }

            livreur = $(".undelivered[data-commande='" + parseInt(order.entity_id).toString() + "']").attr("data-livreur");
            if (livreur !== undefined) {
                color = '#f05050';
            }


            return this.pinSymbol(color);
        },

        pinSymbol: function (color) {
            return {
                path: 'M 0,0 C -2,-20 -10,-22 -10,-30 A 10,10 0 1,1 10,-30 C 10,-22 2,-20 0,0 z',
                fillColor: color,
                fillOpacity: 1,
                strokeColor: '#000',
                labelOrigin: {x: 0, y: -28},
                strokeWeight: 2,
                scale: 1
            };
        },

        resetMarkers: function () {
            this.markers.forEach(function (m) {
                m.setMap(null)
            });
        },

        showAllMarkers: function () {
            this.resetMarkers();


            var oms = new OverlappingMarkerSpiderfier(this.map, {
                markersWontMove: true,   // we promise not to move any markers, allowing optimizations
                markersWontHide: true,   // we promise not to change visibility of any markers, allowing optimizations
                basicFormatEvents: true  // allow the library to skip calculating advanced formatting information
            });


            const request = {
                origin: model.franchise,
                destination: model.orders()[model.orders().length - 1],
                travelMode: google.maps.TravelMode.DRIVING,
                waypoints: []
            };

            model.orders().forEach(function (o, index) {
                // On n'affiche jamais les commandes d'une tournée terminée (commandes livrées)
                if (o.delivery_id() === null && o.dispatched() == 1) {
                    return
                }

                // Filtre sur les commandes non dispatchées (pour lesquelles delivery_man_id vaut null ou 0)
                if (this.mapFilterStatus === 'not_dispatched_only' && o.delivery_man_id() > 0) {
                    return;
                }

                // Filtre sur les commandes dispatchées (pour lesquelles delivery_man_id est supérieur à 0)
                if (this.mapFilterStatus === 'dispatched_only' && (o.delivery_man_id() == null || o.delivery_man_id() == 0)) {
                    return;
                }

                if (this.mapFilterStatus === 'dispatched_not_delivery' && (
                    (o.delivery_man_id() == null || o.delivery_man_id() == 0) || o.delivery_status() == 1)
                ) {
                    return;
                }

                if (this.mapFilterStatus === 'in_delivery' && o.delivery_status() != 1) {
                    return;
                }

                request.waypoints.push({
                    location: o,
                    stopover: true
                });
                var bg = "";
                var text = parseInt(o.colis_id).toString();
                var livreur = "";
                var color = 'white';

                if (o.delivery_status() == 1) {

                    livreur = $(".inProgress[data-commande='" + parseInt(o.entity_id).toString() + "']").attr("data-livreur");

                    if (livreur !== undefined) {
                        var countFrom = new Date(o.updated_at).getTime();
                        var id = 'timer' + livreur.slice(-2);
                        clearTimeout(eval(id).interval);
                        eval(id).interval = setTimeout(function () {
                            countUpFromTime(countFrom, id);
                        }, 1000);

                        color = 'transparent';
                        var started = new Date(o.updated_at);
                        text = '<div data-bou="1"><span><em class="fa fa-spinner vert rotating"></em> ' + livreur + '</span> ::: ' + parseInt(o.colis_id).toString() + '</div>';
                    } else {
                        bg = "#3F8C2C";
                    }
                }

                const marker = new google.maps.Marker({
                    position: o,
                    label: {
                        text: text,
                        color: color,
                        fontSize: "15px",
                        fontWeight: "bold"
                    },
                    icon: this.pinSymbolOrder(o, bg)
                });

                marker.setMap(this.map);
                this.markers.push(marker);

                oms.addMarker(marker);


            }, this);

        },

        mapFilter: function (data, event) {
            this.mapFilterStatus = data.field;
            this.showAllMarkers();
        },

        sortit: function (data, event) {
            var colType = $(event.target).closest('.col-lg-26').data('type');
            eval('this.orders' + colType + 'SortField(data.field)');
            eval('this.orders' + colType + 'SortOrder(data.order)');
        },

        hasOrders: function () {
            return model.hasOrders();
        },

        formatOrderName: function (item) {
            return "Commande " + parseInt(item.colis_id);
        },

        formatCustomerName: function (item) {
            return item.shipping_address_lastname + " " + item.shipping_address_firstname;
        },

        formatPrice: function (price) {
            return priceUtils.formatPrice(price) + " €";
        },

        formatTimeslotLabel: function (item) {
            if (isNaN(item.timeslot_id)) {
                return item.timeslot_id;
            }
            return this.formatTimeslot(item.timeslot_start_hour) + " - " + this.formatTimeslot(item.timeslot_end_hour);
        },

        isShipped: function (item) {
            return (item.delivery_order_status == 2) ? true : false;
        },

        hasNoCard: function (item) {
            return (this.isShipped(item)
                || this.isCanceled(item)
                || this.isInProgress(item)) ? true : false;
        },

        isInProgress: function (item) {
            return (item.delivery_order_status == 1) ? true : false;
        },

        isCanceled: function (item) {
            return (item.delivery_order_status == 3) ? true : false;
        },

        isReady: function (item) {
            return (item.production_ready == "1") ? true : false;
        },

        isDeliveryLaunchable: function (delivery_man) {
            return !this.isShippingInProgress[delivery_man.delivery_man_id]() && !this.dm[delivery_man.delivery_man_id][1]().length == 0 && this.dm[delivery_man.delivery_man_id][1]().every(function (order) {
                return order.ready_to_dispatch() == 1
            })
        },

        formatTimeslot: function (hours) {
            var sign = hours < 0 ? "-" : "";
            var hour = Math.floor(Math.abs(hours));
            var min = Math.floor((Math.abs(hours) * 60) % 60);
            return sign + hour + "h" + (min == 0 ? "" : (min < 10 ? "0" : "") + min);
        },

        hasPortlet: function (delivery_man_id) {
            if (this.isShippingInProgress[delivery_man_id]()) {
                return "tournee";
            }
            return "livreur";
        },

        switchTournee: function (item, event) {
            var tournee = $(event.target).closest('a').data('tournee');
            var deliveryManId = $(event.target).closest('.card').data('id');
            var container = $("#livreur-" + deliveryManId);
            container.find(".card-header .h4").addClass("hidden");
            container.find(".card-body.tournee").addClass("hidden");
            container.find(".card-header .h4[data-tournee=" + tournee + "]").removeClass("hidden");
            container.find(".card-body.tournee[data-tournee=" + tournee + "]").removeClass("hidden");
        }


    });
});

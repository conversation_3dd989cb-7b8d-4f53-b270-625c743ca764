<?php

namespace Lf\Dispatch\Model\Api\SearchCriteria\JoinProcessor;

use Magento\Framework\Api\SearchCriteria\CollectionProcessor\JoinProcessor\CustomJoinInterface;
use Magento\Framework\Data\Collection\AbstractDb;

/**
 * Class Rate
 * @package Lf\Order\Model\Api\SearchCriteria\JoinProcessor
 */
class ShippingData implements CustomJoinInterface
{
    /**
     * @param \Magento\Tax\Model\ResourceModel\Calculation\Rule\Collection $collection
     * @return true
     */
    public function apply(AbstractDb $collection)
    {
        $aliasShipping = 'shipping_data';
        $aliasQuote = 'quote';
        $tableName = 'quote_shipping_datas';
        $aliasTimeslot = 'timeslot';
        $timeslotTableName = 'timeslot';
        $aliasZone = 'zone';
        $zoneTableName = 'zone';
        $aliasShippingOrderAddress = 'sales_order_shipping_address';
        $orderAddressableName = 'sales_order_address';
        $aliasDeliveryOrder = 'delivery_order';
        $deliveryOrderTableName = 'delivery_order';
        $aliasDelivery = 'delivery';
        $deliveryTableName = 'delivery';

        $collection->getSelect()
            ->joinLeft(
                [$aliasQuote => $collection->getTable($aliasQuote)],
                "main_table.quote_id = {$aliasQuote}.entity_id",
                []
            )
            ->joinLeft(
                [$aliasShipping => $collection->getTable($tableName)],
                "{$aliasQuote}.entity_id = {$aliasShipping}.quote_id",
                ["timeslot_id" => "{$aliasShipping}.timeslot_id"]
            )
            ->joinLeft(
                [$aliasTimeslot => $collection->getTable($timeslotTableName)],
                "{$aliasShipping}.timeslot_id = {$aliasTimeslot}.timeslot_id",
                ["timeslot_start_hour" => "{$aliasTimeslot}.start_hour","timeslot_end_hour" => "{$aliasTimeslot}.end_hour", "timeslot_is_retention" => "{$aliasTimeslot}.is_retention"]
            )
            ->joinLeft(
                [$aliasZone => $collection->getTable($zoneTableName)],
                "{$aliasShipping}.zone = {$aliasZone}.code",
                ["zone_id" => "{$aliasZone}.zone_id","zone_name" => "{$aliasZone}.code"]
            )
            ->joinLeft(
                [$aliasDeliveryOrder => $collection->getTable($deliveryOrderTableName)],
                "main_table.entity_id = {$aliasDeliveryOrder}.sales_order_id",
                [
                    "delivery_id" => "{$aliasDeliveryOrder}.delivery_id",
                    "delivery_order_status" => "{$aliasDeliveryOrder}.status",
                    "delivery_order_sort" => "{$aliasDeliveryOrder}.sort",
                ]
            )
            ->joinLeft(
                [$aliasDelivery => $collection->getTable($deliveryTableName)],
                "{$aliasDelivery}.id = {$aliasDeliveryOrder}.delivery_id",
                [
                    "delivery_number" => "{$aliasDelivery}.number",
                    "delivery_man_id" => "{$aliasDelivery}.delivery_man_id",
                    "delivery_status" => "{$aliasDelivery}.status",
                ]
            )
            ->joinLeft(
                [$aliasShippingOrderAddress => $collection->getTable($orderAddressableName)],
                "{$aliasShippingOrderAddress}.parent_id= main_table.entity_id and {$aliasShippingOrderAddress}.address_type='shipping'",
                [
                    "shipping_address_company" => "{$aliasShippingOrderAddress}.company",
                    "shipping_address_lastname" => "{$aliasShippingOrderAddress}.lastname",
                    "shipping_address_firstname" => "{$aliasShippingOrderAddress}.firstname",
                ]
            )
            ->order(["{$aliasDeliveryOrder}.sort"]);
        ;

        return true;
    }
}

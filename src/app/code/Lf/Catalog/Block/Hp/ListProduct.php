<?php

/**
 * @category  Lf
 * @package   Lf_Catalog
 * @copyright 2018 Adin
 */

namespace Lf\Catalog\Block\Hp;

use Lf\Core\Helper\Stringstuffs;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Framework\App\ActionInterface;

class ListProduct extends \Magento\Catalog\Block\Product\AbstractProduct
{

    protected $products;
    protected $categories;
    protected $categoryFactory;
    protected $productFactory;
    protected $categoryHelper;

    /**
     * @var ImageBuilder
     * @since 101.1.0
     */
    protected $imageBuilder;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;


    private $stringstuffsHelper;


    /**
     * @var Data
     */
    private $urlHelper;
    private CategoryRepositoryInterface $categoryRepository;

    public function __construct(
        \Magento\Catalog\Block\Product\Context $context,
        \Magento\Catalog\Helper\Category $categoryHelper,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Catalog\Model\CategoryFactory  $categoryFactory,
        \Magento\Catalog\Model\ProductFactory  $productFactory,
        CategoryRepositoryInterface $categoryRepository,
        \Magento\Framework\Url\Helper\Data $urlHelper,
        Stringstuffs $stringstuffsHelper,
        array $data = array())
    {
        parent::__construct($context, $data);
        $this->imageBuilder = $context->getImageBuilder();
        $this->categoryFactory = $categoryFactory;
        $this->stringstuffsHelper = $stringstuffsHelper;
        $this->productFactory = $productFactory;
        $this->categoryHelper= $categoryHelper;
        $this->storeManager = $storeManager;
        $this->categoryRepository = $categoryRepository;
        $this->urlHelper = $urlHelper;
    }


    /**
     * Get post parameters
     *
     * @param Product $product
     * @return string
     */
    public function getAddToCartPostParams(Product $product)
    {
        $url = $this->getAddToCartUrl($product);
        return [
            'action' => $url,
            'data' => [
                'product' => $product->getEntityId(),
                ActionInterface::PARAM_NAME_URL_ENCODED => $this->urlHelper->getEncodedUrl($url),
            ]
        ];
    }

    /**
     * @param $category Category
     * @return mixed
     */
    public function getCategoryProducts($category)
    {
        $products = $category->getProductCollection();
        $products->addAttributeToSelect('*');
        $products->addFieldToFilter('status', Status::STATUS_ENABLED);

        $categorySortBy = $category->getDefaultSortBy();

        if ($categorySortBy) {
                $products->setOrder($categorySortBy, 'asc');
        }


        return $products;
    }

    /**
     * get First level categories
     *
     * @return array
     */
    public function getCategories()
    {

        if(!$this->categories)
        {
            /** @var \Magento\Store\Model\Store $store */
            $store = $this->storeManager->getStore();
            $rootId = $store->getRootCategoryId();
            $rootCategory = $this->categoryRepository->get($rootId, $this->_storeManager->getStore()->getId());

            $this->categories = $this->categoryFactory->create()->getCollection()->addAttributeToSelect('*')
                ->addAttributeToFilter('is_active', 1)
                ->setOrder('position', 'ASC')
                ->addIdFilter($rootCategory ->getChildren());
        }

        return $this->categories;
    }

    /**
     * Retrieve product image
     *
     * @param \Magento\Catalog\Model\Product $product
     * @param string $imageId
     * @param array $attributes
     * @return \Magento\Catalog\Block\Product\Image
     */
    public function getImage($product, $imageId, $attributes = [])
    {
        return $this->imageBuilder->setProduct($product)
            ->setImageId($imageId)
            ->setAttributes($attributes)
            ->create();
    }

    public function getFilters($product)
    {
        $subselection = explode(',',$product->getData('subselection') ?? '');
        $preparation = explode(',',$product->getData('preparation') ?? '');
        $preference_alimentaire = explode(',',$product->getData('preference_alimentaire') ?? '');

        $filters = array_merge($subselection, $preparation, $preference_alimentaire);

        $filters = array_filter($filters);
        $output = "";
        foreach($filters as $filter)
        {
            $output.= "filter_".$filter." ";
        }

        return $output;
    }

    /**
     * @param string $optionName
     * @return string
     */
    public function getValidFileName($optionName)
    {
        return $this->stringstuffsHelper->sanitizeFileName($optionName);
    }
}

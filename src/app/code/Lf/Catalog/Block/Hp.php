<?php

/**
 * @category  Lf
 * @package   Lf_Catalog
 * @copyright 2018 Adin
 */

namespace Lf\Catalog\Block;

use Magento\Framework\App\Http\Context as HttpContext;
use Magento\Framework\View\Element\Template\Context;

class Hp extends \Magento\Framework\View\Element\Template
{


    /**
     * @var HttpContext
     */
    protected $httpContext;


    /**
     * @param Context $context
     * @param Session $customerSession ,
     * @param array $data
     */
    public function __construct(
        Context $context,
        HttpContext $httpContext,
        array $data = []
    )
    {
        parent::__construct($context, $data);
        $this->httpContext = $httpContext;
    }

    public function getJsLayout()
    {
        $this->jsLayout['components']
        ['productListHp']
        ['config']
        ['updateUrl'] = $this->getUrl('cat/hp/refresh');


        return parent::getJsLayout();
    }


    /**
     * @return bool
     */
    public function isCustomerLoggedIn()
    {
        return $this->httpContext->getValue(\Magento\Customer\Model\Context::CONTEXT_AUTH) ? 1  : 0 ;
    }
}

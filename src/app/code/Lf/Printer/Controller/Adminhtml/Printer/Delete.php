<?php

namespace Lf\Printer\Controller\Adminhtml\Printer;

use Lf\Printer\Api\PrinterRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Framework\Exception\NoSuchEntityException;

class Delete extends Action
{
    /**
     * @var PrinterRepositoryInterface
     */
    private $printerRepository;

    public function __construct(
        Action\Context $context,
        PrinterRepositoryInterface $printerRepository
    )
    {
        parent::__construct($context);
        $this->printerRepository = $printerRepository;
    }


    public function execute()
    {
        $result =  $this->resultRedirectFactory->create()
            ->setPath('*/*/listing');

        $id = $this->getRequest()->getParam('id', false);

        if ($id === false) {
            return $result;
        }

        try {
            $this->printerRepository->delete($id);
        } catch (NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__("The printer does not exist"));
            return $result;
        }

        $this->messageManager->addSuccessMessage(__('The printer was successfully deleted'));
        return $result;
    }
}
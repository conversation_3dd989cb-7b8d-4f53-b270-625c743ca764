<?php

declare(strict_types=1);

namespace Lf\DialogInsight\Cron;

use Lf\DialogInsight\Model\Api\DialogInsightApiInterface;
use Lf\DialogInsight\Model\Api\Payload\RelationalDataMerge;
use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Config\ScopeConfigInterface;

class SyncFranchises
{
    public function __construct(
        private readonly DialogInsightApiInterface $api,
        private readonly FranchiseRepositoryInterface $franchiseRepository,
        private readonly SearchCriteriaBuilder $criteriaBuilder,
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    public function execute(): void
    {
        $criteria = $this->criteriaBuilder->create();

        $franchises = $this->franchiseRepository->getList($criteria);

        $franchiseTableId = $this->scopeConfig->getValue('dialoginsight/webapi/franchises');

        $message = new RelationalDataMerge();

        $message->setTableId($franchiseTableId);

        foreach ($franchises->getItems() as $franchise) {
            $message->addRecord('franchise_id', $franchise->getId(), [
                'franchise_id' => $franchise->getFranchiseId(),
                'code' => $franchise->getCode(),
                'name' => $franchise->getName(),
                'email' => $franchise->getEmail(),
                'active' => $franchise->getIsActive(),
                'siret' => $franchise->getSiret(),
                'phone' => $franchise->getPhone(),
                'address' => $franchise->getAddress(),
                'postcode' => $franchise->getPostcode(),
                'city' => $franchise->getCity(),
                'vat' => $franchise->getVat(),
                'capital' => $franchise->getCapital(),
                'iban' => $franchise->getIban(),
                'bank_code' => $franchise->getBankCode(),
                'ape' => $franchise->getApeCode(),
            ]);
        }

        $this->api->mergeRelationalTable($message);
    }
}

<div data-bind="visible: shippingBar.isTimeslotVisible, css: { 'creneau1': shippingBar.isToday(), 'creneau2': shippingBar.isTomorrow(), 'creneau3': !shippingBar.isToday() && !shippingBar.isTomorrow() }">
    <div class="tooltip tooltip2">
        <!-- ko ifnot: shippingBar.isToday -->
            <div class="moment" data-bind="html: shippingBar.timeslotTypeName"></div>
        <!--/ko-->
        <div class="part select" value="0" data_type="_hour">Choisissez votre heure de livraison<span></span></div>
        <!-- ko if: (shippingBar.isToday()==1 && shippingBar.isExpressShippingAvailable()==1) -->
            <div class="part select" data_type="_hour" value="express" data-bind="click: function(data, event) { selectTimeslot('express',data, event) }">Express 1h<span data-bind="html: formatExpressShippingAmount()"></span></div>
        <!--/ko-->
        <!-- ko foreach: shippingBar.timeslotsToShow -->
            <div class="part select" data-bind="attr: {value: timeslot_id}, click: $parent.isClosed(timeslot_id) ? null : function(data, event) { $parent.selectTimeslot(timeslot_id,data, event) }, css: {closed: $parent.isClosed(timeslot_id)}" data_type="_hour">
                <!-- ko text: $parent.formatHour(start_hour)--><!--/ko--> - <!-- ko text: $parent.formatHour(end_hour)--><!--/ko-->
                <!-- ko if: $parent.isClosed(timeslot_id) -->
                <span class="closed">COMPLET</span>
                <!--/ko-->
                <!-- ko ifnot: $parent.isClosed(timeslot_id) -->
                    <span data-bind="html: $parent.formatDeliveryShippingAmount($data)"></span>
                <!--/ko-->
            </div>
        <!--/ko-->
        <!-- ko if: (shippingBar.isPickupAvailable()==1) -->
        <div class="part select" data_type="_hour" value="pickup" data-bind="click: function(data, event) { selectTimeslot('pickup',data, event) }">Retrait en atelier<span data-bind="html: formatPickupShippingAmount()"></span></div>
        <!--/ko-->
    </div>
</div>

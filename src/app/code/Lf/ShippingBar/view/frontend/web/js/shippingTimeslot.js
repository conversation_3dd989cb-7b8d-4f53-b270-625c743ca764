define([
    'ko',
    'jquery',
    'moment',
    'uiComponent',
    'Magento_Customer/js/customer-data',
    './model/shippingBar'
], function (ko, $, moment, Component, customerData, shippingBar) {
    'use strict';

    var shippingbarData = customerData.get('shippingbar');

    return Component.extend({
        defaults: {
            updateUrl: '/shippingbar/retrieve/timeslot',
            resetUrl: '/shippingbar/update/shippingreset',
        },

        initObservable: function () {
            this._super();

            this.shippingBar = shippingBar;
            this.warningVisible = ko.observable(false);
            this.checkWarning = this.checkWarning.bind(this);

            ko.computed(function () {
                let hasTodayType = false;

                $.each(shippingBar.timeslotTypes(), function (e, value) {
                        if (value.is_today == 1) {
                            hasTodayType = true;
                        }
                    }.bind(this));

                if(hasTodayType) {
                    this.checkTodayVisibility(shippingBar.timeslots);
                    this.checkTomorrowVisibility(shippingBar.timeslots);
                } else {
                    shippingBar.isTodayVisible(false);
                }
            }.bind(this));

            if (shippingBar.zone() !== null && shippingBar.zone() !== undefined) {
                if (shippingBar.timeslot() === null || shippingBar.timeslot() === undefined) {
                    $('.no-delivery').addClass('loading');
                }
            }
            $(window).on('scroll',this.checkWarning);
            ko.computed(this.checkWarning);

            return this;
        },

        checkWarning: function () {
            if (shippingBar.address() != null && shippingBar.shippingdate() == null && $('.delivery').hasClass('fixednoanim')) {
                this.warningVisible(true);
            } else {
                this.warningVisible(false);
            }
        },

        checkTomorrowVisibility: function(timeslots) {
            var today = new Date();
            var tomorrow = new Date();
            tomorrow.setDate(today.getDate()+1);
            var tommorrowDate = moment(tomorrow).format('YYYYMMDD');

            /**
             * Check if tomorrow is not a closing day
             */
            if(shippingbarData()['shipping_closed_days'] !== null && Array.isArray(shippingbarData()['shipping_closed_days']))
            {
                var found = shippingbarData()['shipping_closed_days'].find(function(element) {
                    return element == tommorrowDate;
                });

                if(found!=undefined)
                {
                    shippingBar.isTomorrowVisible(false);
                    return;
                }
                shippingBar.isTomorrowVisible(true);
            }

            //Hide tomorrow, show if one found
            shippingBar.isTomorrowVisible(false);
            //check if timeslot are still available, even with long preparation time
            var now = moment().format('YYYYMMDDHHmm');
            var tomorrow = moment().format('YYYYMMDD');
            tomorrow = moment(tomorrow, 'YYYYMMDD').add(1, 'days');
            tomorrow = tomorrow.format('YYYYMMDD');
            $.each(timeslots(), function (e, value) {
                var timeSlotEndDateTime = shippingBar.getMaxDateTimeToOrderForThisTimeSlot(value, tomorrow);
                if(timeSlotEndDateTime > now) {
                    shippingBar.isTomorrowVisible(true);
                    return false;
                }

            }.bind(this));
        },

        checkTodayVisibility: function(timeslots){
            var today = moment(new Date());
            var currentDate = moment(today).format('YYYYMMDD');

            /**
             * Check if today is not a closing day
             */

            if (shippingbarData()['shipping_closed_days'] !== null && shippingbarData()['shipping_closed_days'] !== undefined) {
                var found = shippingbarData()['shipping_closed_days'].find(function (element) {
                    return element == currentDate;
                });

                if(found!=undefined)
                {
                    shippingBar.isTodayVisible(false);
                    return;
                }
            }

            //Hide today, show if one found
            shippingBar.isTodayVisible(false);
            var now = moment().format('YYYYMMDDHHmm');
            var today = moment().format('YYYYMMDD');

            $.each(timeslots(), function (e, value) {
                var timeSlotEndDateTime = shippingBar.getMaxDateTimeToOrderForThisTimeSlot(value, today);
                if(timeSlotEndDateTime > now) {
                    shippingBar.isTodayVisible(true);
                    return false;
                }
            }.bind(this));
        },

        showTimeslot: function(){
            if(!shippingBar.visiblityState() || shippingBar.visiblityState()!="today")
            {
                shippingBar.isToday(true);
                shippingBar.isTomorrow(false);
                shippingBar.visiblityState('today');
                shippingBar.selectedDateFormated(moment().format('YYYYMMDD'));
                shippingBar.isTimeslotVisible(true);
                shippingBar.isTimeslotTypeVisible(false);
                shippingBar.isDatepickerVisible(false);
                shippingBar.refreshTimeslotDatas();
            } else {
                this.hidePopin();
            }
            return true;
        },

        hidePopin:  function(){
            shippingBar.isTimeslotVisible(false);
            shippingBar.isTimeslotTypeVisible(false);
            shippingBar.isDatepickerVisible(false);
            shippingBar.visiblityState(false);
            $(".timeslotSelected").remove();
        },

        removeLoader: function (element) {
             $(element).parent().find('.loader').addClass("hidden");
             $(element).parent().show();
        },

        showTimeslotType: function(){
            if(!shippingBar.visiblityState() || shippingBar.visiblityState()!="tomorrow") {
                var tomorrow = moment().format('YYYYMMDD');
                tomorrow = moment(tomorrow, 'YYYYMMDD').add(1, 'days');
                tomorrow = tomorrow.format('YYYYMMDD');
                shippingBar.selectedDateFormated(tomorrow);
                shippingBar.isTomorrow(true);
                shippingBar.isToday(false);
                shippingBar.refreshTimeslotDatas();
                shippingBar.isTimeslotTypeVisible(true);
                shippingBar.isTimeslotVisible(false);
                shippingBar.isDatepickerVisible(false);
                shippingBar.visiblityState('tomorrow');
            } else {
                this.hidePopin();
            }
            return true;
        },

        showDatepicker: function(){
            if (!shippingBar.visiblityState() || shippingBar.visiblityState()!="later") {
                shippingBar.visiblityState('later');
                shippingBar.isTimeslotTypeVisible(false);
                shippingBar.isTimeslotVisible(false);
                shippingBar.isToday(false);
                shippingBar.isTomorrow(false);
                shippingBar.isDatepickerVisible(true);
            }
            else {
                this.hidePopin();
            }
            return true;
        },

        isTsSelected: function () {
            if(($('#delivery1')[0].checked || $('#delivery2')[0].checked || $('#delivery3')[0].checked )
                && $("#adresse-input").val != "") {

                return true;
            }
        },

        isFixedNoanim: function () {
            if($('.delivery').hasClass("fixednoanim") && $("#adresse-input").val != "")  {

                return true;
            }
        },

        backToStep1: function(){
            $.post(this.resetUrl, {}).done(
                function (response) {
                    if (response.success) {
                        window.location = "/";
                    }
                    $(".timeslotSelected").remove();
                }.bind(this)
            );
        }
    });
});

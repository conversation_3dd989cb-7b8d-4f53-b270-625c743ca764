<?php
declare(strict_types=1);

namespace Lf\CatalogInventory\Plugin;

use Lf\Franchise\Api\Data\FranchiseProductExtensionFactory;
use Lf\Franchise\Api\Data\FranchiseProductInterface;
use Lf\Franchise\Api\FranchiseProductRepositoryInterface;
use Lf\Franchise\Model\FranchiseProduct;
use Magento\Framework\Api\SearchResults;
use Magento\Framework\Data\SearchResultInterface;

class FranchiseProductRepositorySaveStockMax
{
    /**
     * @var FranchiseProductExtensionFactory
     */
    private $extensionFactory;

    /**
     * FranchiseProductRepositorySaveStockMax constructor.
     * @param FranchiseProductExtensionFactory $extensionFactory
     */
    public function __construct(
        FranchiseProductExtensionFactory $extensionFactory
    )
    {
        $this->extensionFactory = $extensionFactory;
    }

    /**
     * Saves the stock max to the franchise_product table
     *
     * @param FranchiseProductRepositoryInterface $franchiseProductRepository
     * @param FranchiseProductInterface $franchiseProduct
     * @return null
     */
    public function beforeSave(FranchiseProductRepositoryInterface $franchiseProductRepository, FranchiseProductInterface $franchiseProduct)
    {
        $franchiseProductExtension = $franchiseProduct->getExtensionAttributes();

        if ($franchiseProductExtension === null) {
            return null;
        }

        if(!empty($franchiseProductExtension->getStockMax()) || $franchiseProductExtension->getStockMax()=== '0')
        {
            $franchiseProduct->setData('stock_max', $franchiseProductExtension->getStockMax());
        }
        else
        {
            $franchiseProduct->setData('stock_max', null);
        }

        return null;
    }

    /**
     * Copie stock_max to extension attributes
     *
     * @param FranchiseProductRepositoryInterface $franchiseProductRepository
     * @param FranchiseProductInterface $franchiseProduct
     * @return FranchiseProductInterface
     */
    public function afterGetByFranchiseProduct(FranchiseProductRepositoryInterface $franchiseProductRepository, FranchiseProductInterface $franchiseProduct)
    {
        $franchiseProductExtension = $franchiseProduct->getExtensionAttributes();


        if($franchiseProductExtension === null)
        {
            $franchiseProductExtension = $this->extensionFactory->create();
        }

        $franchiseProductExtension
            ->setStockMax(
                $franchiseProduct->getData('stock_max')
            );
        $franchiseProduct->setExtensionAttributes($franchiseProductExtension);

        return $franchiseProduct;
    }

    public function afterGetByFranchiseProductId(FranchiseProductRepositoryInterface $franchiseProductRepository, FranchiseProductInterface $franchiseProduct)
    {
        return $this->afterGetByFranchiseProduct($franchiseProductRepository, $franchiseProduct);
    }



    /**
     * Copie stock_max to extension attributes
     *
     * @param FranchiseProductRepositoryInterface $franchiseProductRepository
     * @param SearchResultInterface $result
     * @return SearchResultInterface
     */
    public function afterGetList(FranchiseProductRepositoryInterface $franchiseProductRepository, SearchResults $result)
    {
        /** @var FranchiseProduct $franchiseProduct */
        foreach ($result->getItems() as $franchiseProduct)
        {
            if($franchiseProduct->getExtensionAttributes() === null)
            {
                $franchiseProduct->setExtensionAttributes($this->extensionFactory->create());
            }

            $franchiseProduct
                ->getExtensionAttributes()
                ->setStockMax(
                    $franchiseProduct->getData('stock_max')
                );
        }

        return $result;
    }


}
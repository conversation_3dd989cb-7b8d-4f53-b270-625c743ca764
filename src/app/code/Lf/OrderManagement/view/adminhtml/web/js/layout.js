define([
    'uiComponent',
    'jquery',
    './model/navigation',
    './observer/cartUpdater',
    './observer/fraisLivraisonUpdater'
], function (Component, $, navigation) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Lf_OrderManagement/layout',
        },

        blockClass: function (index) {
            return navigation.selectedBlock() === index ? 'card card-default active' : 'card card-default';
        },

        isVisible: function () {
            return navigation.cartAndPaymentVisible();
        },
    });
});
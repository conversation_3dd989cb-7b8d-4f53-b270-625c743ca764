define([
    'uiComponent',
    'jquery',
    './model/model',
    'ko',
    'moment',
    'jquery-ui-modules/datepicker',
    'mage/calendar'
], function (Component, $, model, ko, moment) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Lf_OrderManagement/shipping-form',
            displayArea: 'shipping-form',
            model: model,
            inputId: '#datetimepicker',
            selectId: '#creneau'
        },
        initialize: function () {
          this._super();
            this.selectedValue = ko.observable();
            this.onTimeslotChange = this.onTimeslotChange.bind(this);
            this.timeslotAfterRender = this.timeslotAfterRender.bind(this);

          return this;
        },

        initObservable: function () {
            this._super().observe({
                timeslot: ''
            });

            this.timeslotsInDb =  ko.observableArray();

            ko.utils.arrayForEach(window.timeslots, function(timeslot) {
                timeslot.zoneId = ko.observable(timeslot.zoneId);
                this.timeslotsInDb.push(timeslot);
            }.bind(this));

            this.timeslotsInDb.push(
                {
                    id: 'express_express',
                    zoneId: model.zoneId,
                    value: 'Livraison express'
                },
                {
                    id: 'pickup_pickup',
                    zoneId: model.zoneId,
                    value: 'Retrait en magasin'
                });

            this.filteredTimeslots = ko.computed(
                function () {
                    var values = ko.utils.arrayFilter(this.timeslotsInDb(),
                        function (timeslot) {

                            /* Si on est hors zone mais autorisé à le faire */
                            if(model.zoneId()==null && window.is_out_of_area_enabled)
                            {
                                return true;
                            }
                            return timeslot.zoneId() == model.zoneId();
                        }
                    )
                    if(model.zoneId()==null && window.is_out_of_area_enabled)
                    {
                        var resArr = [];
                        values.forEach(function(item){
                            var i = resArr.findIndex(x => x.value == item.value);
                            if(i <= -1){
                                resArr.push(item);
                            }
                        });

                        return resArr;
                    }

                    return values;

                },this
        )
            ;
            // Synchronisation de l'input avec les changements de date du modèle
            ko.computed(function () {
                if (model.shippingDate() == null) {
                    $(this.inputId).val('');
                } else {
                    $(this.inputId).datepicker("setDate", model.shippingDate());
                }
            }, this);

            // Synchronisation du select de timeslot avec les changements de modèle
            ko.computed(function () {
                if (!model.timeslot()) {
                    $(this.selectId).val('');
                } else {
                    const selectedOption = $(this.selectId).find('option[value="' + model.timeslot() + '"]');


                    if (selectedOption.length !== 0) {
                        $(this.selectId).val(model.timeslot());
                    } else {
                        this.selectedValue(model.timeslot());
                        $(this.selectId).val('');
                        model.timeslot(null);
                    }
                }
            }, this);

            return this;
        },

        /**
         * Copie du nouveau timeslot dans le modèle et déclenchement d'un évènement.
         *
         * @param source
         * @param event
         */
        onTimeslotChange: function (source, event) {
            const newValue = $(event.target).val();
            const oldValue = model.timeslot();

            if (newValue === oldValue) {
                return;
            }

            model.timeslot(newValue);

            $(document).trigger('lf.timeslotChanged', {
                oldValue: oldValue,
                newValue: newValue
            });
        },

        /**
         * Copie de la nouvelle date si l'évènement shippingDateChangeBefore le permet.
         * Déclenche l'évènement shippingDateChanged en cas de succès.
         *
         * @param newDate
         */
        onDateChange: function (newDate) {
            if ($(document).triggerHandler('lf.shippingDateChangeBefore', newDate)) {
                model.shippingDate(newDate);
                $(document).trigger('lf.shippingDateChanged');
            } else {
                this.$datePicker.datepicker("setDate", model.shippingDate());
            }
        },

        /**
         * AfterRender qui navigue vers le bloc panier au tab sur le dernier input.
         * @param element
         */
        setupTabNavigation: function (element) {
          $(element).on('keydown', function (e) {
            if (e.which === 9) { //Tab
                $('#produits').trigger('focus');
                return false;
            }
          });
        },

        /**
         * Block ui elements while data refresh is in progress
         * @param jqXhr
         */
        handlePendingRefresh: function (jqXhr) {
            $(this.inputId).prop('disabled', true);
            $(this.selectId).prop('disabled', true);

            jqXhr.always(function () {
                $(this.inputId).prop('disabled', false);
                $(this.selectId).prop('disabled', false);
            }.bind(this))
        },

        /**
         * AfterRender qui initialise le widget de datepicker.
         *
         * @param element
         *  L'élément de DOM cible
         */
        setupDatePicker: function (element) {
            const today = new Date(),
                $element = $(element),
                self = this;

            this.$datePicker = $element;

            // Si l'utilisateur tape quelque chose dans l'input de date on supprime la valeur
            $element.on('change', function () {
                model.shippingDate(null);
            });

            $element.calendar({
                changeYear: false,
                changeMonth: false,
                showButtonPanel: false,
                showOn: 'focus',
                defaultDate: today,
                minDate: today,
                dateFormat: 'DD d MMMM yy',
                beforeShow: function (input, inst) {
                    $('#ui-datepicker-div').addClass("ll-skin-latoja");
                },
                onSelect: function (dateText, inst) {
                    var newDate = $element.datepicker('getDate');
                    self.onDateChange(newDate);
                },
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-crosshairs',
                    clear: 'fa fa-trash'
                }
            });

            // Navigation au clavier
            $element.on('keyup', function(event) {
                const date = $element.datepicker('getDate');

                switch(event.which) {
                    case 37: //left arrow
                        self.onDateChange(
                            moment(date).subtract(1, 'days').toDate()
                        );
                        break;
                    case 38: //up arrow
                        self.onDateChange(
                            moment(date).subtract(7, 'days').toDate()
                        );
                        break;
                    case 39: //right arrow
                        self.onDateChange(
                            moment(date).add(1, 'days').toDate()
                        );
                        break;
                    case 40: //down arrow
                        self.onDateChange(
                            moment(date).add(7, 'days').toDate()
                        );
                        break;
                    case 13: //enter
                        $element.datepicker('hide');
                        break;
                }
            });
        },

        timeslotAfterRender: function () {
            if (this.selectedValue != null && this.selectedValue != undefined) {
                const selectedOption = $("#creneau").find('option[value="' + this.selectedValue() + '"]');


                if (selectedOption.length !== 0) {
                    $("#creneau").val(this.selectedValue());
                }
                return this.selectedValue();
            }
        }
    });
});

define([
    'uiComponent',
    'jquery',
    './model/model'
], function (Component, $, model) {
    'use strict';

    const customer = model.customer;

    return Component.extend({
        defaults: {
            template: 'Lf_OrderManagement/customer-summary',
            displayArea: 'customer-summary',
            customer: customer
        },

        formatAmount: function(value) {
            return Math.round(value) + ' €'
        },

        showOrders: function () {
            window.open(window.ordersUrl + 'main_table.customer_id/' + model.customer.id() + '/reset_filter', '_blank');
        },
    });
});

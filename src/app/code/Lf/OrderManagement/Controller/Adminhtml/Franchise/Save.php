<?php

namespace Lf\OrderManagement\Controller\Adminhtml\Franchise;

use Lf\Franchise\Api\FranchiseRepositoryInterface;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Psr\Log\LoggerInterface;

class Save extends Action
{
    private $resultJsonFactory;

    private $franchiseRepository;

    private $logger;

    /**
     * Constructor
     *
     * @param Context $context
     * @param JsonFactory $resultJsonFactory
     * @param FranchiseRepositoryInterface $franchiseRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        FranchiseRepositoryInterface $franchiseRepository,
        LoggerInterface $logger
    )
    {
        parent::__construct($context);

        $this->resultJsonFactory = $resultJsonFactory;
        $this->franchiseRepository = $franchiseRepository;
        $this->logger = $logger;
    }

    public function execute()
    {
        if (!$this->getRequest()->isAjax()) {
            $this->_forward('noroute');
            return $this;
        }

        $data = $this->getRequest()->getParams();

        try {
            $franchise = $this->franchiseRepository->getFranchiseActif();

            if (array_key_exists('impulsion', $data)) {
                $franchise->setImpulsion($data['impulsion']);
            }

            if (array_key_exists('ruptures', $data)) {
                $franchise->setRuptures($data['ruptures']);
            }

            $this->franchiseRepository->save($franchise);
        } catch (\Exception $e) {
            $this->logger->error($e);

            return $this->resultJsonFactory->create()->setData([
                    'error' => $e->getMessage()
                ]
            );
        }

        return $this->resultJsonFactory->create()->setData(['success' => true]);
    }
}
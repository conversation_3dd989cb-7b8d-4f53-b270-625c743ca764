<?php

namespace Lf\OrderManagement\Controller\Adminhtml\Address;

use Lf\OrderManagement\Model\Service\AddressFinder;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Psr\Log\LoggerInterface;

class Search extends Action
{
    private JsonFactory $resultJsonFactory;

    private AddressFinder $addressFinder;

    private LoggerInterface $logger;

    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        AddressFinder $addressFinder,
        LoggerInterface $logger
    ) {
        parent::__construct($context);

        $this->resultJsonFactory = $resultJsonFactory;
        $this->addressFinder = $addressFinder;
        $this->logger = $logger;
    }

    /**
     * Retourne en JSON les adresses pour lesquelles le nom de l'entreprise correspond au paramètre searchQuery.
     */
    public function execute()
    {
        if (!$this->getRequest()->isAjax()) {
            $this->_forward('noroute');
            return $this;
        }

        $searchQuery = $this->getRequest()->getParam('searchQuery');

        try {
            return $this->resultJsonFactory->create()->setData(
                [
                    'addresses' => $this->addressFinder->find(
                        $searchQuery
                    ),
                ]
            );
        } catch (\Exception $e) {
            $this->logger->error($e);

            return $this->resultJsonFactory->create()->setData(
                [
                    'error' => $e->getMessage(),
                ]
            );
        }
    }
}

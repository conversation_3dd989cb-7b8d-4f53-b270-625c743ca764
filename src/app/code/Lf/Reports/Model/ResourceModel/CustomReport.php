<?php

namespace Lf\Reports\Model\ResourceModel;

use Magento\Backend\Model\Auth\Session;
use Magento\Framework\App\State;
use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Framework\Model\ResourceModel\Db\Context;

class CustomReport extends \DEG\CustomReports\Model\ResourceModel\CustomReport
{
    /**
     * @var Session
     */
    private $authSession;
    /**
     * @var State
     */
    private $state;

    public function __construct(
        Session $authSession,
        Context $context,
        State $state,
        $connectionName = null
    ) {
        parent::__construct($context, $connectionName);
        $this->authSession = $authSession;
        $this->state = $state;
    }

    protected function _construct()
    {
        $this->_init('deg_customreports', 'customreport_id');
    }

    protected function _getLoadSelect(
        $field,
        $value,
        $object
    ) {
        $parentGetLoadSelect = parent::_getLoadSelect($field, $value, $object);
        if ($this->state->getAreaCode() == \Magento\Framework\App\Area::AREA_ADMINHTML) {
            $parentGetLoadSelect->joinLeft(
                ['user_report' => $this->getTable('deg_customreports_user_date')],
                'deg_customreports.customreport_id = user_report.customreport_id AND user_report.user_id = ' . $this->authSession->getUser()->getId(),
                ['DateDeb', 'DateFin']

            );
        }

        return $parentGetLoadSelect;
    }

}

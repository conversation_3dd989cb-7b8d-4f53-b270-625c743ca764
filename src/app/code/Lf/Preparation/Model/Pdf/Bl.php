<?php

namespace Lf\Preparation\Model\Pdf;

use FPDF;
use Lf\Franchise\Model\Franchise;
use Lf\Franchise\Model\FranchiseFactory;
use Lf\Franchise\Model\Timeslot;
use Lf\Franchise\Model\TimeslotFactory;
use Lf\Franchise\Model\Zone;
use Lf\Franchise\Model\ZoneFactory;
use Lf\Opc\Model\ShippingModelFactory;
use Lf\Payment\Model\Differe;
use Lf\Payment\Model\Livraison;
use Lf\Preparation\Model\MagicSort;
use Lf\Printer\Api\PrinterRepositoryInterface;
use Lf\ShippingBar\Helper\Date;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Quote\Model\QuoteFactory;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\OrderFactory;
use Magento\Sales\Model\ResourceModel\Order\Collection;
use Magento\Sales\Model\ResourceModel\Order\Tax\Item;
use Magento\Store\Model\StoreRepository;

/**
 * Bon de livraison PDF model
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class Bl
{

    /**
     *  Bl folder name
     */
    const BL_FOLDER = 'bl';

    /**
     *  Prefix used for Bl filename
     */
    const Bl_FILE_PREFIX = 'Bl';

    /**
     * @var ZoneFactory
     */
    private $zoneFactory;
    /**
     * @var FranchiseFactory
     */
    private $franchiseFactory;
    /**
     * @var QuoteFactory
     */
    private $quoteFactory;
    /**
     * @var OrderFactory
     */
    private $orderFactory;
    /**
     * @var TimeslotFactory
     */
    private $timeslotFactory;
    /**
     * @var Date
     */
    private $dateHelper;
    /**
     * @var ShippingModelFactory
     */
    private $shippingModelFactory;

    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * @var TimezoneInterface
     */
    private $date;
    /**
     * @var PrinterRepositoryInterface
     */
    private $printerRepository;
    /**
     * @var StoreRepository
     */
    private $storeRepository;
    /**
     * @var MagicSort
     */
    private $magicSort;
    /**
     * @var \Magento\Sales\Model\ResourceModel\Order\Tax\Item
     */
    private Item $taxItem;

    public function __construct(
        ZoneFactory                $zoneFactory,
        FranchiseFactory           $franchiseFactory,
        TimeslotFactory            $timeslotFactory,
        TimezoneInterface          $date,
        Date                       $dateHelper,
        OrderFactory               $orderFactory,
        ShippingModelFactory       $shippingModelFactory,
        Filesystem                 $filesystem,
        QuoteFactory               $quoteFactory,
        Item                       $taxItem,
        StoreRepository            $storeRepository,
        PrinterRepositoryInterface $printerRepository,
        MagicSort                  $magicSort
    ) {
        $this->dateHelper = $dateHelper;
        $this->zoneFactory = $zoneFactory;
        $this->franchiseFactory = $franchiseFactory;
        $this->timeslotFactory = $timeslotFactory;
        $this->quoteFactory = $quoteFactory;
        $this->orderFactory = $orderFactory;
        $this->filesystem = $filesystem;
        $this->shippingModelFactory = $shippingModelFactory;
        $this->date = $date;
        $this->taxItem = $taxItem;
        $this->printerRepository = $printerRepository;
        $this->storeRepository = $storeRepository;
        $this->magicSort = $magicSort;
    }

    /**
     * Return PDF document
     *
     * @param array|Collection $orders
     * @return \Zend_Pdf
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function generatePdf($printId, $orderIds = [])
    {
        $today = $this->date->date()->format('d/m/Y');

        $printer = $this->printerRepository->getById($printId);
        $printerHostname = $printer->getHostname();

        /** @var Order $order */
        foreach ($orderIds as $orderId) {
            $order = $this->orderFactory->create();
            $order->load($orderId);

            $customer = $order->getCustomer();

            $orderShippingAddress = $order->getShippingAddress();
            $quote = $this->quoteFactory->create();
            $quote->load($order->getQuoteId());

            $shippingModel = $this->shippingModelFactory->create();
            $shippingModel->load($order->getQuoteId(),'quote_id');

            $hasSplit = false;
            foreach ($quote->getAllItems() as $item) {
                if ($item->getSplit() && $item->getSplit() != "Split par défaut") {
                    $hasSplit = true;
                }
            }

            $comment = $shippingModel->getComment();

            /** @var Zone $zone */
            $zone = $this->zoneFactory->create();
            $zone->load($shippingModel->getZone(), 'code');

            /** @var Franchise $franchise */
            $franchise = $this->franchiseFactory->create();
            $franchise->load($shippingModel->getFranchiseId());

            /** @var Timeslot $timeslot */
            $date = $today;
            if (is_numeric($shippingModel->getTimeslotId())) {
                $timeslot = $this->timeslotFactory->create();
                $timeslot->load($shippingModel->getTimeslotId());
                $b3 = $this->dateHelper->formatTimeslot($timeslot->getStartHour()) . " - " . $this->dateHelper->formatTimeslot($timeslot->getEndHour()) . "\n" . $date;
            }
            else{
                $b3 = $shippingModel->getTimeslotId() . "\n" . $date;
            }

            $pageLargeur1 = 205;
            $pageLargeur = 204;

            $number = (int)$order->getColisId();
            $b1 = "N° " . $number;
            $b1l = 80;
            $block1H = 80;
            $numZone = $zone->getCode();
            $b2 = $numZone;
            $b2h = 15;

            $blockSarlH = 40;
            $Yimage = $block1H +2;
            $Ysarl = $Yimage + $b2h +2 ;
            $sarl = $franchise->getName()." RCS ".$franchise->getSiret()."\n".$franchise->getAddress()."\n".$franchise->getPostcode()." ".$franchise->getCity()."\n".$franchise->getPhone()." N° TVA ".$franchise->getVat();

            $YSP = $Ysarl + 25;
            if ($comment) {
                $SP = str_split("SP livraison: " . $comment, 31);
                $hauteurSp = count($SP) * 20;
                $blockSarlH += $hauteurSp;
            }
            elseif (isset($SP)) {
                unset($SP, $hauteurSp);
            }

            $Yclient = $Yimage + $blockSarlH -2 ;

            $clientNom = str_split($orderShippingAddress->getCompany(),25);
            $societe = $orderShippingAddress->getCompany();

            $clientAdd = str_split($orderShippingAddress->getStreetLine(1). ' '. $orderShippingAddress->getPostcode(). ' '.$orderShippingAddress->getCity(), 50);

            $clientH = 48 + 15*(count($clientNom)-1) + 10*(count($clientAdd)-1);

            $contact = $orderShippingAddress->getLastname(). ' '.$orderShippingAddress->getFirstname();
            $clientContact = $contact . " - tel : ".$orderShippingAddress->getTelephone();

            $detail=[];
            $nbProduits= 0;
            foreach ($this->magicSort->execute($order->getItems()) as $item) {
                $nbProduits += $item->getQtyOrdered();

                $detail[] = [
                    number_format($item->getQtyOrdered(), 0) . ' ' . substr($item->getData('libelle_bon_livraison'), 0, 20),
                    $this->TrimTrailingZeroes($item->getRowTotalInclTax()),
                    $item->getData('sort')
                ];
            }
            usort($detail, array($this, "cmp"));

            $Ycmd = $Yclient + $clientH;
            $totalSP=($order->getShippingTaxAmount()+$order->getShippingAmount());
            $discount=$order->getDiscountAmount();
            $livraison_height=0;
            if ($totalSP > 0) {
                $livraison_height = 15;
            }
            $discount_height=0;
            if ($discount > 0) {
                $discount_height = 15;
            }
            if ($franchise->getPrintPriceBl()) {
                $cmdH = 90 + $livraison_height + $discount_height + count($detail) * 10;
            }
            else {
                $cmdH = 50 + count($detail) * 10;
            }

            $cmdNum = "N° ".$order->getIncrementId();
            $totalHT = $this->TrimTrailingZeroes($order->getSubTotal()) + $this->TrimTrailingZeroes($order->getShippingAmount())+$discount+$this->TrimTrailingZeroes($order->getDiscountTaxCompensationAmount());
            $taxItems = $this->taxItem->getTaxItemsByOrderId($order->getId());

            $TVA20 = "0";
            $TVA10 = "0";
            $TVA5 = "0";

            $taxes = [];
            foreach ($taxItems as $taxItem) {
                if (!isset($taxes[$taxItem['code']])) {
                    $taxes[$taxItem['code']] = $taxItem['real_amount'];
                }
                else{
                    $taxes[$taxItem['code']] += $taxItem['real_amount'];
                }
            }
            foreach ($taxes as $taxCode => $taxAmount) {
                if ($taxCode == "TVA 10") {
                    $TVA10 = $this->TrimTrailingZeroes($taxAmount);
                }
                elseif ($taxCode == "TVA 55") {
                    $TVA5 = $this->TrimTrailingZeroes($taxAmount);
                }
                elseif ($taxCode="TVA 20"){
                    $TVA20= $this->TrimTrailingZeroes($taxAmount);
                }
            }
            $TTC = $this->TrimTrailingZeroes($order->getGrandTotal());
            $merci = $franchise->getTicketMessage();

            $Ypayment = $Ycmd + $cmdH;
            $paymentH = 50;
            $modeDePaiement = "MODE DE PAIEMENT";
            $paymentType1 = "CB VISA";
            $paymentType2 = "DIFFÉRÉ";
            $paymentType3 = "A LA LIVRAISON";
            $paymentMethod = $order->getPayment()->getMethod();

            $pageHauteur = $Ypayment + $paymentH + 5;

            if (isset($SP)) {
                $pageHauteur;
            }
//signature
            $hauteurPageSignature = 130;
            $signature = "Signature & tampon société";

            if($franchise->getPrintPriceBl())
            {
                $signatureText = mb_convert_encoding($societe ?? '', 'ISO-8859-1', 'UTF-8') . " " . mb_convert_encoding($contact ?? '',
                        'ISO-8859-1', 'UTF-8') . "\n" . $TTC . " " . chr(128) . " ttc\n" . $date;
            }
            else
            {
                $signatureText = mb_convert_encoding($societe ?? '', 'ISO-8859-1', 'UTF-8') . " " . mb_convert_encoding($contact ?? '',
                        'ISO-8859-1', 'UTF-8') . "\n" .  $date;

            }

//lots

            $lots=[];

            if (!$hasSplit) {
                foreach ($order->getItems() as $item) {
                    $values = explode(';', $item->getCartonAssignement() ?? '');
                    foreach ($values as $value) {
                        $datas = explode('-', $value);
                        if (is_array($datas) && count($datas) == 2) {
                            if (!isset($lots[$datas[0]])) {
                                $lots[$datas[0]] = [
                                    'element' => $datas[0] . '/' . $order->getNbCarton(),
                                    "zone" => $numZone,
                                    "number" => $number,
                                ];
                            }
                            $lots[$datas[0]]['items'][] =[
                                "qty" => (int)$datas[1],
                                "name" => $item->getName(),
                                "option" => ""

                            ];
                        }
                    }
                }

                ksort($lots);
            }
            else{
                foreach ($order->getItems() as $item) {
                    $quoteItem = $quote->getItemById($item->getQuoteItemId());
                    $splitName = $quoteItem->getSplit();

                    if (!isset($lots[$splitName])) {
                        $lots[$splitName] = [
                            "zone" => $numZone,
                            "number" => $number,
                            "name" => $splitName
                        ];
                    }
                    $lots[$splitName]['items'][] =[
                        "qty" => (int)$item->getQtyOrdered(),
                        "price" => $item->getRowTotalInclTax(),
                        "name" => $item->getName(),
                        "option" => ""

                    ];
                }

                $cptClientNom=1;
                foreach ($lots as $key => $lot) {
                    $price = 0;
                    foreach ($lot['items'] as $item) {
                        $price += $item['price'];
                    }
                    $lots[$key]['price'] = $price;
                    $lots[$key]['element'] = $cptClientNom++.'/'.count($lots);
                }
            }

            $pdf = new FPDF('P', 'mm', array($pageLargeur1, $pageHauteur));
            $pdf->SetAutoPageBreak(false, 0);

            $pdf->AddPage();
            $pdf->setMargins(0.1, 0.1, 0.1);
            $pdf->SetFont('Helvetica', 'B', 16);

//N°
            $pdf->setXY(0, 0);
            $pdf->SetFont('Helvetica', 'B', 60);
            $pdf->Cell($b1l, $block1H, mb_convert_encoding($b1 ?? '', 'ISO-8859-1', 'UTF-8'), 1, 0, 'C');

            /**
             * Zone
             */
            $pdf->SetFont('Helvetica', 'B', 45);
            $pdf->setXY($b1l, 0);
            $pdf->Cell($pageLargeur - $b1l - 1, $b2h, mb_convert_encoding($b2 ?? '', 'ISO-8859-1', 'UTF-8'), 1, 0, 'C');


            /**
             * Timeslot
             */
            $pdf->SetFont('Helvetica', 'B', 45);
            $pdf->Rect($b1l, $b2h, $pageLargeur - $b1l - 1, $block1H - $b2h, 'D');
            $pdf->setXY($b1l, $b2h + 5);
            $pdf->MultiCell($pageLargeur - $b1l - 1, 24, mb_convert_encoding($b3 ?? '', 'ISO-8859-1', 'UTF-8'), 0, 'C', 0);

            /**
             * Franchise
             */

            $pdf->Rect(0, $block1H, $pageLargeur - 1, $blockSarlH);
//logo
            $marge = 70;
            $pdf->Image($this->filesystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath() . self::BL_FOLDER . DIRECTORY_SEPARATOR . 'lflogo.jpg', 0 + $marge, $Yimage, $pageLargeur - 2 * $marge, $b2h);

//SARL
            $pdf->SetFont('Helvetica', '', 15);
            $pdf->setXY(0, $Ysarl);
            $pdf->MultiCell($pageLargeur - 1, 5, mb_convert_encoding($sarl ?? '', 'ISO-8859-1', 'UTF-8'), 0, 'C', 0);

//SP
            if (isset($SP)) {
                $pdf->SetFont('Helvetica', 'B', 38);
                $pdf->SetFillColor(240, 240, 240);
                $pdf->Rect(1, $YSP + 3, $pageLargeur - 3, $hauteurSp - 7, 'F');
                $cptLineSp = 0;

                foreach($SP as $chunk)
                {
                    $pdf->setXY(1, $YSP+$cptLineSp*20);

                    $pdf->Cell($pageLargeur - 3, 20, mb_convert_encoding($chunk ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L');
                    $cptLineSp++;
                }
                if ($cptLineSp > 0) $cptLineSp--;
            }

            /**
             * Customer
             */
            $pdf->Rect(0, $Yclient, $pageLargeur - 1, $clientH);
            $pdf->SetFont('Helvetica', 'UB', 40);
            $cptClientNom = 0;

            foreach($clientNom as $chunk)
            {
                $pdf->setXY(0, $Yclient+$cptClientNom*15);

                $pdf->Cell($pageLargeur - 3, 15, mb_convert_encoding($chunk ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'C', 0);
                $cptClientNom++;
            }
            if($cptClientNom>0) $cptClientNom--;
            $pdf->SetFont('Helvetica', '', 20);

            $cptClientAdd = 0;

            foreach($clientAdd as $chunk) {
                $pdf->setXY(0, $Yclient + 15 + $cptClientNom * 15 + $cptClientAdd*10);
                if (!strpos($chunk,'œu')) {
                $pdf->Cell($pageLargeur - 3, 20, mb_convert_encoding($chunk ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'C', 0);}
                else
                {

                    $chunk = iconv('utf-8', 'windows-1252', $chunk);
                    $pdf->Cell($pageLargeur - 3, 20, $chunk, 0, 0, 'C', 0);
                }

                $cptClientAdd++;
            }
            if ($cptClientAdd > 0) $cptClientAdd--;
            $pdf->SetFont('Helvetica', 'B', 23);

            $pdf->setXY(0, $Yclient + 30 + $cptClientNom*15 + $cptClientAdd*10 );

            $pdf->Cell($pageLargeur - 3, 20, mb_convert_encoding($clientContact ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'C', 0);

            /**
             * Order
             */
            $pdf->Rect(0, $Ycmd, $pageLargeur - 1, $cmdH);
            $pdf->setXY(0, $Yclient - 2);
            $x = 5;
            $x2 = 110;
            $x3 = 140;
            $x4 = 120;
            $x5 = 150;
            $pdf->setXY($x, $Ycmd);
            $pdf->SetFont('Helvetica', '', 20);
            $pdf->Cell($pageLargeur - 3, 20, mb_convert_encoding($cmdNum ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);
            $y = $Ycmd + 15;
            foreach ($detail as $line) {
                $pdf->setXY($x, $y);
                $pdf->Cell($pageLargeur, 10, mb_convert_encoding($line[0] ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);

                if($franchise->getPrintPriceBl())
                {
                    $pdf->setXY($x4+10, $y);
                    $pdf->Cell(50, 10, mb_convert_encoding($line[1] ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'R', 0);
                    $pdf->setXY($x5+30, $y);

                    $pdf->Cell(25, 10, chr(128) . " TTC", 0, 0, 'L', 0);
                }
                $y += 10;
            }
            //Frais de Livraison
            if ($franchise->getPrintPriceBl() && $totalSP > 0) {
                $pdf->setXY($x, $y);
                $pdf->Cell(50, 10, "LIVRAISON", 0, 0, 'L', 0);
                $pdf->setXY($x5+32, $y);
                $pdf->Cell(20, 10, $this->TrimTrailingZeroes($totalSP)." ".chr(128)." TTC", 0, 0, 'R', 0);
            }
            $y += 10;
            //Remises
            if ($franchise->getPrintPriceBl() && $discount < 0) {
                $pdf->setXY($x, $y);
                $pdf->Cell(50, 10, "REMISE", 0, 0, 'L', 0);
                $pdf->setXY($x5+32, $y);
                $pdf->Cell(20, 10, $this->TrimTrailingZeroes($discount)." ".chr(128)." TTC", 0, 0, 'R', 0);
            }
            $y += 10;
            $pdf->setXY($x, $y);
            $pdf->SetFont('Helvetica', 'I', 20);
            $pdf->Cell($pageLargeur, 10, "( Nombre total d'articles : ".$nbProduits." )", 0, 0, 'L', 0);
            $y += 10;

            if ($franchise->getPrintPriceBl()) {
                $pdf->SetFont('Helvetica', '', 16);
                $pdf->setXY($x4, $y);
                $pdf->Cell(45, 10, "HT", 0, 0, 'R', 0);
                $pdf->setXY($x5, $y);
                $pdf->Cell(50, 10, $totalHT. chr(128), 0, 0, 'R', 0);
                $y += 7;
                $pdf->setXY($x4, $y);
                $pdf->Cell(45, 10, "TVA 20%", 0, 0, 'R', 0);
                $pdf->setXY($x5, $y);
                $pdf->Cell(50, 10, $TVA20 . chr(128), 0, 0, 'R', 0);
                $y += 7;
                $pdf->setXY($x4, $y);
                $pdf->Cell(45, 10, "TVA 10%", 0, 0, 'R', 0);
                $pdf->setXY($x5, $y);
                $pdf->Cell(50, 10, $TVA10 . chr(128), 0, 0, 'R', 0);
                $y += 7;
                $pdf->setXY($x4+2, $y);
                $pdf->Cell(45, 10, "TVA 5,5%", 0, 0, 'R', 0);
                $pdf->setXY($x5, $y);
                $pdf->Cell(50, 10, $TVA5 . chr(128), 0, 0, 'R', 0);
                $y += 7;
                $pdf->setXY($x4, $y);
                $pdf->Cell(45, 10, "TTC", 0, 0, 'R', 0);
                $pdf->setXY($x5, $y);
                $pdf->Cell(50, 10, $TTC . chr(128), 0, 0, 'R', 0);
                $y += 10;
            }

            $pdf->setXY(0, $y);
            $pdf->SetFont('Helvetica', 'BI', 20);
            $pdf->Cell($pageLargeur - 8, 10, mb_convert_encoding($merci ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'R', 0);


            /**
             * Paiement
             */

                $pdf->Rect(0, $Ypayment, $pageLargeur - 1, $paymentH);
                $pdf->setXY(0, $Ypayment );
                $pdf->SetFont('Helvetica', 'UB', 30);
                $pdf->Cell($pageLargeur - 3, 20, mb_convert_encoding($modeDePaiement ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'C', 0);

                $pdf->SetFont('Helvetica', 'B', 20);
                $y = $Ypayment + 24;
                $pdf->setXY(5, $y + 2.5);
                $pdf->Cell(100, 5, mb_convert_encoding($paymentType1 ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);
                $pdf->setXY(60, $y + 2.5);
                $pdf->Cell(100, 5, mb_convert_encoding($paymentType2 ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);
                $pdf->setXY(120, $y + 2.5);
                $pdf->Cell(100, 5, mb_convert_encoding($paymentType3 ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);

                $pdf->Rect(40, $y, 10, 10);
                $pdf->Rect(100, $y, 10, 10);
                $pdf->Rect(185, $y, 10, 10);
                $pdf->SetFont('Helvetica', 'B', 30);
                switch ($paymentMethod) {
                    case Differe::PAYMENT_METHOD_DIFFERE_CODE:
                        $pdf->setXY(100, $y + 3);
                        $pdf->Cell(10, 5, "X", 0, 0, 'L', 0);
                        break;
                    case Livraison::PAYMENT_METHOD_LIVRAISON_CODE:
                        $pdf->setXY(185, $y + 3);
                        $pdf->Cell(10, 5, "X", 0, 0, 'L', 0);
                        break;
                    default:
                        $pdf->setXY(40, $y + 3);
                        $pdf->Cell(10, 5, "X", 0, 0, 'L', 0);
                        break;
                }


            $pdf->SetFont('Helvetica', 'B', 20);
            $y = $Ypayment + 24;
            $pdf->setXY(5, $y + 2.5);
            $pdf->Cell(100, 5, mb_convert_encoding($paymentType1 ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);
            $pdf->setXY(60, $y + 2.5);
            $pdf->Cell(100, 5, mb_convert_encoding($paymentType2 ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);
            $pdf->setXY(120, $y + 2.5);
            $pdf->Cell(100, 5, mb_convert_encoding($paymentType3 ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);

            $pdf->Rect(40, $y, 10, 10);
            $pdf->Rect(100, $y, 10, 10);
            $pdf->Rect(185, $y, 10, 10);
            $pdf->SetFont('Helvetica', 'B', 30);
            switch ($paymentMethod) {
                case Differe::PAYMENT_METHOD_DIFFERE_CODE:
                    $pdf->setXY(100, $y + 3);
                    $pdf->Cell(10, 5, "X", 0, 0, 'L', 0);
                    break;
                case Livraison::PAYMENT_METHOD_LIVRAISON_CODE:
                    $pdf->setXY(185, $y + 3);
                    $pdf->Cell(10, 5, "X", 0, 0, 'L', 0);
                    break;
                default:
                    $pdf->setXY(40, $y + 3);
                    $pdf->Cell(10, 5, "X", 0, 0, 'L', 0);
                    break;
            }

            /**
             * Signature
             */
            $pdf->AddPage('L', array($pageLargeur1, $hauteurPageSignature));
            $pdf->setMargins(0.1, 0.1, 0.1);
            $pdf->Rect(0, 0, $pageLargeur - 1, $hauteurPageSignature - 1);
            $pdf->SetFont('Helvetica', 'B', 20);
            $pdf->setXY(5, 3);
            $pdf->Cell(200, 10, mb_convert_encoding($signature ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);
            $pdf->SetFont('Helvetica', '', 20);
            $pdf->setXY(5, 14);
            $pdf->MultiCell(200, 8, $signatureText, 0, 'L', 0);

            $nbrpage = 2;

            if (count($lots) > 1) {
                foreach ($lots as $lot) {
                    $hauteurPageLot = 30;

                    foreach ($lot['items'] as $item) {
                        $hauteurPageLot += 25;
                        if (isset($item['option']) && $item['option'] != "") {
                            $hauteurPageLot += 10;
                        }
                    }

                    if ($hauteurPageLot < 100) $hauteurPageLot = 100;

                    if ($pageLargeur1 > $hauteurPageLot) {
                        $pdf->AddPage('L', array($pageLargeur1, $hauteurPageLot));
                    } else {
                        $pdf->AddPage('P', array($pageLargeur1, $hauteurPageLot));
                    }
                    $nbrpage++;
                    $pdf->setMargins(0.1, 0.1, 0.1);
                    $pdf->Rect(0, 0, $pageLargeur - 1, $hauteurPageLot - 1);

                    $header_split = 0;
                    // split name
                    if ($hasSplit) {
                        $header_split = 15;
                        $hauteurPageLot += $header_split;
                        $pdf->SetFont('Helvetica', 'U', 40);
                        $pdf->setXY(5, 5);
                        $pdf->Cell(90, 10, substr(mb_convert_encoding($lot['name'] ?? '', 'ISO-8859-1', 'UTF-8'), 0, 20), 0, 0, 'L', 0);

                    }

                    // split price
                    if ($hasSplit) {
                        $pdf->SetFont('Helvetica', 'U', 40);
                        $pdf->setXY(85, 5 + $header_split);
                        $pdf->Cell(30, 10, $this->TrimTrailingZeroes($lot['price']) . chr(128), 0, 0, 'R', 0);
                    }

                    //element
                    $pdf->SetFont('Helvetica', 'U', 35);
                    $pdf->setXY(115, 5 + $header_split);
                    $pdf->Cell(100, 10, $lot['element'], 0, 0, 'L', 0);

                    //zone & number
                    $pdf->SetFont('Helvetica', 'B', 35);
                    $pdf->Rect(150, 0, $pageLargeur - 150 - 1, $hauteurPageLot / 2);
                    $pdf->setXY(150, 5);
                    $pdf->MultiCell($pageLargeur - 150 - 1, 15, $lot['zone'], 0, 'C', 0);
                    $pdf->Rect(150, $hauteurPageLot / 2, $pageLargeur - 150 - 1, $hauteurPageLot / 2 - 1);
                    $pdf->setXY(150, ($hauteurPageLot / 2) + 10);
                    $pdf->Cell($pageLargeur - 150 - 1, 10,
                        mb_convert_encoding("N° " . $lot['number'], 'ISO-8859-1', 'UTF-8'), 0, 0, 'C', 0);

                    //items
                    $hauteurLigneName = 7;
                    $y = 30 + $header_split;
                    foreach ($lot['items'] as $item) {
                        $pdf->SetFont('Helvetica', '', 20);
                        $cptLotName = 0;
                        foreach (str_split($item['qty'] . ' ' . $item['name'], 30) as $chunk) {
                            $pdf->setXY(5, $y + $cptLotName * $hauteurLigneName);
                            $pdf->Cell($pageLargeur - 150 - 1, 10,
                                mb_convert_encoding($chunk ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);
                            $cptLotName++;
                        }
                        if ($cptLotName > 0) $cptLotName--;

                        if (isset($item['option']) && $item['option'] != "") {
                            $y += 25 + $cptLotName * $hauteurLigneName;
                            $pdf->line(0, $y, 150 - 1, $y);
                            $pdf->setXY(2, $y - 8);
                            $pdf->SetFont('Helvetica', '', 15);
                            $pdf->Cell($pageLargeur, 10,
                                mb_convert_encoding($item['option'] ?? '', 'ISO-8859-1', 'UTF-8'), 0, 0, 'L', 0);
                            $y += 10;
                        } else {
                            $y += 15 + $cptLotName * $hauteurLigneName;
                        }
                    }
                }
            }

            $franchiseCode = $franchise->getCode();

            $storeFront = $this->storeRepository->getById(1);
            $storeCode = $storeFront->getCode();

            $unixUserCode = $franchiseCode.'_'.$storeCode;

            $pdfPath = $this->getBlDirectory($printerHostname, $unixUserCode);
            $fullFilename = $this->getBlFullFileName($order);
            $filename = $this->getBlFileName($order);

            if (!is_dir($pdfPath)) {
                mkdir($pdfPath, 0777, true);
            }

            $brut = $pdf->Output('F', $pdfPath . DIRECTORY_SEPARATOR . $fullFilename);

            $readyPath = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath() . self::BL_FOLDER . DIRECTORY_SEPARATOR . "ready/";
            if (!is_dir($readyPath)) {
                mkdir($readyPath,0777,true);
            }
            shell_exec("rm -fr " . $readyPath . DIRECTORY_SEPARATOR . $unixUserCode . DIRECTORY_SEPARATOR . self::BL_FOLDER . DIRECTORY_SEPARATOR . $printerHostname . DIRECTORY_SEPARATOR . $filename);

            if (!is_dir($readyPath . $unixUserCode)) {
                mkdir($readyPath . $unixUserCode,0777,true);
            }
            if (!is_dir($readyPath . $unixUserCode . DIRECTORY_SEPARATOR . self::BL_FOLDER . DIRECTORY_SEPARATOR . $printerHostname)) {
                mkdir($readyPath . $unixUserCode . DIRECTORY_SEPARATOR . self::BL_FOLDER . DIRECTORY_SEPARATOR . $printerHostname,0777,true);
                shell_exec("chmod 775 " .$readyPath . $unixUserCode . DIRECTORY_SEPARATOR . self::BL_FOLDER . DIRECTORY_SEPARATOR . $printerHostname);
            }
            shell_exec("mv ".$this->filesystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath() . self::BL_FOLDER . DIRECTORY_SEPARATOR.$unixUserCode . DIRECTORY_SEPARATOR . self::BL_FOLDER . DIRECTORY_SEPARATOR.$printerHostname."/* ".$readyPath.$unixUserCode.DIRECTORY_SEPARATOR. self::BL_FOLDER . DIRECTORY_SEPARATOR .$printerHostname."/");
            shell_exec("chmod -R 664 ".$readyPath.$unixUserCode.DIRECTORY_SEPARATOR. self::BL_FOLDER . DIRECTORY_SEPARATOR .$printerHostname."/*");
        }
    }

    /**
     * @return string
     */
    public function getBlDirectory($printerHostname, $unixUserCode)
    {
        return $this->filesystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath() . self::BL_FOLDER .DIRECTORY_SEPARATOR . $unixUserCode . DIRECTORY_SEPARATOR . self::BL_FOLDER. DIRECTORY_SEPARATOR . $printerHostname . DIRECTORY_SEPARATOR;
    }

    /**
     * @param CartInterface $quote
     * @return string
     */
    public function getBlFileName($order)
    {
        return self::Bl_FILE_PREFIX . '_' . $this->date->date()->format('Y-m-d').'_' . $order->getEntityId();
    }

    /**
     * @param CartInterface $quote
     * @return string
     */
    public function getBlFullFileName($order)
    {
        $filename = $this->getBlFileName($order);
        return $filename. '.pdf';
    }

    /**
     * @return string
     */
    public function getImageDirectory()
    {
        return $this->filesystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath() . self::BL_FOLDER.DIRECTORY_SEPARATOR.'images/';
    }

    function TrimTrailingZeroes($nbr) {
        return strpos($nbr,'.')!==false ? rtrim(rtrim($nbr,'0'),'.') : $nbr;
    }

    private static function cmp($a, $b)
    {
        return strcmp($a[2], $b[2]);
    }

}


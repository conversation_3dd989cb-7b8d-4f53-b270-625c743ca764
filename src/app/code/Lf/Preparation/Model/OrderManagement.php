<?php
/**
 * @category  Lf
 * @package   Lf_Preparation
 * @Copyright 2018 Adin
 * @license   Apache License Version 2.0
 */

namespace Lf\Preparation\Model;


use Lf\Franchise\Api\TimeslotRepositoryInterface;
use Lf\Opc\Model\ResourceModel\ShippingDatasRepository;
use Lf\ProductionDisplay\Model\ResourceModel\ProductionDisplay\CollectionFactory as ProductionDisplayCollectionFactory;
use Magento\Catalog\Model\ProductRepository;
use Magento\Sales\Model\ResourceModel\Order\Item\CollectionFactory as ItemCollectionFactory;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class OrderManagement
{

    private $itemCollectionFactory;
    private $productRepository;
    private $productionDisplayCollectionFactory;
    private $shippingDatasRepository;
    /**
     * @var TimezoneInterface
     */
    private $date;
    /**
     * @var TimeslotRepositoryInterface
     */
    private $timeslotRepository;

    public function __construct(
        ItemCollectionFactory $itemCollectionFactory,
        ProductRepository $productRepository,
        TimezoneInterface $date,
        ShippingDatasRepository $shippingDatasRepository,
        ProductionDisplayCollectionFactory $productionDisplayCollectionFactory,
        TimeslotRepositoryInterface $timeslotRepository
    )
    {

        $this->itemCollectionFactory = $itemCollectionFactory;
        $this->productRepository = $productRepository;
        $this->productionDisplayCollectionFactory = $productionDisplayCollectionFactory;
        $this->date = $date;
        $this->shippingDatasRepository = $shippingDatasRepository;
        $this->timeslotRepository = $timeslotRepository;
    }

    public function releaseProducts($order)
    {
        $itemCollection = $this->itemCollectionFactory->create();
        $itemCollection->addFieldToFilter('order_id', $order->getEntityId());

        $shippingData = $this->shippingDatasRepository->getByQuoteId($order->getQuoteId());
        $timeslot = $this->timeslotRepository->getById($shippingData->getTimeslotId());

        $today = $this->date->date()->format('Y-m-d');

        foreach ($itemCollection as $item) {
            $productionDisplayCollection = $this->productionDisplayCollectionFactory->create();
            $productionDisplayCollection
                ->addFieldToFilter('product_id', $item->getProductId())
                ->addFieldToFilter('franchise_id', $shippingData->getFranchiseId())
                ->addFieldToFilter('day', $today);

            foreach ($productionDisplayCollection as $productionDisplay) {
                if ($timeslot->getIsRetention() && $productionDisplay->getMerged() == 0) {
                    $productionDisplay
                        ->setQtyToProduceAfternoon($productionDisplay->getQtyToProduceAfternoon() - $item->getQtyOrdered());
                } else {
                    $productionDisplay
                        ->setQtyToProduceMorning($productionDisplay->getQtyToProduceMorning() - $item->getQtyOrdered());
                }

                if ($item->getQtyAffected() > 0) {
                    $productionDisplay
                        ->setQtyAffected($productionDisplay->getQtyAffected() - $item->getQtyAffected());
                }

                $productionDisplay->save();
            }
        }
    }
}
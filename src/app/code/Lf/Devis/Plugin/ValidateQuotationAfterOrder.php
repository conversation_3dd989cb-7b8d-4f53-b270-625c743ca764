<?php
declare(strict_types=1);

namespace Lf\Devis\Plugin;

use Closure;
use Lf\Devis\Api\Exception\QuotationNotFoundException;
use Lf\Devis\Api\QuotationRepositoryInterface;
use Lf\Devis\Domain\QuotationStatusChange\UseCase\QuotationValidation\QuotationValidation;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\PaymentInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\QuoteManagement;

class ValidateQuotationAfterOrder
{
    private QuotationValidation $quotationValidation;

    private CartRepositoryInterface $cartRepository;

    private State $state;

    private QuotationRepositoryInterface $quotationRepository;

    public function __construct(
        QuotationValidation $quotationValidation,
        State $state,
        CartRepositoryInterface $cartRepository,
        QuotationRepositoryInterface $quotationRepository
    ) {
        $this->quotationValidation = $quotationValidation;
        $this->cartRepository = $cartRepository;
        $this->state = $state;
        $this->quotationRepository = $quotationRepository;
    }

    /**
     * Enables order creation on disabled quotes on the backend.
     *
     * The place order method tries to retrieve an active quote and fails with an inactive quote.
     */
    public function aroundPlaceOrder(
        QuoteManagement $quoteManagement,
        Closure $proceed,
        $cartId,
        PaymentInterface $paymentMethod = null
    ) {
        if ($this->state->getAreaCode() !== Area::AREA_ADMINHTML) {
            return $proceed($cartId, $paymentMethod);
        }

        $cart = $this->cartRepository->get($cartId);

        $isActive = $cart->getIsActive();

        $cart->setIsActive(true);

        $this->cartRepository->save($cart);

        try {
            return $proceed($cartId, $paymentMethod);
        } catch (\Exception $e) {
            if (!$isActive) {
                $cart->setIsActive(false);
                $this->cartRepository->save($cart);
            }

            throw $e;
        }
    }

    /**
     * Validates the quotation after a successful order creation.
     */
    public function afterSubmit(
        QuoteManagement $quoteManagement,
        $result,
        Quote $quote
    ) {
        try {
            $quotation = $this->quotationRepository->getByQuoteId($quote->getId());
        } catch (QuotationNotFoundException) {
            // Not a quotation.
            return $result;
        }

        $this->quotationValidation->execute($quotation->getQuotationId());

        return $result;
    }
}

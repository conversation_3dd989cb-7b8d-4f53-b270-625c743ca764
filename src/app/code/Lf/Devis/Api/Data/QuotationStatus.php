<?php

namespace Lf\Devis\Api\Data;

/**
 * Enum that contains the possible statuses for quotations.
 */
enum QuotationStatus: string
{
    // The quotation is in progress. It has not been to the customer, or it has been modified since.
    case IN_PROGRESS = 'in_progress';

    // The quotation has been sent to the customer.
    case SENT_TO_CUSTOMER = 'sent_to_customer';

    // The quotation is validated, this status is final.
    case VALIDATED = 'validated';

    // The quotation has been refused by LF or the customer.
    case REFUSED = 'refused';

    // A reminder email has been sent to the customer.
    case CUSTOMER_REMINDER = 'customer_reminder';

    // The quotation has been canceled by LF or the customer.
    case CANCELED = 'canceled';

    // The quotation is expired.
    case EXPIRED = 'expired';
}

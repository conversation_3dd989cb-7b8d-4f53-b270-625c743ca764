<?php

namespace Lf\Devis\Api\Data;

interface QuotationInterface
{
    public const QUOTE_ID = 'quote_id';
    public const INCREMENT_ID = 'increment_id';
    public const CREATED_AT = 'created_at';
    public const EXPIRATION_DATE = 'expiration_date';
    public const STATUS = 'status';
    public const TYPE = 'quotation_type';

    /**
     * Returns the quotation id.
     */
    public function getQuotationId();

    /**
     * Returns the quote id associated with this quotation.
     */
    public function getQuoteId();

    /**
     * Returns the quotation title.
     */
    public function getTitle();

    /**
     * Returns the quotation increment id.
     */
    public function getIncrementId(): string;

    /**
     * Returns the quotation reference.
     */
    public function getReference(): string;

    /**
     * Returns the quotation reference.
     */
    public function getType(): QuotationType;

    /**
     * Returns the quotation current status.
     */
    public function getStatus(): QuotationStatus;

    /**
     * Returns the quotation expiration date.
     */
    public function getExpirationDate(): \DateTimeInterface;

    /**
     * Returns the quotation expiration date.
     */
    public function getCreatedAt(): \DateTimeInterface;

    /**
     * Returns the list of past quotation statuses.
     *
     * It is ordered from older to newer statuses.
     *
     * @return \Lf\Devis\Api\Data\QuotationStatusHistoryInterface[]
     */
    public function getStatusHistory(): array;

    /**
     * Sets the quotation title.
     */
    public function setTitle($title): void;

    /**
     * Sets the quotation reference.
     */
    public function setReference($reference): void;

    /**
     * Sets the quotation expiration date.
     */
    public function setExpirationDate(\DateTimeInterface $expirationDate): void;
}

<?php
declare(strict_types=1);

namespace Lf\Devis\Domain\BackendQuotation\UseCase\QuotationDuplication;

use Lf\Devis\Domain\BackendQuotation\UseCase\Common\DuplicationServiceInterface;

/**
 * Use case class for quotation duplication on the backend.
 */
class QuotationDuplication
{
    private DuplicationServiceInterface $duplicationService;

    public function __construct(DuplicationServiceInterface $duplicationService)
    {
        $this->duplicationService = $duplicationService;
    }

    /**
     * Creates a new inactive anonymous cart from an existing cart on the backend.
     *
     * @param mixed $cartId The identifier for the cart to be duplicated.
     */
    public function execute($cartId, OutputPortInterface $outputPort): void
    {
        $newCartId = $this->duplicationService->duplicateCart($cartId);

        $outputPort->outputSuccess($newCartId);
    }
}

<?php
declare(strict_types=1);

namespace Lf\Devis\Domain\CustomerQuotation\UseCase\QuotationUnload;

class CustomerQuotationUnload
{
    private DataGatewayInterface $dataGateway;

    public function __construct(DataGatewayInterface $dataGateway)
    {
        $this->dataGateway = $dataGateway;
    }

    /**
     * Disables the currently active quotation and replaces it with a new empty cart.
     */
    public function execute(OutputPortInterface $outputPort): void
    {
        $this->dataGateway->activateNewCustomerCart();

        $outputPort->render();
    }
}

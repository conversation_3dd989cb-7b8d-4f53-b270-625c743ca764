<?php
declare(strict_types=1);

namespace Lf\Devis\Domain\QuotationCommon\QuotationPdf\PdfPresenter;

use Lf\Devis\Domain\QuotationCommon\QuotationPdf\DataModel;
use Lf\Devis\Domain\QuotationCommon\QuotationPdf\PresenterInterface;

class PdfPresenter implements PresenterInterface
{
    private PdfViewInterface $pdfView;

    public function __construct(PdfViewInterface $pdfView)
    {
        $this->pdfView = $pdfView;
    }

    public function presentPdf(DataModel $dataModel): string
    {
        $viewModel = new ViewModel(
            customerId: $dataModel->getCustomerId(),
            customerLastname: $dataModel->getCustomerLastname(),
            shippingDate: $dataModel->getShippingDate(),
            billingCompany: $dataModel->getBillingCompany(),
            cartId: $dataModel->getCartId(),
            filePath: $this->getPdfPath($dataModel)
        );

        return $this->pdfView->writePdf($viewModel);
    }

    private function getPdfPath(DataModel $dataModel): string
    {
        $shippingDate = $dataModel->getShippingDate()->format('Ymd');

        $customerName = mb_strtolower($dataModel->getCustomerLastname());

        $customerId = $dataModel->getCustomerId();

        $filename = "devis/$customerId/devis_{$shippingDate}_$customerName";

        if ($dataModel->getBillingCompany() != null) {
            $filename .= '_' . mb_strtolower(str_replace(' ', '', $dataModel->getBillingCompany()));
        }

        return "$filename.pdf";
    }
}

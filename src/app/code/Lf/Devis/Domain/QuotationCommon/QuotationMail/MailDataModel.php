<?php
declare(strict_types=1);

namespace Lf\Devis\Domain\QuotationCommon\QuotationMail;

class MailDataModel
{
    private $cartId;

    private string $customerFirstName;

    private string $customerLastName;

    private string $company;

    private string $phoneNumber;

    private \DateTimeInterface $shippingDate;

    private ?string $message;

    private ?string $shippingInstructions;

    private array $recipients;

    private array $replyTo;

    private string $pdfPath;

    public function __construct(
        $cartId,
        string $customerFirstName,
        string $customerLastName,
        string $company,
        string $phoneNumber,
        \DateTimeInterface $shippingDate,
        ?string $message,
        ?string $shippingInstructions,
        string $pdfPath,
        array $recipients,
        array $replyTo
    ) {
        $this->cartId = $cartId;
        $this->customerFirstName = $customerFirstName;
        $this->customerLastName = $customerLastName;
        $this->company = $company;
        $this->phoneNumber = $phoneNumber;
        $this->shippingDate = $shippingDate;
        $this->message = $message;
        $this->shippingInstructions = $shippingInstructions;
        $this->recipients = $recipients;
        $this->replyTo = $replyTo;
        $this->pdfPath = $pdfPath;
    }

    public function getCartId()
    {
        return $this->cartId;
    }

    public function getCustomerFirstName(): string
    {
        return $this->customerFirstName;
    }

    public function getCustomerLastName(): string
    {
        return $this->customerLastName;
    }

    public function getCompany(): string
    {
        return $this->company;
    }

    public function getPhoneNumber(): string
    {
        return $this->phoneNumber;
    }

    public function getShippingDate(): \DateTimeInterface
    {
        return $this->shippingDate;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function getShippingInstructions(): ?string
    {
        return $this->shippingInstructions;
    }

    public function getRecipients(): array
    {
        return $this->recipients;
    }

    public function getReplyTo(): array
    {
        return $this->replyTo;
    }

    public function getPdfPath(): string
    {
        return $this->pdfPath;
    }
}

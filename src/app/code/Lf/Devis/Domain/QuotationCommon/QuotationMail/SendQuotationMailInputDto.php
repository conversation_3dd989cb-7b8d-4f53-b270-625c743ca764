<?php
declare(strict_types=1);

namespace Lf\Devis\Domain\QuotationCommon\QuotationMail;

class SendQuotationMailInputDto
{
    private string $quotationId;

    private array $replyTo;

    private array $to;

    private ?string $message;

    public function __construct(
        string $quotationId,
        array $to = [],
        array $replyTo = [],
        ?string $message = null
    ) {
        $this->quotationId = $quotationId;
        $this->replyTo = $replyTo;
        $this->to = $to;
        $this->message = $message;
    }

    public function getReplyTo(): array
    {
        return $this->replyTo;
    }

    public function getTo(): array
    {
        return $this->to;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function getQuotationId(): string
    {
        return $this->quotationId;
    }
}

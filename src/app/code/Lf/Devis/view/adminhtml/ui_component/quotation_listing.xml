<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">quotation_listing.quotation_listing_data_source</item>
            <item name="deps" xsi:type="string">quotation_listing.quotation_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">spinner_columns</item>
    </argument>
    <dataSource name="quotation_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">
                Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider
            </argument>
            <argument name="name" xsi:type="string">quotation_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">entity_id</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="sticky" xsi:type="boolean">true</item>
            </item>
        </argument>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <filterSelect name="status" provider="${ $.parentName }"
                          component="Magento_Ui/js/form/element/ui-select"
                          template="ui/grid/filters/elements/ui-select">
                <settings>
                    <options class="Lf\Devis\Model\Config\Source\QuotationStatus"/>
                    <label translate="true">Statut</label>
                    <dataScope>status</dataScope>
                </settings>
            </filterSelect>
        </filters>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="spinner_columns">
        <column name="increment_id" sortOrder="10">
            <settings>
                <filter>text</filter>
                <label translate="true">ID</label>
            </settings>
        </column>
        <column name="title" sortOrder="20">
            <settings>
                <filter>text</filter>
                <label translate="true">Titre</label>
            </settings>
        </column>
        <column name="total" class="Magento\Sales\Ui\Component\Listing\Column\Price" sortOrder="30">
            <settings>
                <label translate="true">Montant HT</label>
            </settings>
        </column>
        <column name="orderId" sortOrder="40">
            <settings>
                <label translate="true">Référence commande</label>
            </settings>
        </column>
        <column name="invoiceId" sortOrder="50">
            <settings>
                <label translate="true">Référence facture</label>
            </settings>
        </column>
        <column name="status" component="Magento_Ui/js/grid/columns/select" sortOrder="60">
            <settings>
                <options class="Lf\Devis\Model\Config\Source\QuotationStatus"/>
                <dataType>select</dataType>
                <label translate="true">Statut</label>
            </settings>
        </column>
        <column name="customer_email" sortOrder="70">
            <settings>
                <filter>text</filter>
                <label translate="true">Email client</label>
            </settings>
        </column>
        <column name="customerName" sortOrder="80">
            <settings>
                <label translate="true">Nom client</label>
                <filter>text</filter>
            </settings>
        </column>
        <column name="companyName" sortOrder="90">
            <settings>
                <label translate="true">Société</label>
                <filter>text</filter>
            </settings>
        </column>
        <column name="shippingAddress" sortOrder="100">
            <settings>
                <label translate="true">Adresse livraison</label>
            </settings>
        </column>
        <column name="billingAddress" sortOrder="110">
            <settings>
                <label translate="true">Adresse facturation</label>
            </settings>
        </column>
        <column name="comment" sortOrder="120">
            <settings>
                <label translate="true">Commentaire</label>
            </settings>
        </column>
        <column name="shipping_date" component="Magento_Ui/js/grid/columns/date" sortOrder="125">
            <settings>
                <dateFormat>d MMM. YYYY</dateFormat>
                <filter>dateRange</filter>
                <label translate="true">Date de livraison</label>
            </settings>
        </column>
        <column name="expiration_date" component="Magento_Ui/js/grid/columns/date" sortOrder="130">
            <settings>
                <dateFormat>d MMM. YYYY</dateFormat>
                <filter>dateRange</filter>
                <label translate="true">Expiration</label>
            </settings>
        </column>
        <column name="quotation_type" component="Magento_Ui/js/grid/columns/select" sortOrder="140">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <options>
                    <option name="devis" xsi:type="array">
                        <item name="value" xsi:type="string">1</item>
                        <item name="label" xsi:type="string" translate="true">devis</item>
                    </option>
                    <option name="edevis" xsi:type="array">
                        <item name="value" xsi:type="string">2</item>
                        <item name="label" xsi:type="string" translate="true">e-devis</item>
                    </option>
                </options>
                <label translate="true">Type devis</label>
            </settings>
        </column>
        <column name="created_at" component="Magento_Ui/js/grid/columns/date" sortOrder="150">
            <settings>
                <dateFormat>d MMM. YYYY</dateFormat>
                <filter>dateRange</filter>
                <label translate="true">Date de création</label>
            </settings>
        </column>
        <column name="created_by" sortOrder="160">
            <settings>
                <label translate="true">Créé par</label>
                <filter>text</filter>
            </settings>
        </column>
        <column name="updated_at" component="Magento_Ui/js/grid/columns/date" sortOrder="170">
            <settings>
                <dateFormat>d MMM. YYYY</dateFormat>
                <filter>dateRange</filter>
                <label translate="true">Date de mise à jour</label>
            </settings>
        </column>
        <column name="updated_by" sortOrder="180">
            <settings>
                <label translate="true">Mis à jour par</label>
                <filter>text</filter>
            </settings>
        </column>
        <actionsColumn name="actions" class="Lf\Devis\Ui\Component\Listing\Column\QuotationActions"/>
    </columns>
</listing>


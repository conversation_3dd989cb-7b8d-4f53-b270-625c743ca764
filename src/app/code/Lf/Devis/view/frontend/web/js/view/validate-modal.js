define([
    'j<PERSON>y',
    'mage/translate',
    'Magento_Ui/js/modal/modal'
], function ($, $t, modal) {
    'use strict';

    return function (config, element) {
        var options = {
            'type': 'popup',
            'title': $t('Validez votre devis'),
            'modalClass': 'popup-devis',
            'responsive': true,
            'innerScroll': true,
            'buttons': [],
        };

        modal(options, $(element));

        $(config.target).on('click', function (event) {
            const modalContent = $(event.currentTarget).parent().find('[name="modal-body"]');

            $(element).find('.validate-message').html(modalContent.val());

            $(element).modal('openModal');
        });
    };
});

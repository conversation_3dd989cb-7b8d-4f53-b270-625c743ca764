bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
exit
composer
composer install --ignore-platform-reqs
binmagento
bin/magento
bin/magento
pwd
exit
bin/magento c:c
bin/magento
bin/magento setup:upgrade
flush privileges;
exit
bin/magento
bin/magento setup:upgrade
bin/magento maintence:disable
bin/magento maintenance:disable
ps -ef
ls
cd var
cd log
lsl
ls
ls -altr
tail system
tail system.log 
tail exception.log 
tail debug.log 
exit
bin/magento c:f
cd var/log
ls -altr
vi systempay.log 
exit
bin/magento setup:upgrade
php bin/magento setup:di:compile
bin/magento setup:static-content:deploy 
composer require magepal/magento2-googletagmanager  --ignore-platform-reqs
bin/magento module:enable --clear-static-content MagePal_GoogleTagManager
bin/magento setup:upgrade
exit
bin/magento setup:db:status
composer install  --ignore-platform-reqs
bin/magento setup:upgrade
composer require magefan/module-google-tag-manager
bin/magento setup:upgrade
bin/magento setup:di:compile
exit
composer install  --ignore-platform-reqs
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:db:status
bin/magento setup:upgrade
bin/magento maintenance:disable
exit
cd var
cd log
ls -al
tail debug.log 
exit
ping varnish
exit
bin/magento c:c:
bin/magento c:c
bin/magento 
bin/magento setup:db:status
bin/magento setup:upgrade
cd var
cd log
tail debug.log 
bin/magento c:disable
cd
bin/magento c:disable
exit
bin/magento c:enable
ls
./n98-magerun2.phar sys:cron:list
./n98-magerun2.phar sys:cron:run eatlf_generate_bl
bin/magento test-bl-eat
bin/magento test-bl-eat 33842 
bin/magento test-bl-eat --order-id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33842 
bin/magento test-bl-eat --order_id 33840
bin/magento test-bl-eat --order_id 33800
bin/magento test-bl-eat --order_id 33755
bin/magento test-bl-eat --order_id 33843
bin/magento test-bl-eat --order_id 49091
bin/magento test-bl-eat --order_id 49091
bin/magento test-bl-eat --order_id 49091
bin/magento test-bl-eat --order_id 49091
bin/magento test-bl-eat --order_id 33843
bin/magento test-bl-eat --order_id 33800
exit
bin/magento setup:upgrade
ls -al
composer install  --ignore-platform-reqs
cd bin
composer install  --ignore-platform-reqs
exit
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento setup:upgrade
composer install --ignore-reqs
composer install --ignore-platform-reqs
composer install --ignore-platform-reqs
exit
bin/magento setup:upgrade
composer install --ignore-platform-reqs
bin/magento setup:upgrade
tail var/log/system.log
ping varnish
tail var/log/system.log
tail var/log/system.log
exir
exit
composer install --ignore-platform-reqs
bin/magento setup:upgrade
exit
bin/magento setup:upgrade
exit
composer install --ignore-platform-reqs
bin/magento setup:upgrade
exit
bin/magento setup:upgrade
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:upgrade
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento setup:di:compile
bin/magento setup:di:compile
bin/magento setup:di:compile
bin/magento setup:di:compile
bin/magento setup:di:compile
bin/magento setup:di:compile
bin/magento setup:di:compile
exit
bin/magento c:c config
bin/magento c:c config
bin/magento c:c config
bin/magento c:c config
cd var
cd log
vi system.log
cd
bin/magento c:c
ls
cd  bin
ls
cd
./n98-magerun2.phar sys:cron:run set_colis_id
./n98-magerun2.phar sys:cron:run set_colis_id
./n98-magerun2.phar cache:clean
./n98-magerun2.phar cache:clean
./n98-magerun2.phar sys:cron:run set_colis_id
./n98-magerun2.phar cache:clean
./n98-magerun2.phar cache:clean
./n98-magerun2.phar cache:clean
./n98-magerun2.phar index:reindex
./n98-magerun2.phar sys:cron:run set_colis_id
./n98-magerun2.phar sys:cron:run set_colis_id
exit
tail var/log/system.log
exit
bin/magento
bin/magento info:language:list
bin/magento i18n:collect-phrases
bin/magento i18n:collect-phrases --magento
bin/magento i18n:collect-phrases --magento |grep -i "In progress"
bin/magento i18n:collect-phrases --magento |grep -i "Delivered"
bin/magento i18n:collect-phrases 
bin/magento i18n:
rm pub/static/frontend/EatLf/default/fr_FR/js-translation.json 
m pub/static/frontend/EatLf/default/fr_FR/js-translation.json 
ls -al  pub/static/frontend/EatLf/default/fr_FR/js-translation.json 
bin/magento c:c
ls -al  pub/static/frontend/EatLf/default/fr_FR/js-translation.json 
bin/magento setup:static:deploy
bin/magento setup:static:deploy -f
ls -al  pub/static/frontend/EatLf/default/fr_FR/js-translation.json 
bin/magento i18n:collect-phrases --magento |grep -i "In progress"
bin/magento i18n:collect-phrases --magento |grep -i "delivery"
rm pub/static/frontend/EatLf/default/fr_FR/js-translation.json 
bin/magento setup:static:deploy fr_FR -f
bin/magento cache:flush
rm pub/static/frontend/EatLf/default/fr_FR/js-translation.json 
bin/magento setup:static:deploy  -f
bin/magento cache:flush
rm pub/static/frontend/EatLf/default/fr_FR/js-translation.json 
bin/magento setup:static:deploy  -f
bin/magento cache:flush
bin/magento cache:c
bin/magento cache:c
rm pub/static/frontend/EatLf/default/fr_FR/js-translation.json 
bin/magento cache:c
bin/magento cache:flush
bin/magento cache:flush
bin/magento cache:flush
bin/magento cache:flush
bin/magento setup:static:deploy  -f
bin/magento cache:flush
bin/magento setup:static:deploy  -f
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
bin/magento setup:static:deploy  -f
bin/magento cache:clean
bin/magento cache:clean
find . -name js-translation.json -exec rm {} \;
bin/magento setup:static:deploy  -f
find . -name js-translation.json -exec rm {} \;
bin/magento setup:static:deploy  -f
bin/magento cache:clean
find . -name js-translation.json -exec rm {} \;
bin/magento setup:static:deploy  -f
bin/magento cache:clean
find . -name js-translation.json -exec rm {} \;
bin/magento cache:clean
bin/magento setup:static:deploy  -f
find . -name js-translation.json -exec rm {} \;
bin/magento setup:static:deploy  -f
bin/magento cache:clean
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;

bin/magento deploy:mode:show
find . -name js-translation.json -exec rm {} \;
bin/magento cache:clean
find . -name js-translation.json -exec rm {} \;
bin/magento setup:static:deploy  -f
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
bin/magento cache:clean
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
bin/magento cache:clean
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
find . -name js-translation.json -exec rm {} \;
bin/magento c:c
./n98-magerun2.phar sys:cron:run set_colis_id
bin/magento c:c
./n98-magerun2.phar sys:cron:run set_colis_id
./n98-magerun2.phar sys:cron:run set_colis_id
exit
bin/magento config:show dev/grid/async_indexing
exit
./n98-magerun2.phar sys:cron:run set_colis_id
exit
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
bin/magento c:c
exit
bin/magento setup:uprade
bin/magento setup:upgrade
composer install --ignore-platform-reqs
bin/magento setup:upgrade
bin/magento setup:upgrade
bin/magento setup:upgrade
bin/magento setup:upgrade
bin/magento setup:upgrade
exit
cat var/log/system.log
exit
bin/magento c:c
composer install --ignore-platform-reqs
bin/magento c:c
bin/magento setup:upgrade
exit
cat var/log/system.log
cat var/log/system.log
cat var/log/system.log
rm var/log/*.log
cat var/log/system.log
bin/magento setup:upgrade
exit
composer install --ignore-platform-reqs
bin/magento setup:upgrade
exit
bin/magento setup:upgrade
exit
bin/magento c:c
bin/magento setup:upgrade
exit
./vendor/bin/cache-clean.js --watch
bin/magento c:c
bin/magento c:c
n98-magerun2 sys:cron:run set_colis_id
exit
bin/magento setup:upgrade
bin/magento setup:upgrade
df
ls
bin/magento setup:upgrade
exit
bin/magento setup:upgrade 
bin/magento setup:upgrade 
bin/magento setup:upgrade 
bin/magento c:c
rm -fr vendor/
composer install --ignore-platform-reqs
bin/magento c:c
bin/magento cache:status
bin/magento setup:upgrade 
bin/magento cache:clean
bin/magento cache:clean
bin/magento cache:clean
./vendor/bin/cache-clean.js --watch
vi var/log/system.log
cat var/log/system.log
rm var/log/system.log
./vendor/bin/cache-clean.js --watch
bin/magento cache:clean
bin/magento cache:clean
./vendor/bin/cache-clean.js --watch
bin/magento cache:clean
./vendor/bin/cache-clean.js --watch
bin/magento cache:clean
./vendor/bin/cache-clean.js --watch
bin/magento cache:clean
bin/magento cache:clean
exit
grep -i slide vendor/magestore/bannerslider-magento2/Controller/Adminhtml/AbstractAction.php 
composer update --ignore-platform-reqs
grep -i slide vendor/magestore/bannerslider-magento2/Controller/Adminhtml/AbstractAction.php 
exit
bin/magento c:c
bin/magento setup:upgrade
exit
bin/magento c:c
bin/magento c:c
bin/magento c:c
exit
exit
composer install --ignore-platform-reqs
mv patches/composer/magestore-bannerslider-property.diff old
composer install --ignore-platform-reqs
grep slider vendor/magestore/banners7lider-magento2/Controller/Adminhtml/AbstractAction.php 
exit
exit
bin/magento setup:upgrade
n98-magerun2 sys:cron:run eatlf_generate_bl
n98-magerun2 sys:cron:run eatlf_generate_bl
cd pub
cd media
ls
cd bl
ls -altr
cd LILLE_EATLF/
ls -altr
cd bl
ls -altr
cd EATLILLE3
ls -altr
cd
cd var
cd log
ls
cat exception.log 
vi system.log
ls
tail system.log
cd
cd pub
cd media
ls
cd bl
ls
cd ready/
ls
cd LILLE_EATLF/
ls
cd bl/
ls
mkdir EATLILLE4
cd
n98-magerun2 sys:cron:run eatlf_generate_bl
cd -
ls -altr
cd EATLILLE4
ls -altr
cd
bin/magento test-bl --order-id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
bin/magento test-bl --order_id 49124
cd var/log
ls -al
vi system.log 
cat system.log
tail exception.log 
tail system.log
bin/magento c:c
cd
bin/magento c:c
exit
bin/magento setup:upgrade
bin/magento customer
bin/magento 
bin/magento cantine:init:referral
exit
bin/magento c:c
bin/magento setup:upgrade
exit
bin/magento c:c
exit

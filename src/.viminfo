# This viminfo file was generated by Vim 8.1.
# You may edit it if you're careful!

# Viminfo version
|1,4

# Value of 'encoding' when this file was written
*encoding=latin1


# hlsearch on (H) or off (h):
~h
# Last Search Pattern:
~MSle0~/patches

# Command Line History (newest to oldest):
:q
|2,0,1680813230,,"q"
:wq!
|2,0,1680813201,,"wq!"

# Search String History (newest to oldest):
?/patches
|2,1,1680813180,47,"patches"

# Expression History (newest to oldest):

# Input Line History (newest to oldest):

# Debug Line History (newest to oldest):

# Registers:
""1	LINE	0
	            "ced/module-wallet": {
|3,1,1,1,1,0,1680813194,"            \"ced/module-wallet\": {"
"2	LINE	0
	            },
|3,0,2,1,1,0,1680813191,"            },"
"3	LINE	0
	                "Fix deprecated Array and string offset access syntax ": "patches/composer/cedwallet.diff"
|3,0,3,1,1,0,1680813190,"                \"Fix deprecated Array and string offset access syntax \": \"patches/composer/cedwallet.diff\""

# File marks:
'0  145  16  ~/composer.json
|4,48,145,16,1680813230,"~/composer.json"
'1  141  16  ~/composer.json
|4,49,141,16,1680813201,"~/composer.json"
'2  141  16  ~/composer.json
|4,50,141,16,1680813201,"~/composer.json"

# Jumplist (newest first):
-'  145  16  ~/composer.json
|4,39,145,16,1680813230,"~/composer.json"
-'  141  16  ~/composer.json
|4,39,141,16,1680813214,"~/composer.json"
-'  141  16  ~/composer.json
|4,39,141,16,1680813201,"~/composer.json"
-'  132  9  ~/composer.json
|4,39,132,9,1680813184,"~/composer.json"
-'  132  9  ~/composer.json
|4,39,132,9,1680813184,"~/composer.json"
-'  17  27  ~/composer.json
|4,39,17,27,1680813183,"~/composer.json"
-'  17  27  ~/composer.json
|4,39,17,27,1680813183,"~/composer.json"
-'  1  0  ~/composer.json
|4,39,1,0,1680813180,"~/composer.json"
-'  1  0  ~/composer.json
|4,39,1,0,1680813180,"~/composer.json"

# History of marks within files (newest to oldest):

> ~/composer.json
	*	1680813229	0
	"	145	16
	.	141	0
	+	141	0

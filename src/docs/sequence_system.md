# Système de Séquence dans EatLf

## Architecture Générale

Le système de séquence est un composant essentiel de la plateforme EatLf qui gère la génération automatique et unique des numéros pour divers types de documents commerciaux.

### Composants Principaux

1. **SequenceCreator**
   - Classe principale de configuration initiale
   - Crée les séquences de base lors de l'installation
   - Gère les préfixes par défaut (LI, PT, RY)
   - Configure les paramètres de base pour chaque type d'entité

2. **SequenceCreatorObserver**
   - Gère la création dynamique de séquences
   - S'active lors de la création d'une nouvelle franchise
   - Adapte les préfixes selon le code du franchise
   - Gère les différents types d'entités (order, invoice, creditmemo, etc.)

3. **FlushColisSequence**
   - Cron job pour la maintenance
   - Nettoie les séquences des colis
   - Tronque les tables de séquence inutilisées
   - Garde le système propre et performant

## Structure des Séquences

### Types d'Entités Supportés

| Type d'Entité | Préfixe | Description |
|---------------|----------|-------------|
| Commande      | [CodeFranchise] | Numéros de commande par franchise |
| Facture       | F[CodeFranchise] | Factures client |
| Avoir         | A[CodeFranchise] | Avoirs client |
| Expédition    | S[CodeFranchise] | Documents d'expédition |
| Colis         | (vide) | Numéros de colis uniques |
| BAP           | B[CodeFranchise] | Bons d'achat prépayés |
| Devis         | D[CodeFranchise] | Documents de devis |

### Configuration des Séquences

```php
class SequenceCreator {
    private $sequenceBuilder;
    private $sequenceConfig;
    
    public function create($setup) {
        // Configuration par défaut
        $connection = $setup->getConnection();
        $connection->delete('sales_sequence_meta');
        $connection->delete('sales_sequence_profile');
        
        // Paramètres configurables
        $defaultStoreIds = [0, 1];
        $defaultFranchiseIds = [1 => "LI", 2 => "PT", 3 => "RY"];
        
        // Configuration des séquences
        foreach ($defaultStoreIds as $storeId) {
            foreach ($defaultFranchiseIds as $franchiseId => $franchisePrefix) {
                foreach ($this->entityPool->getEntities() as $entityType) {
                    $this->sequenceBuilder
                        ->setPrefix($franchisePrefix)
                        ->setSuffix($this->sequenceConfig->get('suffix'))
                        ->setFranchiseId($franchiseId)
                        ->setStartValue($this->sequenceConfig->get('startValue'))
                        ->setStoreId($storeId)
                        ->setStep($this->sequenceConfig->get('step'))
                        ->setWarningValue($this->sequenceConfig->get('warningValue'))
                        ->setMaxValue($this->sequenceConfig->get('maxValue'))
                        ->setEntityType($entityType)
                        ->create();
                }
            }
        }
    }
}
```

## Gestion des Franchises

### Création de Nouvelle Franchise

Lors de la création d'une nouvelle franchise :
1. Un observateur (`SequenceCreatorObserver`) est déclenché
2. Le préfixe est généré automatiquement à partir des deux premières lettres du code franchise
3. Les séquences sont créées pour tous les types d'entités supportés
4. Les paramètres sont configurés selon les valeurs par défaut du système

### Structure des Préfixes

- Préfixe principal : Les deux premières lettres du code franchise
- Préfixe spécifique : Lettre identifiant le type de document (F pour facture, A pour avoir, etc.)
- Exception : Les colis n'ont pas de préfixe spécifique

## Maintenance et Performance

### Cron Job de Maintenance

Le cron job `FlushColisSequence` effectue :
1. Une recherche des tables de séquence inutilisées
2. Un nettoyage des séquences obsolètes
3. Une optimisation de la base de données

```php
class FlushColisSequence {
    public function execute() {
        $sql = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME LIKE 'sequence_colis%' 
                AND TABLE_SCHEMA = :schema";
        
        foreach($connection->fetchAll($sql, ['schema' => $schema]) as $tableName) {
            $connection->truncateTable($tableName['TABLE_NAME']);
        }
    }
}
```

## Sécurité et Intégrité

- Chaque franchise a ses propres séquences indépendantes
- Les numéros sont uniques par franchise et par type d'entité
- Les séquences sont gérées au niveau de la base de données
- Les valeurs sont incrémentées de manière atomique

## Optimisations

1. **Initialisation Optimisée**
   - Suppression des anciennes séquences avant création
   - Regroupement des opérations par franchise
   - Configuration en batch pour les différents types d'entités

2. **Maintenance Automatique**
   - Nettoyage régulier des séquences inutilisées
   - Optimisation automatique des tables
   - Prévention des conflits de séquence

## Considérations pour l'Évolution

1. **Nouveau Type d'Entité**
   - Ajouter le type dans `entityPool->getEntities()`
   - Configurer le préfixe approprié
   - Mettre à jour les règles de génération

2. **Nouvelle Franchise**
   - Générer automatiquement le préfixe
   - Créer les séquences nécessaires
   - Configurer les paramètres par défaut

3. **Migration**
   - Sauvegarder les séquences existantes
   - Mettre à jour les configurations
   - Assurer la continuité des numéros

## Système des Bons à Payer (BAP)

### Structure des BAP

Les bons à payer sont des documents financiers spécifiques qui suivent une structure particulière :

| Champ | Description | Type |
|-------|-------------|------|
| bap_id | Identifiant unique du BAP | int |
| increment_id | Numéro incrémental unique | string |
| month | Mois de contribution | int |
| year | Année de contribution | int |
| contribution_id | ID de la contribution | int |
| total_excl_tax | Total hors taxe | float |
| total_incl_tax | Total TTC | float |
| order_total_incl_tax | Total des commandes TTC | float |
| tax_details | Détails des taxes | array |
| tax_base | Base taxable | float |
| payment_method | Méthode de paiement | string |
| payment_number | Numéro de paiement | string |
| payment_date | Date de paiement | string |

### Processus de Création

1. **Extraction des Données**
   - Récupération de la date de livraison
   - Extraction du mois et de l'année
   - Validation de la date

2. **Création du BAP**
   - Génération de l'ID unique
   - Calcul des totaux (hors taxe et TTC)
   - Génération de l'identifiant incrémental
   - Création des détails fiscaux

3. **Validation**
   - Vérification de l'existence du BAP
   - Validation des données financières
   - Vérification de la cohérence des dates

### États et Transitions

1. **Nouveau BAP**
   - Création initiale
   - Attribution du numéro de séquence
   - Calcul des montants

2. **En Attente de Paiement**
   - BAP généré mais non payé
   - Attente de la validation du paiement
   - Possibilité d'annulation

3. **Paiement Validé**
   - Paiement reçu et vérifié
   - Mise à jour du statut
   - Archivage du BAP

4. **Annulation**
   - Processus d'annulation
   - Remboursement si nécessaire
   - Mise à jour des statuts associés

### Gestion des Taxes

- Calcul automatique des taxes
- Génération des détails fiscaux
- Mise à jour des bases taxables
- Validation des montants

### Sécurité et Validation

1. **Validation des Données**
   - Contrôle des dates
   - Vérification des montants
   - Validation des statuts

2. **Gestion des Exceptions**
   - Gestion des erreurs de date
   - Gestion des erreurs de paiement
   - Gestion des erreurs de statut

3. **Locking**
   - Système de verrouillage des BAP
   - Prévention des doublons
   - Garantie de l'intégrité

## Améliorations Potentielles et Aspects Manquants

### 1. Gestion des Erreurs et Validation

- **Améliorations Possibles**
  - Créer des exceptions plus spécifiques pour chaque type d'erreur
  - Implémenter un système de logging dédié
  - Ajouter des validations des paramètres de configuration
  - Mettre en place des tests unitaires pour la validation

- **Code à Améliorer**
```php
// Exemple d'amélioration des exceptions
abstract class BapNewInvoiceException extends \Exception
{
    public const INVALID_DATE = 'invalid_date';
    public const DUPLICATE_INVOICE = 'duplicate_invoice';
    public const INVALID_STATUS = 'invalid_status';
}
```

### 2. Configuration

- **Points à Améliorer**
  - Externaliser les valeurs par défaut dans un fichier de configuration
  - Ajouter une validation des configurations
  - Mettre en place un système de configuration par franchise
  - Ajouter des tests de configuration

### 3. Performance

- **Optimisations Possibles**
  - Utiliser des transactions pour les opérations massives
  - Implémenter un système de pagination
  - Ajouter un cache pour les séquences fréquemment utilisées
  - Optimiser les requêtes SQL

### 4. Sécurité

- **Points à Renforcer**
  - Ajouter une validation des préfixes personnalisés
  - Implémenter un système de droits d'accès
  - Prévenir les collisions de séquences
  - Ajouter des tests de sécurité

### 5. Monitoring

- **Nouvelles Métriques à Ajouter**
  - Utilisation des séquences
  - Temps de génération des numéros
  - Erreurs rencontrées
  - Performance du système

### 6. Maintenabilité

- **Améliorations Techniques**
  - Réduire le couplage avec Magento
  - Ajouter des tests unitaires
  - Documenter le code
  - Mettre en place une architecture plus modulaire

### 7. Évolutivité

- **Points à Améliorer**
  - Faciliter l'ajout de nouveaux types d'entités
  - Ajouter un système de migration
  - Rendre la génération des préfixes plus flexible
  - Préparer l'évolution future

## Conclusion

Le système de séquence d'EatLf, bien que fonctionnel, pourrait bénéficier de plusieurs améliorations pour devenir plus robuste, performant et maintenable. Ces améliorations permettraient de mieux gérer les futurs besoins du système tout en assurant la qualité et la fiabilité du code.
